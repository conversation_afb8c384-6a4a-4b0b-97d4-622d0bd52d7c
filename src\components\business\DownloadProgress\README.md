# DownloadProgress 下载进度组件

## 组件说明

DownloadProgress 组件用于显示文件下载进度，提供实时进度条、百分比显示、文件名和取消功能，提升用户体验。

## 组件目录
- 组件文件：`src/components/business/DownloadProgress/index.vue`

## Props
| 属性名         | 类型    | 说明                       | 必填 | 默认值 |
| -------------- | ------- | -------------------------- | ---- | ------ |
| visible        | boolean | 是否显示下载进度弹窗       | 是   | false  |
| progress       | number  | 下载进度百分比 (0-100)     | 是   | 0      |
| fileName       | string  | 下载文件名                 | 否   | '视频文件' |
| status         | string  | 下载状态                   | 否   | 'downloading' |

### status 可选值
- `downloading`: 下载中
- `completed`: 下载完成
- `failed`: 下载失败
- `cancelled`: 下载已取消

## Emits
| 事件名        | 说明                   |
| ------------- | ---------------------- |
| cancel        | 点击取消按钮时触发     |

## 用法示例
```vue
<template>
  <DownloadProgress
    :visible="showDownloadProgress"
    :progress="downloadProgress"
    :file-name="fileName"
    :status="downloadStatus"
    @cancel="handleCancelDownload"
  />
</template>

<script setup>
import { ref } from 'vue'
import DownloadProgress from '@/components/business/DownloadProgress/index.vue'

const showDownloadProgress = ref(false)
const downloadProgress = ref(0)
const fileName = ref('我的视频.mp4')
const downloadStatus = ref('downloading')

const handleCancelDownload = () => {
  showDownloadProgress.value = false
  // 取消下载逻辑
}
</script>
```

## 注意事项
- 组件使用固定定位，会覆盖整个屏幕
- 点击遮罩不会关闭弹窗，防止误操作
- 进度条使用渐变色，与项目主题色保持一致
- 支持实时更新进度，提供流畅的用户体验 