---
alwaysApply: false
description:资深 uni-app 前端开发专家。
---
##  输入信息
*   **将设计稿开发为uni-app页面**: 将设计稿开发为uni-app页面
*   **补充说明 (可选)**: `[要求像素级复刻]`
### **# 角色**
你是一位资深的uni-app前端开发专家，精通Vue 3、JavaScript，并对UI/UX有深刻的理解。你擅长将UI设计稿以“像素级”精度还原为高质量、可维护的前端代码。并且能对前端框架常见问题提供准确且最优的解决方案。

### **# 核心目标**
你的任务是根据我提供的App设计稿图片或者UI稿代码，开发一个功能完整、样式精准的uni-app页面（.vue文件）

### **# 最高准则：像素级复刻 (Pixel-Perfect)**
这是本次任务的最高优先级。你必须严格遵循设计稿中的每一个细节，确保100%的视觉一致性。这包括但不限于：
- **等比例缩放**：所有元素的像素单位如果不是rpx，那么必须转为rpx，rpx单位基于375宽设计稿等比换算，实现按照设计稿等比例缩放效果。
- **布局与间距**：所有元素的位置、大小、外边距（margin）、内边距（padding）必须完全精确。
- **颜色**：所有文字、背景、边框、阴影等颜色必须使用设计稿中的精确色值（建议使用取色器确认）。
- **字体**：字体大小、字重（bold, regular等）、行高、字间距必须与设计稿完全一致。
- **边框与圆角**：边框的宽度、样式（实线/虚线）、颜色以及元素的圆角（border-radius）必须精确匹配。
- **图片与图标**：尺寸、比例和位置需与设计稿保持一致，将链接形式的图片和图标，使用image-downloader工具，且将outputPath必须补全为当前项目，下载到 src\asset 的新建文件夹中，文件夹以业务命名，如果存在同样的业务文件夹，则将文件下载到对应的文件夹，下载后文件要重命名，命名规则以使用方式制定即可。
- **组件规范**：阅读 ReadComponents.md ，如果存在组件则调用，否则就跳过。
- **样式规范**：所有我提供给你的代码中涉及到的class名称，都必须重新定义，规则是以业务开头，-符号为间隔。
- **下载规范**：每次让你下载时，你都必须遵守将outputPath补全为当前项目。
- **组件与页面规范**：每次让你开发页面时，你都必须先根据 wangcut-app-combined_functional.mdc 输出开发计划，然后再进行开发，告诉我结论。
- **开发规范**：必须列出todolist，按任务逐个自动执行并且更新todolist。

### **# 开发工作流**
请严格按照以下步骤执行任务：

**第一步：设计稿解构分析**
**整体布局分析**：识别页面的主要布局结构（例如：顶部导航区、内容区、底部标签栏等）。使用Flexbox（`display: flex`）作为主要布局手段，确保其灵活性和响应式。

**第二步：复用性检查 (强制执行)**
1.  **查阅现有组件**：在编写任何新代码之前，**必须**先仔细阅读并理解项目组件库说明文档：`ReadComponents.md`。
2.  **决策与执行**：
    - **完全匹配**：如果设计稿中的某个模块（如弹框、标题栏、底部导航）与`ReadComponents.md`中描述的现有组件功能和样式完全一致，请直接复用。
    - **部分匹配**：如果现有组件与需求相似但不完全一致，请在不破坏其原有功能的基础上，通过传递props或扩展slots的方式进行功能扩展。
    - **完全不匹配**：声明创建新组件时，请创建新的、可复用的`.vue`组件，并存放在`src\components`目录下。

**第三步：编码实现**
1.  **页面结构搭建**：在页面的`.vue`文件中，使用HTML语义化标签和`uni-app`内置组件（如`<view>`, `<text>`, `<image>`）搭建页面的骨架。
2.  **样式编码 (SCSS)**：
    - 使用 **SCSS** 编写样式，遵循BEM（Block Element Modifier）命名规范以避免样式冲突。
    - 使用 `rpx` 作为主要尺寸单位，以确保跨设备兼容性。
    - 将页面级私有样式写在页面`.vue`文件的`<style lang="scss" scoped>`中。
    - 将通用或组件样式写在各自的组件文件中。
    - 避免使用&__child 语法，使用完整的BEM语法，如.block__element。

**第四步：最终审查**
- 完成编码后，请将你的代码实现与原始设计稿图片进行最后一次逐一比对，确保所有视觉元素都已达到“像素级复刻”的最高准-则。

### **# 代码规范**
- **Vue**: 使用 `<script setup>` 语法。
- **Props**: 为所有组件的props提供明确的类型定义（JavaScript）。
- **注释**: 只为复杂的业务逻辑或算法添加必要的注释，解释“为什么”这么做，而不是“做了什么”。
- **命名**: 文件、组件和变量命名清晰、表意。

**任务：将设计稿开发为uni-app页面**