# HomeMoreFunctions 首页更多功能组件

## 组件描述

HomeMoreFunctions 是一个专门为首页设计的更多功能展示组件，采用网格布局展示功能入口。支持自定义功能列表和国际化文本。

## 功能特性

- 🎯 **网格布局** - 3列网格布局，整齐美观
- 🎨 **图标展示** - 支持 emoji 图标和自定义图标
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🔄 **交互事件** - 支持点击跳转和自定义事件
- 🌍 **国际化支持** - 内置国际化文本支持

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '' | 组件标题 |
| functionList | Array | [] | 功能列表，为空时使用默认配置 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| item-click | { item, index } | 功能项点击时触发 |

## 数据结构

### functionList 数据格式

```javascript
[
  {
    title: '功能标题',           // 必填：功能标题
    icon: '👤',                 // 必填：功能图标（emoji）
    link: '/pages/feature/index' // 可选：跳转链接
  }
]
```

## 默认功能配置

当 `functionList` 为空时，组件会使用以下默认配置：

1. **账户管理** - `index.accountManage`
2. **任务分发** - `index.distributionTask`
3. **私信管理** - `index.privateMessage`
4. **评论管理** - `index.commentManage`
5. **使用教程** - `index.tutorial`
6. **数据统计** - `index.dataStats`

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <HomeMoreFunctions 
      title="更多功能"
      @item-click="handleItemClick"
    />
  </view>
</template>

<script setup>
import HomeMoreFunctions from '@/components/business/HomeMoreFunctions/index.vue'

const handleItemClick = ({ item, index }) => {
  console.log('点击功能:', item.title, index)
}
</script>
```

### 自定义功能列表

```vue
<template>
  <view>
    <HomeMoreFunctions 
      title="特色功能"
      :function-list="customFunctionList"
      @item-click="handleItemClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import HomeMoreFunctions from '@/components/business/HomeMoreFunctions/index.vue'

const customFunctionList = ref([
  {
    title: 'AI 配音',
    icon: '🎤',
    link: '/pages/ai-voice/index'
  },
  {
    title: '背景音乐',
    icon: '🎵',
    link: '/pages/bgm-selector/index'
  },
  {
    title: '视频模板',
    icon: '🎬',
    link: '/pages/video-templates/index'
  },
  {
    title: '数据分析',
    icon: '📊',
    link: '/pages/analytics/index'
  },
  {
    title: '用户反馈',
    icon: '💬',
    link: '/pages/feedback/index'
  },
  {
    title: '设置中心',
    icon: '⚙️',
    link: '/pages/settings/index'
  }
])

const handleItemClick = ({ item, index }) => {
  console.log('点击功能:', item.title)
  
  if (item.link) {
    uni.navigateTo({
      url: item.link,
      fail: (err) => {
        console.error('页面跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  }
}
</script>
```

### 国际化支持

```vue
<template>
  <view>
    <HomeMoreFunctions 
      :title="$t('index.moreFeatures')"
      @item-click="handleItemClick"
    />
  </view>
</template>

<script setup>
import HomeMoreFunctions from '@/components/business/HomeMoreFunctions/index.vue'

const handleItemClick = ({ item, index }) => {
  console.log('点击功能:', item.title)
}
</script>
```

### 动态功能列表

```vue
<template>
  <view>
    <HomeMoreFunctions 
      :title="sectionTitle"
      :function-list="dynamicFunctionList"
      @item-click="handleItemClick"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import HomeMoreFunctions from '@/components/business/HomeMoreFunctions/index.vue'

const userType = ref('vip') // 用户类型：vip, normal, guest

const sectionTitle = computed(() => {
  switch (userType.value) {
    case 'vip':
      return 'VIP 专属功能'
    case 'normal':
      return '更多功能'
    default:
      return '基础功能'
  }
})

const dynamicFunctionList = computed(() => {
  const baseFunctions = [
    { title: '基础功能1', icon: '📱', link: '/pages/basic1/index' },
    { title: '基础功能2', icon: '📱', link: '/pages/basic2/index' }
  ]
  
  const vipFunctions = [
    { title: 'VIP 功能1', icon: '👑', link: '/pages/vip1/index' },
    { title: 'VIP 功能2', icon: '👑', link: '/pages/vip2/index' }
  ]
  
  switch (userType.value) {
    case 'vip':
      return [...baseFunctions, ...vipFunctions]
    case 'normal':
      return baseFunctions
    default:
      return baseFunctions.slice(0, 1)
  }
})

const handleItemClick = ({ item, index }) => {
  if (userType.value !== 'vip' && item.title.includes('VIP')) {
    uni.showModal({
      title: '提示',
      content: '此功能需要 VIP 会员',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({ url: '/pages/vip/index' })
        }
      }
    })
    return
  }
  
  console.log('点击功能:', item.title)
}
</script>
```

## 样式定制

组件使用 SCSS 编写，主要样式特点：

- 网格布局：3列等宽布局
- 图标尺寸：`100rpx`（小屏幕 `90rpx`）
- 图标背景：半透明白色，毛玻璃效果
- 文字大小：`24rpx`（小屏幕 `22rpx`）
- 间距：`50rpx` 垂直间距，`40rpx` 水平间距

## 响应式设计

组件支持响应式设计，在小屏幕设备上会自动调整：

- 图标尺寸从 `100rpx` 调整为 `90rpx`
- 文字大小从 `24rpx` 调整为 `22rpx`
- 网格间距相应调整

## 国际化配置

组件内置了以下国际化 key：

```javascript
{
  "index": {
    "moreFeatures": "更多功能",
    "accountManage": "账户管理",
    "distributionTask": "任务分发",
    "privateMessage": "私信管理",
    "commentManage": "评论管理",
    "tutorial": "使用教程",
    "dataStats": "数据统计"
  }
}
```

## 注意事项

1. 组件会自动处理页面跳转，使用 `uni.navigateTo`
2. 图标建议使用 emoji，确保跨平台兼容性
3. 功能列表会自动填充到 3 的倍数，保持布局整齐
4. 组件依赖国际化配置，确保 `$t` 函数可用
5. 建议功能数量为 3 的倍数，以获得最佳视觉效果

## 最佳实践

1. **图标选择**：使用统一的 emoji 风格，保持视觉一致性
2. **功能分类**：根据用户权限动态显示不同功能
3. **文案简洁**：功能标题保持简洁明了
4. **跳转逻辑**：确保跳转链接的有效性
5. **用户体验**：为 VIP 功能添加权限检查

## 与其他组件配合

HomeMoreFunctions 通常与以下组件配合使用：

- **HomeBanner**: 首页轮播
- **FunctionGrid**: 功能网格
- **FunctionCard**: 功能卡片

形成完整的首页功能展示体系。 