<template>
  <view class="video-player-demo">
    <!-- 状态栏占位 -->
    <view class="video-player-demo__status-bar"></view>
    
    <!-- 演示内容 -->
    <view class="video-player-demo__content">
      <view class="video-player-demo__header">
        <text class="video-player-demo__title">视频播放器组件演示</text>
      </view>
      
      <view class="video-player-demo__section">
        <text class="video-player-demo__section-title">基础功能</text>
        <view class="video-player-demo__buttons">
          <view class="video-player-demo__btn" @click="showVideoPlayer">
            <text class="video-player-demo__btn-text">播放示例视频</text>
          </view>
          <view class="video-player-demo__btn" @click="showVideoPlayerWithPoster">
            <text class="video-player-demo__btn-text">播放带封面视频</text>
          </view>
        </view>
      </view>
      
      <view class="video-player-demo__section">
        <text class="video-player-demo__section-title">高级功能</text>
        <view class="video-player-demo__buttons">
          <view class="video-player-demo__btn" @click="showVideoPlayerAutoPlay">
            <text class="video-player-demo__btn-text">自动播放</text>
          </view>
          <view class="video-player-demo__btn" @click="showVideoPlayerLoop">
            <text class="video-player-demo__btn-text">循环播放</text>
          </view>
        </view>
      </view>
      
      <view class="video-player-demo__section">
        <text class="video-player-demo__section-title">弹窗测试</text>
        <view class="video-player-demo__buttons">
          <view class="video-player-demo__btn video-player-demo__btn--test" @click="testModalWithVideo">
            <text class="video-player-demo__btn-text">测试弹窗功能</text>
          </view>
          <view class="video-player-demo__btn video-player-demo__btn--test" @click="testModalWithoutVideo">
            <text class="video-player-demo__btn-text">仅测试弹窗</text>
          </view>
        </view>
      </view>
      
      <view class="video-player-demo__section">
        <text class="video-player-demo__section-title">事件监听</text>
        <view class="video-player-demo__events">
          <text class="video-player-demo__event-item" v-for="(event, index) in events" :key="index">
            {{ event }}
          </text>
        </view>
      </view>
    </view>
    
    <!-- 视频播放器 -->
    <VideoPlayer
      v-if="isVideoPlayerVisible"
      :video-src="currentVideoSrc"
      :poster="currentPoster"
      :auto-play="autoPlay"
      :loop="loop"
      :muted="muted"
      :has-modal="showDownloadProgress"
      @close="hideVideoPlayer"
      @play="onPlay"
      @pause="onPause"
      @ended="onEnded"
      @timeupdate="onTimeUpdate"
      @fullscreen="onFullscreen"
      @export="onExport"
    />
    
    <!-- 下载进度弹窗 -->
    <DownloadProgress
      :visible="showDownloadProgress"
      :progress="downloadProgress"
      :file-name="downloadFileName"
      :status="downloadStatus"
      @cancel="handleCancelDownload"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import VideoPlayer from '../VideoPlayer/index.vue'
import DownloadProgress from '../DownloadProgress/index.vue'

// 响应式数据
const isVideoPlayerVisible = ref(false)
const currentVideoSrc = ref('')
const currentPoster = ref('')
const autoPlay = ref(false)
const loop = ref(false)
const muted = ref(false)
const events = ref([])

// 下载进度相关
const showDownloadProgress = ref(false)
const downloadProgress = ref(0)
const downloadFileName = ref('')
const downloadStatus = ref('downloading')
const downloadTimer = ref(null)

// 示例视频源
const videoSources = {
  basic: 'https://www.w3schools.com/html/mov_bbb.mp4',
  withPoster: 'https://www.w3schools.com/html/mov_bbb.mp4',
  poster: 'https://www.w3schools.com/html/pic_mountain.jpg'
}

// 方法
const addEvent = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  events.value.unshift(`[${timestamp}] ${message}`)
  if (events.value.length > 10) {
    events.value.pop()
  }
}

const showVideoPlayer = () => {
  currentVideoSrc.value = videoSources.basic
  currentPoster.value = ''
  autoPlay.value = false
  loop.value = false
  muted.value = false
  isVideoPlayerVisible.value = true
  addEvent('打开基础视频播放器')
}

const showVideoPlayerWithPoster = () => {
  currentVideoSrc.value = videoSources.withPoster
  currentPoster.value = videoSources.poster
  autoPlay.value = false
  loop.value = false
  muted.value = false
  isVideoPlayerVisible.value = true
  addEvent('打开带封面视频播放器')
}

const showVideoPlayerAutoPlay = () => {
  currentVideoSrc.value = videoSources.basic
  currentPoster.value = ''
  autoPlay.value = true
  loop.value = false
  muted.value = false
  isVideoPlayerVisible.value = true
  addEvent('打开自动播放视频播放器')
}

const showVideoPlayerLoop = () => {
  currentVideoSrc.value = videoSources.basic
  currentPoster.value = ''
  autoPlay.value = false
  loop.value = true
  muted.value = false
  isVideoPlayerVisible.value = true
  addEvent('打开循环播放视频播放器')
}

const hideVideoPlayer = () => {
  isVideoPlayerVisible.value = false
  addEvent('关闭视频播放器')
}

// 弹窗测试功能
const testModalWithVideo = () => {
  // 先打开视频播放器
  currentVideoSrc.value = videoSources.basic
  currentPoster.value = ''
  autoPlay.value = true
  loop.value = false
  muted.value = false
  isVideoPlayerVisible.value = true
  addEvent('打开视频播放器并准备测试弹窗')
  
  // 延迟2秒后显示弹窗
  setTimeout(() => {
    showDownloadProgress.value = true
    downloadFileName.value = '测试视频.mp4'
    downloadProgress.value = 0
    downloadStatus.value = 'downloading'
    addEvent('显示下载进度弹窗')
    
    // 模拟下载进度
    downloadTimer.value = setInterval(() => {
      if (downloadProgress.value < 100) {
        downloadProgress.value += 10
        addEvent(`下载进度: ${downloadProgress.value}%`)
      } else {
        clearInterval(downloadTimer.value)
                        downloadStatus.value = 'completed'
                        addEvent('下载完成')
                        setTimeout(() => {
                          showDownloadProgress.value = false
                          addEvent('弹窗关闭，视频应自动恢复播放')
                        }, 1000)
                      }
                    }, 500)
                  }, 2000)
                }

const testModalWithoutVideo = () => {
  // 仅测试弹窗功能
  showDownloadProgress.value = true
  downloadFileName.value = '测试文件.mp4'
  downloadProgress.value = 0
  downloadStatus.value = 'downloading'
  addEvent('仅显示下载进度弹窗')
  
  // 模拟下载进度
  downloadTimer.value = setInterval(() => {
    if (downloadProgress.value < 100) {
      downloadProgress.value += 10
      addEvent(`下载进度: ${downloadProgress.value}%`)
    } else {
      clearInterval(downloadTimer.value)
      downloadStatus.value = 'completed'
      addEvent('下载完成')
      setTimeout(() => {
        showDownloadProgress.value = false
        addEvent('弹窗关闭')
      }, 1000)
    }
  }, 500)
}

const handleCancelDownload = () => {
  if (downloadTimer.value) {
    clearInterval(downloadTimer.value)
    downloadTimer.value = null
  }
  downloadStatus.value = 'cancelled'
  addEvent('取消下载')
  setTimeout(() => {
    showDownloadProgress.value = false
    addEvent('弹窗关闭')
  }, 1000)
}

const onPlay = () => {
  addEvent('视频开始播放')
}

const onPause = () => {
  addEvent('视频暂停播放')
}

const onEnded = () => {
  addEvent('视频播放结束')
}

const onTimeUpdate = ({ current, total }) => {
  // 避免频繁更新事件列表
  if (Math.floor(current) % 5 === 0) {
    addEvent(`播放进度: ${Math.floor(current)}s / ${Math.floor(total)}s`)
  }
}

const onFullscreen = (isFullscreen) => {
  addEvent(`全屏状态: ${isFullscreen ? '开启' : '关闭'}`)
}

const onExport = () => {
  addEvent('点击导出按钮')
  // 模拟导出功能
  showDownloadProgress.value = true
  downloadFileName.value = '导出视频.mp4'
  downloadProgress.value = 0
  downloadStatus.value = 'downloading'
  
  // 模拟下载进度
  downloadTimer.value = setInterval(() => {
    if (downloadProgress.value < 100) {
      downloadProgress.value += 10
    } else {
      clearInterval(downloadTimer.value)
      downloadStatus.value = 'completed'
      setTimeout(() => {
        showDownloadProgress.value = false
        addEvent('导出完成，弹窗关闭')
      }, 1000)
    }
  }, 300)
}
</script>

<style lang="scss" scoped>
.video-player-demo {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  &__status-bar {
    height: var(--status-bar-height);
    width: 100%;
  }

  &__content {
    flex: 1;
    padding: 24rpx;
  }

  &__header {
    margin-bottom: 48rpx;
  }

  &__title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }

  &__section {
    margin-bottom: 48rpx;
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
  }

  &__section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 24rpx;
  }

  &__buttons {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }

  &__btn {
    background: #007aff;
    border-radius: 12rpx;
    padding: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &--test {
      background: #ff6b6b;
    }
  }

  &__btn-text {
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
  }

  &__events {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
  }

  &__event-item {
    font-size: 24rpx;
    color: #666;
    padding: 8rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
  }
}
</style>