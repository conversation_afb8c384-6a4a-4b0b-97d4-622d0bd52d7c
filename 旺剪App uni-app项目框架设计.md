# 旺剪App uni-app项目框架设计

## 项目目录结构

```
wangcut-app/
├── src/
│   ├── components/                    # 组件库
│   │   ├── common/                   # 通用组件
│   │   │   ├── Button/
│   │   │   │   ├── index.vue
│   │   │   │   ├── Button.md         # 使用说明
│   │   │   │   └── demo.vue          # 示例组件
│   │   │   ├── Modal/
│   │   │   ├── Loading/
│   │   │   ├── Toast/
│   │   │   ├── TabBar/
│   │   │   └── index.js              # 统一导出
│   │   ├── business/                 # 业务组件
│   │   │   ├── VideoPlayer/
│   │   │   ├── VideoEditor/
│   │   │   ├── AudioRecorder/
│   │   │   ├── TemplateSelector/
│   │   │   ├── AITextGenerator/
│   │   │   └── index.js
│   │   ├── layout/                   # 布局组件
│   │   │   ├── Header/
│   │   │   ├── NavBar/
│   │   │   └── PageContainer/
│   │   └── demo/                     # 组件演示页面
│   │       ├── index.vue             # 组件集合页
│   │       ├── common/
│   │       │   ├── button-demo.vue
│   │       │   └── modal-demo.vue
│   │       └── business/
│   │           ├── video-player-demo.vue
│   │           └── video-editor-demo.vue
│   ├── pages/                        # 页面
│   │   ├── index/                    # 首页
│   │   │   └── index.vue
│   │   ├── create/                   # 创作页面
│   │   │   ├── index.vue
│   │   │   ├── template.vue
│   │   │   ├── ai-mix.vue
│   │   │   └── ai-version.vue
│   │   ├── history/                  # 创作历史
│   │   │   └── index.vue
│   │   ├── trending/                 # 热门/爆款
│   │   │   └── index.vue
│   │   ├── profile/                  # 个人中心
│   │   │   └── index.vue
│   │   └── demo/                     # 组件演示页面（仅开发环境）
│   │       └── index.vue
│   ├── api/                          # API接口
│   │   ├── index.js                  # API统一配置
│   │   ├── request.js                # 请求封装
│   │   ├── modules/
│   │   │   ├── video.js              # 视频相关API
│   │   │   ├── ai.js                 # AI相关API
│   │   │   ├── user.js               # 用户相关API
│   │   │   └── template.js           # 模板相关API
│   │   └── mock/                     # Mock数据
│   │       ├── video.js
│   │       └── user.js
│   ├── store/                        # 状态管理
│   │   ├── index.js                  # Pinia配置
│   │   ├── modules/
│   │   │   ├── user.js               # 用户状态
│   │   │   ├── video.js              # 视频状态
│   │   │   ├── app.js                # 应用状态
│   │   │   └── create.js             # 创作状态
│   │   └── types.js                  # 状态类型定义
│   ├── utils/                        # 工具类
│   │   ├── index.js                  # 通用工具
│   │   ├── request.js                # 请求工具
│   │   ├── storage.js                # 存储工具
│   │   ├── media.js                  # 媒体处理工具
│   │   ├── validate.js               # 验证工具
│   │   ├── format.js                 # 格式化工具
│   │   └── permission.js             # 权限工具
│   ├── mixins/                       # 混入
│   │   ├── page.js                   # 页面通用混入
│   │   ├── auth.js                   # 权限混入
│   │   └── share.js                  # 分享混入
│   ├── styles/                       # 样式
│   │   ├── variables.scss            # 变量
│   │   ├── mixins.scss               # 混入
│   │   ├── common.scss               # 通用样式
│   │   ├── components.scss           # 组件样式
│   │   └── themes/                   # 主题
│   │       ├── dark.scss
│   │       └── light.scss
│   ├── locale/                       # 国际化
│   │   ├── index.js                  # 国际化配置
│   │   ├── zh-CN.js                  # 中文
│   │   ├── en-US.js                  # 英文
│   │   └── ja-JP.js                  # 日文
│   ├── static/                       # 静态资源
│   │   ├── images/
│   │   ├── icons/
│   │   └── fonts/
│   ├── App.vue                       # 应用入口
│   ├── main.js                       # 主入口
│   ├── manifest.json                 # 应用配置
│   ├── pages.json                    # 页面配置
│   └── uni.scss                      # 全局样式
├── tests/                            # 自动化测试（基于uni-app官方推荐）
│   ├── unit/                         # 单元测试
│   │   ├── components/               # 组件测试
│   │   │   ├── common/
│   │   │   │   ├── Button.test.js
│   │   │   │   └── Modal.test.js
│   │   │   └── business/
│   │   │       ├── VideoPlayer.test.js
│   │   │       └── VideoEditor.test.js
│   │   ├── utils/                    # 工具测试
│   │   │   ├── request.test.js
│   │   │   └── storage.test.js
│   │   └── store/                    # 状态测试
│   │       ├── user.test.js
│   │       └── video.test.js
│   ├── integration/                  # 集成测试
│   │   ├── pages/
│   │   │   ├── create-flow.test.js
│   │   │   ├── video-upload.test.js
│   │   │   └── ai-generation.test.js
│   │   └── api/
│   │       ├── video-api.test.js
│   │       └── user-api.test.js
│   ├── e2e/                          # 端到端测试（uni-automator）
│   │   ├── specs/
│   │   │   ├── create.test.js
│   │   │   ├── history.test.js
│   │   │   └── profile.test.js
│   │   └── support/
│   │       ├── commands.js
│   │       └── helpers.js
│   ├── helpers/                      # 测试辅助工具
│   │   ├── uni-test-utils.js         # uni-app测试工具封装
│   │   ├── mock.js                   # Mock工具
│   │   ├── setup.js                  # 测试环境设置
│   │   └── page-helpers.js           # 页面测试辅助
│   ├── config/                       # 测试配置
│   │   ├── jest.config.js            # Jest配置
│   │   ├── uni-automator.config.js   # uni-automator配置
│   │   └── test-env.js               # 测试环境配置
│   └── hbuilderx-extension/          # HBuilderX插件扩展
│       ├── test-runner.js            # 测试运行器
│       ├── coverage-reporter.js      # 覆盖率报告
│       └── plugin-manifest.json      # 插件配置
├── docs/                             # 文档
│   ├── components/                   # 组件文档
│   │   ├── README.md
│   │   ├── common/
│   │   └── business/
│   ├── api/                          # API文档
│   │   └── README.md
│   ├── development/                  # 开发文档
│   │   ├── setup.md
│   │   ├── coding-standards.md
│   │   └── testing.md
│   └── deployment/                   # 部署文档
│       ├── build.md
│       └── release.md
├── scripts/                          # 构建脚本
│   ├── build.js                      # 构建脚本
│   ├── dev.js                        # 开发脚本
│   ├── test.js                       # 测试脚本
│   ├── build-config.js               # 构建配置管理
│   ├── generate-pages-config.js      # 动态生成页面配置
│   └── generate-demo-pages.js        # 生成组件演示页面（仅开发环境）
├── .env                              # 环境变量
├── .env.development                  # 开发环境
├── .env.production                   # 生产环境
├── .gitignore
├── package.json
├── pnpm-lock.yaml
├── vite.config.js
└── README.md
```

## 技术设计架构

### 1. 组件化开发架构

#### 1.1 组件分类
- **通用组件 (Common)**：Button、Modal、Loading、Toast等
- **业务组件 (Business)**：VideoPlayer、VideoEditor、AITextGenerator等
- **布局组件 (Layout)**：Header、NavBar、PageContainer等

#### 1.2 组件开发规范
```javascript
// 组件标准结构
<template>
  <view class="component-name">
    <!-- 组件内容 -->
  </view>
</template>

<script>
export default {
  name: 'ComponentName',
  props: {
    // 属性定义
  },
  emits: ['event-name'],
  setup(props, { emit }) {
    // 组合式API逻辑
  }
}
</script>

<style lang="scss" scoped>
.component-name {
  // 组件样式
}
</style>
```

#### 1.3 组件文档规范
每个组件包含：
- `index.vue` - 组件实现
- `README.md` - 组件说明文档
- `demo.vue` - 使用示例
- `index.test.js` - 单元测试

### 2. 自动化测试架构（基于uni-app官方推荐）

#### 2.1 uni-app官方测试工具栈
- **@dcloudio/uni-automator** - uni-app官方自动化测试工具
- **puppeteer** - 浏览器自动化测试
- **jest** - 单元测试框架（配合uni-automator）
- **@dcloudio/uni-helper-json** - 页面配置测试辅助

#### 2.2 测试配置
```javascript
// jest.config.js
module.exports = {
  preset: '@dcloudio/uni-automator/jest-preset',
  testEnvironment: 'node',
  testTimeout: 30000,
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './test-results',
      filename: 'report.html'
    }]
  ],
  collectCoverageFrom: [
    'src/components/**/*.vue',
    'src/utils/**/*.js',
    'src/store/**/*.js',
    '!src/components/demo/**', // 排除演示组件
    '!src/pages/demo/**'       // 排除演示页面
  ]
}
```

#### 2.3 uni-automator配置
```javascript
// tests/config/uni-automator.config.js
module.exports = {
  // 测试平台配置
  platform: 'h5', // 或 'mp-weixin', 'app-plus'
  
  // 浏览器配置
  headless: process.env.CI === 'true',
  slowMo: 10,
  
  // 页面配置
  pages: {
    index: 'pages/index/index',
    create: 'pages/create/index',
    history: 'pages/history/index'
  },
  
  // 全局配置
  globals: {
    API_BASE_URL: 'http://localhost:3000',
    TEST_MODE: true
  }
}
```

#### 2.4 测试工具类封装
```javascript
// tests/helpers/uni-test-utils.js
const { program } = require('@dcloudio/uni-automator')

class UniTestUtils {
  constructor() {
    this.program = null
  }
  
  // 初始化测试程序
  async setup() {
    this.program = await program({
      cwd: process.cwd(),
      platform: 'h5',
      headless: process.env.CI === 'true'
    })
    return this.program
  }
  
  // 获取页面实例
  async getPage(path) {
    const page = await this.program.navigateTo(`/${path}`)
    await page.waitFor(1000) // 等待页面加载
    return page
  }
  
  // 测试组件渲染
  async testComponent(page, selector, expectedText) {
    const element = await page.$(selector)
    const text = await element.text()
    expect(text).toBe(expectedText)
  }
  
  // 测试用户交互
  async testUserInteraction(page, selector, action = 'tap') {
    const element = await page.$(selector)
    await element[action]()
    await page.waitFor(500) // 等待交互完成
  }
  
  // 测试API调用
  async mockApiResponse(page, url, response) {
    await page.evaluate((url, response) => {
      // 在页面上下文中模拟API响应
      window.__TEST_API_MOCK__ = window.__TEST_API_MOCK__ || {}
      window.__TEST_API_MOCK__[url] = response
    }, url, response)
  }
  
  // 测试页面跳转
  async testNavigation(page, targetPath) {
    await page.waitFor(targetPath)
    const currentPath = await page.path()
    expect(currentPath).toContain(targetPath)
  }
  
  // 清理测试环境
  async teardown() {
    if (this.program) {
      await this.program.close()
    }
  }
}

module.exports = new UniTestUtils()
```

#### 2.5 HBuilderX插件集成
```javascript
// tests/hbuilderx-extension/test-runner.js
const { runTests } = require('@dcloudio/uni-automator')

// HBuilderX插件扩展测试运行器
class TestRunner {
  constructor() {
    this.testResults = []
  }
  
  async runAllTests() {
    const testSuites = [
      'tests/unit/**/*.test.js',
      'tests/integration/**/*.test.js',
      'tests/e2e/**/*.test.js'
    ]
    
    for (const suite of testSuites) {
      const result = await runTests(suite)
      this.testResults.push(result)
    }
    
    return this.generateReport()
  }
  
  generateReport() {
    // 生成测试报告
    const report = {
      total: this.testResults.length,
      passed: this.testResults.filter(r => r.status === 'passed').length,
      failed: this.testResults.filter(r => r.status === 'failed').length,
      coverage: this.calculateCoverage()
    }
    
    return report
  }
  
  calculateCoverage() {
    // 计算代码覆盖率
    return '85%' // 示例
  }
}

module.exports = TestRunner
```

### 3. 国际化支持架构

#### 3.1 国际化配置
```javascript
// src/locale/index.js
import { createI18n } from 'vue-i18n'
import zhCN from './zh-CN'
import enUS from './en-US'
import jaJP from './ja-JP'

const i18n = createI18n({
  locale: uni.getStorageSync('locale') || 'zh-CN',
  fallbackLocale: 'zh-CN',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS,
    'ja-JP': jaJP
  }
})

export default i18n
```

#### 3.2 语言资源文件
```javascript
// src/locale/zh-CN.js
export default {
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除'
  },
  pages: {
    home: {
      title: '首页',
      trending: '热门',
      create: '创作'
    },
    create: {
      title: '视频创作',
      aiMix: 'AI混剪',
      template: '模板视频'
    }
  },
  components: {
    videoPlayer: {
      play: '播放',
      pause: '暂停',
      fullscreen: '全屏'
    }
  }
}
```

### 4. 状态管理架构

#### 4.1 Pinia状态管理
```javascript
// src/store/modules/user.js
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    token: null,
    isLogin: false
  }),
  
  getters: {
    isVip: (state) => state.userInfo?.vip === true
  },
  
  actions: {
    async login(credentials) {
      // 登录逻辑
    },
    
    async logout() {
      // 登出逻辑
    }
  }
})
```

### 5. API接口架构

#### 5.1 请求封装
```javascript
// src/api/request.js
import { useUserStore } from '@/store/modules/user'

class Request {
  constructor(config = {}) {
    this.baseURL = config.baseURL || process.env.VUE_APP_API_BASE_URL
    this.timeout = config.timeout || 10000
  }
  
  request(options) {
    return new Promise((resolve, reject) => {
      const userStore = useUserStore()
      
      uni.request({
        ...options,
        url: this.baseURL + options.url,
        timeout: this.timeout,
        header: {
          'Authorization': `Bearer ${userStore.token}`,
          'Content-Type': 'application/json',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: reject
      })
    })
  }
}

export default new Request()
```

### 6. 构建配置（组件演示系统分离）

#### 6.1 环境配置
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

export default defineConfig(({ command, mode }) => {
  const isProduction = mode === 'production'
  
  return {
    plugins: [uni()],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    define: {
      'process.env.NODE_ENV': JSON.stringify(mode),
      'process.env.DEMO_MODE': JSON.stringify(!isProduction)
    },
    build: {
      rollupOptions: {
        // 生产环境排除演示相关代码
        external: isProduction ? [
          'src/components/demo/**',
          'src/pages/demo/**'
        ] : []
      }
    }
  }
})
```

#### 6.2 组件演示系统构建策略
```javascript
// scripts/build-config.js
const fs = require('fs')
const path = require('path')

class BuildConfig {
  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development'
    this.isProduction = process.env.NODE_ENV === 'production'
  }
  
  // 生成页面配置（生产环境排除演示页面）
  generatePagesConfig() {
    const basePagesConfig = {
      pages: [
        {
          path: 'pages/index/index',
          style: { navigationBarTitleText: 'uni-app' }
        },
        {
          path: 'pages/create/index',
          style: { navigationBarTitleText: '创作' }
        },
        {
          path: 'pages/history/index',
          style: { navigationBarTitleText: '历史记录' }
        },
        {
          path: 'pages/profile/index',
          style: { navigationBarTitleText: '个人中心' }
        }
      ],
      globalStyle: {
        navigationBarTextStyle: 'black',
        navigationBarTitleText: '旺剪',
        navigationBarBackgroundColor: '#F8F8F8',
        backgroundColor: '#F8F8F8'
      },
      tabBar: {
        color: '#7A7E83',
        selectedColor: '#3cc51f',
        borderStyle: 'black',
        backgroundColor: '#ffffff',
        list: [
          {
            pagePath: 'pages/index/index',
            iconPath: 'static/icon_home.png',
            selectedIconPath: 'static/icon_home_selected.png',
            text: '首页'
          },
          {
            pagePath: 'pages/create/index',
            iconPath: 'static/icon_create.png',
            selectedIconPath: 'static/icon_create_selected.png',
            text: '创作'
          },
          {
            pagePath: 'pages/history/index',
            iconPath: 'static/icon_history.png',
            selectedIconPath: 'static/icon_history_selected.png',
            text: '历史'
          },
          {
            pagePath: 'pages/profile/index',
            iconPath: 'static/icon_profile.png',
            selectedIconPath: 'static/icon_profile_selected.png',
            text: '我的'
          }
        ]
      }
    }
    
    // 开发环境添加演示页面
    if (this.isDevelopment) {
      basePagesConfig.pages.push({
        path: 'pages/demo/index',
        style: { 
          navigationBarTitleText: '组件演示',
          navigationBarBackgroundColor: '#4A90E2',
          navigationBarTextStyle: 'white'
        }
      })
      
      // 添加组件演示页面到tabBar
      basePagesConfig.tabBar.list.push({
        pagePath: 'pages/demo/index',
        iconPath: 'static/icon_demo.png',
        selectedIconPath: 'static/icon_demo_selected.png',
        text: '演示'
      })
    }
    
    return basePagesConfig
  }
  
  // 生成组件导出配置
  generateComponentExports() {
    const componentDirs = [
      'src/components/common',
      'src/components/business',
      'src/components/layout'
    ]
    
    let exports = []
    
    componentDirs.forEach(dir => {
      const fullPath = path.join(process.cwd(), dir)
      if (fs.existsSync(fullPath)) {
        const items = fs.readdirSync(fullPath, { withFileTypes: true })
        items.forEach(item => {
          if (item.isDirectory()) {
            const componentName = item.name
            const componentPath = path.join(dir, componentName, 'index.vue')
            
            exports.push({
              name: componentName,
              path: componentPath,
              // 生产环境排除演示相关文件
              demo: this.isDevelopment ? path.join(dir, componentName, 'demo.vue') : null,
              docs: this.isDevelopment ? path.join(dir, componentName, 'README.md') : null
            })
          }
        })
      }
    })
    
    return exports
  }
  
  // 生成演示页面路由（仅开发环境）
  generateDemoRoutes() {
    if (this.isProduction) {
      return []
    }
    
    const components = this.generateComponentExports()
    const routes = []
    
    components.forEach(component => {
      if (component.demo) {
        routes.push({
          path: `/demo/${component.name.toLowerCase()}`,
          name: `${component.name}Demo`,
          component: component.demo,
          meta: {
            title: `${component.name} 演示`,
            category: this.getComponentCategory(component.path)
          }
        })
      }
    })
    
    return routes
  }
  
  getComponentCategory(componentPath) {
    if (componentPath.includes('common')) return 'common'
    if (componentPath.includes('business')) return 'business'
    if (componentPath.includes('layout')) return 'layout'
    return 'other'
  }
}

module.exports = BuildConfig
```

#### 6.3 动态页面配置生成
```javascript
// scripts/generate-pages-config.js
const fs = require('fs')
const path = require('path')
const BuildConfig = require('./build-config')

const buildConfig = new BuildConfig()

// 生成pages.json
const pagesConfig = buildConfig.generatePagesConfig()
const pagesJsonPath = path.join(process.cwd(), 'src/pages.json')

fs.writeFileSync(pagesJsonPath, JSON.stringify(pagesConfig, null, 2))

console.log(`✅ pages.json 生成完成 (${process.env.NODE_ENV} 环境)`)
console.log(`📄 包含页面: ${pagesConfig.pages.length} 个`)

if (process.env.NODE_ENV === 'development') {
  console.log('🔧 开发环境：包含组件演示页面')
} else {
  console.log('🚀 生产环境：已排除组件演示页面')
}
```

#### 6.4 组件演示页面自动生成
```javascript
// scripts/generate-demo-pages.js
const fs = require('fs')
const path = require('path')
const BuildConfig = require('./build-config')

const buildConfig = new BuildConfig()

// 只在开发环境生成演示页面
if (process.env.NODE_ENV === 'development') {
  const components = buildConfig.generateComponentExports()
  const demoRoutes = buildConfig.generateDemoRoutes()
  
  // 生成演示页面索引
  const demoIndexTemplate = `
<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">组件演示</text>
      <text class="demo-subtitle">仅限开发环境</text>
    </view>
    
    <view class="demo-categories">
      <view v-for="category in categories" :key="category.name" class="demo-category">
        <text class="category-title">{{ category.title }}</text>
        <view class="component-list">
          <navigator 
            v-for="component in category.components" 
            :key="component.name"
            :url="component.demoPath"
            class="component-item"
          >
            <text class="component-name">{{ component.name }}</text>
            <text class="component-desc">{{ component.desc }}</text>
          </navigator>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      categories: [
        {
          name: 'common',
          title: '通用组件',
          components: ${JSON.stringify(components.filter(c => c.path.includes('common')), null, 8)}
        },
        {
          name: 'business', 
          title: '业务组件',
          components: ${JSON.stringify(components.filter(c => c.path.includes('business')), null, 8)}
        },
        {
          name: 'layout',
          title: '布局组件', 
          components: ${JSON.stringify(components.filter(c => c.path.includes('layout')), null, 8)}
        }
      ]
    }
  }
}
</script>

<style lang="scss">
.demo-container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .demo-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .demo-subtitle {
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
  }
}

.demo-category {
  margin-bottom: 40rpx;
  
  .category-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .component-list {
    .component-item {
      display: block;
      padding: 20rpx;
      margin-bottom: 20rpx;
      background: white;
      border-radius: 12rpx;
      box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
      
      .component-name {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
      }
      
      .component-desc {
        font-size: 24rpx;
        color: #666;
        margin-top: 10rpx;
      }
    }
  }
}
</style>
  `
  
  // 写入演示页面索引
  const demoIndexPath = path.join(process.cwd(), 'src/pages/demo/index.vue')
  fs.mkdirSync(path.dirname(demoIndexPath), { recursive: true })
  fs.writeFileSync(demoIndexPath, demoIndexTemplate)
  
  console.log('✅ 组件演示页面生成完成')
  console.log(`📦 生成组件演示: ${components.length} 个`)
}
```

### 7. 开发工具配置

#### 7.1 代码规范
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/typescript/recommended',
    'plugin:vue/vue3-essential'
  ],
  rules: {
    'vue/multi-word-component-names': 'off',
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off'
  }
}
```

#### 7.2 Git Hooks
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{js,vue}": ["eslint --fix", "git add"],
    "*.{css,scss}": ["stylelint --fix", "git add"]
  }
}
```

## 核心特性实现

### 1. 基于uni-app官方推荐的自动化测试
- **@dcloudio/uni-automator**：官方自动化测试工具，支持多平台测试
- **集成测试**：页面流程测试、API接口测试
- **E2E测试**：完整用户场景测试
- **HBuilderX插件扩展**：IDE集成的测试运行器

### 2. 智能组件演示系统
- **开发环境**：完整的组件演示系统，包含文档、示例、测试
- **生产环境**：自动排除演示代码，确保发布包纯净
- **动态路由生成**：根据组件目录自动生成演示页面路由
- **分类展示**：按组件类型分类展示，便于查找和测试

### 3. 环境差异化构建
- **开发模式**：包含组件演示、开发工具、Mock数据
- **生产模式**：排除演示代码、优化打包体积、启用压缩
- **测试模式**：集成测试工具、覆盖率报告、持续集成

### 4. 完整的国际化支持
- **多语言切换**：支持中文、英文、日文等多语言
- **本地化适配**：根据系统语言自动切换
- **组件级国际化**：每个组件都支持多语言

### 5. 现代化开发工具链
- **TypeScript支持**：类型安全、智能提示
- **ESLint + Prettier**：代码规范、自动格式化
- **Git Hooks**：提交前自动检查、测试
- **自动化部署**：CI/CD集成、多环境发布

### 6. 测试驱动开发（TDD）
- **单元测试**：组件功能测试
- **集成测试**：页面流程测试
- **E2E测试**：用户场景测试
- **性能测试**：页面加载、交互响应测试

这个架构设计确保了旺剪App在开发效率、代码质量、用户体验方面的最佳平衡，同时严格按照uni-app官方推荐的最佳实践进行实现。