# 原生素材库页面使用说明

## 功能概述

原生素材库页面 (`src/pages/native-gallery/index.nvue`) 提供了本地图片和视频选择功能，支持多选、预览和确认选择。

## 主要功能

### 1. 本地素材选择
- 支持图片和视频多选
- 实时显示选中项预览
- 支持取消选择
- 显示文件时长（视频）

### 2. 数据格式

#### 原生数据格式（来自 qinsilk-gallery 组件）
```javascript
{
  "detail": {
    "selected": [{
      "isSelected": true,
      "path": "/storage/emulated/0/DCIM/Camera/VID_20250112_185653.mp4",
      "id": 29295,
      "mediaType": 3, // 1: 图片, 3: 视频
      "selectedOrder": 1,
      "duration": 81133, // 毫秒
      "durationStr": "00:30"
    }],
    "type": "select"
  }
}
```

#### 转换后的数据格式
```javascript
{
  id: 29295,
  path: "/storage/emulated/0/DCIM/Camera/VID_20250112_185653.mp4",
  mediaType: 3,
  duration: 81133,
  durationStr: "00:30",
  isSelected: true,
  selectedOrder: 1,
  name: "VID_20250112_185653.mp4",
  size: 0
}
```

## 使用方法

### 1. 页面跳转
```javascript
// 跳转到原生素材库页面
uni.navigateTo({
  url: '/pages/native-gallery/index'
});
```

### 2. 获取选中的素材
```javascript
import { getSelectedNativeMaterials } from '@/utils/nativeGallery'

// 获取选中的素材
const selectedMaterials = getSelectedNativeMaterials();
console.log('选中的素材:', selectedMaterials);
```

### 3. 清除选中的素材
```javascript
import { clearSelectedNativeMaterials } from '@/utils/nativeGallery'

// 清除选中的素材
clearSelectedNativeMaterials();
```

### 4. 工具函数

#### 格式化时长
```javascript
import { formatDuration } from '@/utils/nativeGallery'

const duration = formatDuration(81133); // "00:30"
```

#### 获取文件名
```javascript
import { getFileNameFromPath } from '@/utils/nativeGallery'

const fileName = getFileNameFromPath('/path/to/file.jpg'); // "file.jpg"
```

#### 数据格式转换
```javascript
import { convertNativeData } from '@/utils/nativeGallery'

const convertedData = convertNativeData(nativeSelectedItems);
```

## 页面结构

### 顶部导航栏
- 返回按钮
- 标签切换（本地素材、云素材、素材库）

### 内容区域
- 本地素材：qinsilk-gallery 组件
- 云素材：占位内容
- 素材库：MaterialLibrary 组件

### 底部选择区域
- 选中项预览（横向滚动）
- 选择提示
- 确认按钮

## 事件处理

### onGallerySelect
处理原生图片/视频选择事件，自动更新选中项列表。

### confirmSelection
确认选择并返回上一页，选中的素材会保存到本地存储。

## 注意事项

1. **数据存储**：选中的素材会保存到 `uni.setStorageSync('selectedNativeMaterials', selectedItems)`
2. **页面返回**：确认选择后会自动返回上一页
3. **数据清理**：建议在使用完选中的素材后调用 `clearSelectedNativeMaterials()` 清理数据
4. **错误处理**：所有工具函数都包含错误处理，失败时会返回默认值或空数组

## 样式规范

- 使用 BEM 命名规范
- 所有布局容器都使用 flex 布局
- 图片圆角通过包装容器实现
- 响应式设计，适配不同屏幕尺寸 