/**
 * 视频相关接口服务
 */

import http from '@/utils/http'

const videoService = {
  // 上传视频
  uploadVideo(filePath, formData = {}) {
    return http.upload('/api/video/upload', filePath, formData)
  },
  
  // 获取视频列表
  getVideoList(params) {
    return http.get('/api/video/list', params)
  },
  
  // 获取视频详情
  getVideoDetail(id) {
    return http.get(`/api/video/detail/${id}`)
  },
  
  // 创建视频项目
  createVideoProject(data) {
    return http.post('/api/video/project', data)
  },
  
  // 更新视频项目
  updateVideoProject(id, data) {
    return http.put(`/api/video/project/${id}`, data)
  },
  
  // 删除视频项目
  deleteVideoProject(id) {
    return http.delete(`/api/video/project/${id}`)
  },
  
  // 获取用户视频项目列表
  getUserVideoProjects(params) {
    return http.get('/api/video/user-projects', params)
  },
  
  // 复制视频项目
  copyVideoProject(id) {
    return http.post(`/api/video/project/${id}/copy`)
  },
  
  // 分享视频项目
  shareVideoProject(id, data) {
    return http.post(`/api/video/project/${id}/share`, data)
  },
  
  // AI视频生成
  generateAIVideo(data) {
    return http.post('/api/video/ai-generate', data)
  },
  
  // 获取AI生成任务状态
  getAITaskStatus(taskId) {
    return http.get(`/api/video/ai-task/${taskId}`)
  },
  
  // 取消AI生成任务
  cancelAITask(taskId) {
    return http.post(`/api/video/ai-task/${taskId}/cancel`)
  },
  
  // 视频转码
  transcodeVideo(data) {
    return http.post('/api/video/transcode', data)
  },
  
  // 获取转码任务状态
  getTranscodeStatus(taskId) {
    return http.get(`/api/video/transcode/${taskId}`)
  },
  
  // 视频压缩
  compressVideo(data) {
    return http.post('/api/video/compress', data)
  },
  
  // 视频剪辑
  editVideo(data) {
    return http.post('/api/video/edit', data)
  },
  
  // 保存视频编辑配置
  saveEditConfig(data) {
    return http.post('/api/video/edit-config', data)
  },
  
  // 获取视频编辑配置
  getEditConfig(projectId) {
    return http.get(`/api/video/edit-config/${projectId}`)
  },
  
  // 导出视频
  exportVideo(data) {
    return http.post('/api/video/export', data)
  },
  
  // 获取导出任务状态
  getExportStatus(taskId) {
    return http.get(`/api/video/export/${taskId}`)
  },
  
  // 获取视频模板列表
  getTemplateList(params) {
    return http.get('/api/video/templates', params)
  },
  
  // 获取视频模板详情
  getTemplateDetail(id) {
    return http.get(`/api/video/template/${id}`)
  },
  
  // 使用模板创建视频
  createFromTemplate(data) {
    return http.post('/api/video/create-from-template', data)
  },
  
  // 获取热门视频
  getTrendingVideos(params) {
    return http.get('/api/video/trending', params)
  },
  
  // 搜索视频
  searchVideos(params) {
    return http.get('/api/video/search', params)
  },
  
  // 获取视频分类
  getVideoCategories() {
    return http.get('/api/video/categories')
  },
  
  // 按分类获取视频
  getVideosByCategory(categoryId, params) {
    return http.get(`/api/video/category/${categoryId}`, params)
  },
  
  // 点赞视频
  likeVideo(id) {
    return http.post(`/api/video/${id}/like`)
  },
  
  // 取消点赞
  unlikeVideo(id) {
    return http.post(`/api/video/${id}/unlike`)
  },
  
  // 收藏视频
  favoriteVideo(id) {
    return http.post(`/api/video/${id}/favorite`)
  },
  
  // 取消收藏
  unfavoriteVideo(id) {
    return http.post(`/api/video/${id}/unfavorite`)
  },
  
  // 举报视频
  reportVideo(id, data) {
    return http.post(`/api/video/${id}/report`, data)
  },
  
  // 获取视频评论
  getVideoComments(id, params) {
    return http.get(`/api/video/${id}/comments`, params)
  },
  
  // 添加视频评论
  addVideoComment(id, data) {
    return http.post(`/api/video/${id}/comments`, data)
  },
  
  // 删除视频评论
  deleteVideoComment(videoId, commentId) {
    return http.delete(`/api/video/${videoId}/comments/${commentId}`)
  },
  
  // 回复视频评论
  replyVideoComment(videoId, commentId, data) {
    return http.post(`/api/video/${videoId}/comments/${commentId}/reply`, data)
  },
  
  // 获取视频统计数据
  getVideoStats(id) {
    return http.get(`/api/video/${id}/stats`)
  },
  
  // 记录视频播放
  recordVideoPlay(id, data) {
    return http.post(`/api/video/${id}/play`, data)
  },
  
  // 获取视频播放历史
  getPlayHistory(params) {
    return http.get('/api/video/play-history', params)
  },
  
  // 清空播放历史
  clearPlayHistory() {
    return http.delete('/api/video/play-history')
  },
  
  // 获取推荐视频
  getRecommendedVideos(params) {
    return http.get('/api/video/recommended', params)
  },
  
  // 获取相关视频
  getRelatedVideos(id, params) {
    return http.get(`/api/video/${id}/related`, params)
  },

  // 获取视频下载地址
  getVideoDownloadUrl(videoId) {
    return http.get(`/api/v1/video-editing/vod/original/${videoId}/download`)
  }
}

export default videoService 