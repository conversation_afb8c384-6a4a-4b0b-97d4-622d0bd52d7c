@use './variables.scss' as *;

// CSS变量定义
:root {
  --status-bar-height: #{$status-bar-height};
  --safe-area-inset-top: #{$safe-area-inset-top};
  --safe-area-inset-bottom: #{$safe-area-inset-bottom};
  --tabbar-height: #{$tabbar-height};
  --header-height: #{$header-height};
}

// 基础样式重置
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
html, body {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
page {
  font-family: $font-family-secondary;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  color: $text-primary;
  background-color: $bg-secondary;
}

// 安全区域适配
.safe-area-top {
  padding-top: calc(#{$safe-area-inset-top} + 20rpx);
}

.safe-area-bottom {
  padding-bottom: calc(#{$safe-area-inset-bottom} + 20rpx);
}

// 文本样式
.text-primary {
  color: $text-primary;
}

.text-secondary {
  color: $text-secondary;
}

.text-tertiary {
  color: $text-tertiary;
}

.text-inverse {
  color: $text-inverse;
}

.text-link {
  color: $text-link;
}

// 字体大小
.text-xs {
  font-size: $font-size-xs;
}

.text-sm {
  font-size: $font-size-sm;
}

.text-base {
  font-size: $font-size-base;
}

.text-lg {
  font-size: $font-size-lg;
}

.text-xl {
  font-size: $font-size-xl;
}

.text-2xl {
  font-size: $font-size-2xl;
}

.text-3xl {
  font-size: $font-size-3xl;
}

// 字体粗细
.font-light {
  font-weight: $font-weight-light;
}

.font-normal {
  font-weight: $font-weight-normal;
}

.font-medium {
  font-weight: $font-weight-medium;
}

.font-semibold {
  font-weight: $font-weight-semibold;
}

.font-bold {
  font-weight: $font-weight-bold;
}

// 布局工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

// 间距工具类
.p-xs {
  padding: $spacing-xs;
}

.p-sm {
  padding: $spacing-sm;
}

.p-base {
  padding: $spacing-base;
}

.p-lg {
  padding: $spacing-lg;
}

.p-xl {
  padding: $spacing-xl;
}

.p-2xl {
  padding: $spacing-2xl;
}

.px-xs {
  padding-left: $spacing-xs;
  padding-right: $spacing-xs;
}

.px-sm {
  padding-left: $spacing-sm;
  padding-right: $spacing-sm;
}

.px-base {
  padding-left: $spacing-base;
  padding-right: $spacing-base;
}

.px-lg {
  padding-left: $spacing-lg;
  padding-right: $spacing-lg;
}

.px-xl {
  padding-left: $spacing-xl;
  padding-right: $spacing-xl;
}

.py-xs {
  padding-top: $spacing-xs;
  padding-bottom: $spacing-xs;
}

.py-sm {
  padding-top: $spacing-sm;
  padding-bottom: $spacing-sm;
}

.py-base {
  padding-top: $spacing-base;
  padding-bottom: $spacing-base;
}

.py-lg {
  padding-top: $spacing-lg;
  padding-bottom: $spacing-lg;
}

.py-xl {
  padding-top: $spacing-xl;
  padding-bottom: $spacing-xl;
}

.m-xs {
  margin: $spacing-xs;
}

.m-sm {
  margin: $spacing-sm;
}

.m-base {
  margin: $spacing-base;
}

.m-lg {
  margin: $spacing-lg;
}

.m-xl {
  margin: $spacing-xl;
}

.mx-xs {
  margin-left: $spacing-xs;
  margin-right: $spacing-xs;
}

.mx-sm {
  margin-left: $spacing-sm;
  margin-right: $spacing-sm;
}

.mx-base {
  margin-left: $spacing-base;
  margin-right: $spacing-base;
}

.mx-lg {
  margin-left: $spacing-lg;
  margin-right: $spacing-lg;
}

.mx-xl {
  margin-left: $spacing-xl;
  margin-right: $spacing-xl;
}

.my-xs {
  margin-top: $spacing-xs;
  margin-bottom: $spacing-xs;
}

.my-sm {
  margin-top: $spacing-sm;
  margin-bottom: $spacing-sm;
}

.my-base {
  margin-top: $spacing-base;
  margin-bottom: $spacing-base;
}

.my-lg {
  margin-top: $spacing-lg;
  margin-bottom: $spacing-lg;
}

.my-xl {
  margin-top: $spacing-xl;
  margin-bottom: $spacing-xl;
}

// 边框工具类
.border {
  border: 1rpx solid $border-light;
}

.border-light {
  border: 1rpx solid $border-light;
}

.border-medium {
  border: 1rpx solid $border-medium;
}

.border-dark {
  border: 1rpx solid $border-dark;
}

// 圆角工具类
.rounded-xs {
  border-radius: $radius-xs;
}

.rounded-sm {
  border-radius: $radius-sm;
}

.rounded-base {
  border-radius: $radius-base;
}

.rounded-lg {
  border-radius: $radius-lg;
}

.rounded-xl {
  border-radius: $radius-xl;
}

.rounded-2xl {
  border-radius: $radius-2xl;
}

.rounded-full {
  border-radius: $radius-full;
}

// 阴影工具类
.shadow-sm {
  box-shadow: $shadow-sm;
}

.shadow-base {
  box-shadow: $shadow-base;
}

.shadow-lg {
  box-shadow: $shadow-lg;
}

.shadow-xl {
  box-shadow: $shadow-xl;
}

// 背景色工具类
.bg-primary {
  background-color: $bg-primary;
}

.bg-secondary {
  background-color: $bg-secondary;
}

.bg-tertiary {
  background-color: $bg-tertiary;
}

.bg-dark {
  background-color: $bg-dark;
}

// 宽度高度工具类
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.w-screen {
  width: 100vw;
}

.h-screen {
  height: 100vh;
}

// 通用动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn $transition-normal;
}

.animate-slideUp {
  animation: slideUp $transition-normal;
}

.animate-slideDown {
  animation: slideDown $transition-normal;
}

.animate-scaleIn {
  animation: scaleIn $transition-normal;
}

// 交互状态
.interactive {
  transition: all $transition-fast;
  cursor: pointer;
}

.interactive:hover {
  transform: translateY(-2rpx);
  box-shadow: $shadow-lg;
}

.interactive:active {
  transform: translateY(0);
  box-shadow: $shadow-base;
}

// 文本省略
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 隐藏滚动条
.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

// 禁用选择
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
} 