// 首页“开始创作”主卡片跳转及创作页加载完整性测试
const CommonHelpers = require('../common/common-helpers')

// 1.1 页面正常加载
test('首页点击“开始创作”跳转及创作页UI完整性', async () => {
  // 1. 跳转到首页
  await program.navigateTo('/pages/index/index')
  const homePage = await program.currentPage()
  await homePage.waitFor(2000)

  
  // 2. 查找并点击“开始创作”按钮
  const mainCard = await homePage.$('.home-page__main-card')
  expect(mainCard).not.toBeNull()
  if (!mainCard) throw new Error('未找到 .home-page__main-card 元素')
  await mainCard.tap()
  await homePage.waitFor(1000)

  // 3. 检查是否跳转到创作页
  const createPage = await program.currentPage()
  const url = createPage.path // 这里改为属性
  expect(url).toContain('pages/create/index')
  console.log(createPage)

  // 4. 验证页面标题显示为“视频智能剪辑”
  const titleEl = await createPage.$('.video-editing-page .title')
  expect(titleEl).not.toBeNull()
  if (titleEl) {
    const titleText = await titleEl.text()
    expect(titleText).toContain('视频智能剪辑')
  }
  // 5. 验证主要功能模块是否正常显示
  const generateBtn = await createPage.$('.generate-btn')
  const selectVideoBtn = await createPage.$('.upload-label')
  expect(selectVideoBtn).not.toBeNull()
  expect(generateBtn).not.toBeNull()
})