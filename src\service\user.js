/**
 * 用户相关接口服务
 */

import http from '@/utils/http'

const userService = {
  // 用户登录
  login(data) {
    return http.post('/api/user/login', data)
  },
  
  // 用户注册
  register(data) {
    return http.post('/api/user/register', data)
  },
  
  // 发送验证码
  sendCode(data) {
    return http.post('/api/user/send-code', data)
  },
  
  // 验证验证码
  verifyCode(data) {
    return http.post('/api/user/verify-code', data)
  },
  
  // 获取用户信息
  getUserInfo() {
    return http.get('/api/user/info')
  },
  
  // 更新用户信息
  updateUserInfo(data) {
    return http.put('/api/user/info', data)
  },
  
  // 修改密码
  changePassword(data) {
    return http.put('/api/user/password', data)
  },
  
  // 忘记密码
  forgetPassword(data) {
    return http.post('/api/user/forget-password', data)
  },
  
  // 重置密码
  resetPassword(data) {
    return http.post('/api/user/reset-password', data)
  },
  
  // 用户退出登录
  logout() {
    return http.post('/api/user/logout')
  },
  
  // 绑定手机号
  bindPhone(data) {
    return http.post('/api/user/bind-phone', data)
  },
  
  // 解绑手机号
  unbindPhone(data) {
    return http.post('/api/user/unbind-phone', data)
  },
  
  // 绑定邮箱
  bindEmail(data) {
    return http.post('/api/user/bind-email', data)
  },
  
  // 解绑邮箱
  unbindEmail(data) {
    return http.post('/api/user/unbind-email', data)
  },
  
  // 实名认证
  realNameAuth(data) {
    return http.post('/api/user/real-name-auth', data)
  },
  
  // 获取用户VIP信息
  getVipInfo() {
    return http.get('/api/user/vip-info')
  },
  
  // 购买VIP
  buyVip(data) {
    return http.post('/api/user/buy-vip', data)
  },
  
  // 获取用户积分
  getPoints() {
    return http.get('/api/user/points')
  },
  
  // 积分兑换
  exchangePoints(data) {
    return http.post('/api/user/exchange-points', data)
  },
  
  // 获取用户收藏列表
  getFavorites(params) {
    return http.get('/api/user/favorites', params)
  },
  
  // 添加收藏
  addFavorite(data) {
    return http.post('/api/user/favorites', data)
  },
  
  // 取消收藏
  removeFavorite(id) {
    return http.delete(`/api/user/favorites/${id}`)
  },
  
  // 获取用户关注列表
  getFollowing(params) {
    return http.get('/api/user/following', params)
  },
  
  // 关注用户
  followUser(data) {
    return http.post('/api/user/follow', data)
  },
  
  // 取消关注
  unfollowUser(data) {
    return http.post('/api/user/unfollow', data)
  },
  
  // 获取粉丝列表
  getFollowers(params) {
    return http.get('/api/user/followers', params)
  },
  
  // 上传头像
  uploadAvatar(filePath) {
    return http.upload('/api/user/upload-avatar', filePath)
  },
  
  // 获取用户设置
  getSettings() {
    return http.get('/api/user/settings')
  },
  
  // 更新用户设置
  updateSettings(data) {
    return http.put('/api/user/settings', data)
  },
  
  // 删除账号
  deleteAccount(data) {
    return http.post('/api/user/delete-account', data)
  },
  
  // 获取账号注销申请状态
  getDeleteStatus() {
    return http.get('/api/user/delete-status')
  },
  
  // 取消账号注销申请
  cancelDelete() {
    return http.post('/api/user/cancel-delete')
  },
  
  // 获取团队成员列表
  getTeamMembers(params = {}) {
    return http.get('/api/v1/auth/users', params)
  }
}

export default userService 