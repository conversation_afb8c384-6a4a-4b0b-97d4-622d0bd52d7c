// 环境配置
const config = {
  // 根据环境变量设置基础URL
  baseURL: process.env.NODE_ENV === 'production' 
    ? 'http://172.16.4.90:8000' 
    : 'http://172.16.4.90:8000',
  // 素材库API基础URL
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
}

// 日志上传函数（暂时使用console，后续可替换为实际的日志服务）
const uploadLog = (logData) => {
  // TODO: 实际的日志上传地址
  // const logUrl = 'https://log.wangcut.com/api/logs'
  
  // 使用兼容的日志输出方式，避免 console.group 兼容性问题
  console.log('🔍 HTTP Request Log')
  console.log('📊 Log Data:', logData)
  console.log('🕐 Timestamp:', new Date().toISOString())
  console.log('🔍 HTTP Request Log End')
  
  // 实际实现示例：
  // axios.post(logUrl, {
  //   ...logData,
  //   timestamp: new Date().toISOString(),
  //   userAgent: navigator.userAgent,
  //   platform: uni.getSystemInfoSync().platform
  // }).catch(err => {
  //   console.error('日志上传失败:', err)
  // })
}

// 获取用户token
const getToken = () => {
  try {
    return uni.getStorageSync('token') || ''
  } catch (error) {
    console.error('获取token失败:', error)
    return ''
  }
}

// 设置用户token
const setToken = (token) => {
  try {
    if (token) {
      uni.setStorageSync('token', token)
    } else {
      uni.removeStorageSync('token')
    }
  } catch (error) {
    console.error('设置token失败:', error)
  }
}

// 清除用户token
const clearToken = () => {
  try {
    uni.removeStorageSync('token')
  } catch (error) {
    console.error('清除token失败:', error)
  }
}

// 获取用户CID
const getCid = () => {
  try {
    return uni.getStorageSync('cid') || ''
  } catch (error) {
    console.error('获取cid失败:', error)
    return ''
  }
}

// 设置用户CID
const setCid = (cid) => {
  try {
    if (cid) {
      uni.setStorageSync('cid', cid)
    } else {
      uni.removeStorageSync('cid')
    }
  } catch (error) {
    console.error('设置cid失败:', error)
  }
}

// 清除用户CID
const clearCid = () => {
  try {
    uni.removeStorageSync('cid')
  } catch (error) {
    console.error('清除cid失败:', error)
  }
}

// 获取用户ID
const getUserId = () => {
  try {
    return uni.getStorageSync('user_id') || ''
  } catch (error) {
    console.error('获取user_id失败:', error)
    return ''
  }
}

// 设置用户ID
const setUserId = (userId) => {
  try {
    if (userId) {
      uni.setStorageSync('user_id', userId)
    } else {
      uni.removeStorageSync('user_id')
    }
  } catch (error) {
    console.error('设置user_id失败:', error)
  }
}

// 清除用户ID
const clearUserId = () => {
  try {
    uni.removeStorageSync('user_id')
  } catch (error) {
    console.error('清除user_id失败:', error)
  }
}

// 获取设备信息
const getDeviceInfo = () => {
  try {
    const systemInfo = uni.getSystemInfoSync()
    return {
      platform: systemInfo.platform,
      version: systemInfo.version,
      model: systemInfo.model,
      pixelRatio: systemInfo.pixelRatio,
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight
    }
  } catch (error) {
    console.error('获取设备信息失败:', error)
    return {}
  }
}

// 构建请求头
const buildHeaders = (customHeaders = {}) => {
  const headers = {
    'Content-Type': 'application/json;charset=UTF-8',
    ...customHeaders
  }
  
  // 添加token
  const token = getToken()
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  
  // 添加CID
  const cid = getCid()
  if (cid) {
    headers['qs-cid'] = cid
  }
  
  // 添加用户ID
  const userId = getUserId()
  if (userId) {
    headers['qs-uid'] = userId
  }
  
  // 添加设备信息
  const deviceInfo = getDeviceInfo()
  headers['X-Device-Info'] = JSON.stringify(deviceInfo)
  
  // 添加请求ID
  const timestamp = Date.now()
  headers['X-Request-ID'] = `${timestamp}-${Math.random().toString(36).substr(2, 9)}`
  
  return headers
}

// 处理响应数据
const handleResponse = (response, requestInfo) => {
  const { statusCode, data, header } = response
  const endTime = Date.now()
  const duration = endTime - requestInfo.startTime
  
  // 记录响应日志
  uploadLog({
    type: 'response',
    status: statusCode,
    statusText: statusCode === 200 ? 'OK' : 'Error',
    duration: `${duration}ms`,
    headers: header,
    data,
    requestId: requestInfo.requestId,
    url: requestInfo.url
  })
  
  // 处理HTTP状态码
  if (statusCode >= 200 && statusCode < 300) {
    // 成功响应
    if (data && typeof data === 'object') {
      // 根据后端接口规范处理
      if (data.code !== undefined) {
        if (data.code === 200 || data.code === 0 || data.code === 1) {
          return data.data || data
        } else {
          // 业务错误处理
          const error = new Error(data.message || '请求失败')
          error.code = data.code
          error.data = data
          throw error
        }
      }
    }
    return data
  } else {
    // HTTP错误
    const error = new Error(`HTTP ${statusCode}: ${data?.message || '请求失败'}`)
    error.statusCode = statusCode
    error.data = data
    
    // 根据状态码处理不同错误
    switch (statusCode) {
      case 401:
        // 未授权，清除token、cid和user_id并跳转登录
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        clearCid()
        clearUserId()
        uni.showToast({
          title: '登录已过期，请重新登录',
          icon: 'none'
        })
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/login/index'
          })
        }, 1500)
        break
      case 403:
        uni.showToast({
          title: '没有权限访问',
          icon: 'none'
        })
        break
      case 404:
        uni.showToast({
          title: '请求的资源不存在',
          icon: 'none'
        })
        break
      case 500:
        uni.showToast({
          title: '服务器内部错误',
          icon: 'none'
        })
        break
      default:
        uni.showToast({
          title: data?.message || '请求失败',
          icon: 'none'
        })
    }
    
    throw error
  }
}

// 处理请求错误
const handleRequestError = (error, requestInfo) => {
  const endTime = Date.now()
  const duration = endTime - requestInfo.startTime
  
  let errorMessage = '网络连接失败'
  
  // 根据错误类型提供中文提示
  if (error.errMsg) {
    if (error.errMsg.includes('timeout') || error.errMsg.includes('超时')) {
      errorMessage = '请求超时，请检查网络连接'
    } else if (error.errMsg.includes('fail') || error.errMsg.includes('网络')) {
      errorMessage = '网络连接异常，请检查网络设置'
    } else if (error.errMsg.includes('abort')) {
      errorMessage = '请求已取消'
    } else {
      errorMessage = '网络连接失败，请重试'
    }
  }
  
  let errorInfo = {
    type: 'response_error',
    message: errorMessage,
    originalError: error.errMsg || error.message || '网络请求失败',
    duration: `${duration}ms`,
    requestId: requestInfo.requestId,
    url: requestInfo.url
  }
  
  // 记录错误日志
  uploadLog(errorInfo)
  
  // 显示中文错误提示
  uni.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000
  })
  
  return Promise.reject(error)
}

// 封装请求方法
const http = {
  config,
  
  // GET请求
  get(url, params = {}, customConfig = {}) {
    return new Promise((resolve, reject) => {
      const requestInfo = {
        startTime: Date.now(),
        requestId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        url: url
      }
      
      // 构建完整URL
      const fullUrl = customConfig.baseURL ? customConfig.baseURL + url : config.baseURL + url
      
      // 构建查询参数
      const queryString = Object.keys(params).length > 0 
        ? '?' + Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
        : ''
      
      // 记录请求日志
      uploadLog({
        type: 'request',
        method: 'GET',
        url: fullUrl + queryString,
        baseURL: customConfig.baseURL || config.baseURL,
        headers: buildHeaders(customConfig.headers),
        params,
        requestId: requestInfo.requestId
      })
      
      uni.request({
        url: fullUrl + queryString,
        method: 'GET',
        header: buildHeaders(customConfig.headers),
        timeout: customConfig.timeout || config.timeout,
        success: (response) => {
          try {
            const result = handleResponse(response, requestInfo)
            resolve(result)
          } catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          handleRequestError(error, requestInfo)
          reject(error)
        }
      })
    })
  },
  
  // POST请求
  post(url, data = {}, customConfig = {}) {
    return new Promise((resolve, reject) => {
      const requestInfo = {
        startTime: Date.now(),
        requestId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        url: url
      }
      
      // 构建完整URL
      const fullUrl = customConfig.baseURL ? customConfig.baseURL + url : config.baseURL + url
      
      // 记录请求日志
      uploadLog({
        type: 'request',
        method: 'POST',
        url: fullUrl,
        baseURL: customConfig.baseURL || config.baseURL,
        headers: buildHeaders(customConfig.headers),
        data,
        requestId: requestInfo.requestId
      })
      
      uni.request({
        url: fullUrl,
        method: 'POST',
        data: data,
        header: buildHeaders(customConfig.headers),
        timeout: customConfig.timeout || config.timeout,
        success: (response) => {
          try {
            const result = handleResponse(response, requestInfo)
            resolve(result)
          } catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          handleRequestError(error, requestInfo)
          reject(error)
        }
      })
    })
  },
  
  // PUT请求
  put(url, data = {}, customConfig = {}) {
    return new Promise((resolve, reject) => {
      const requestInfo = {
        startTime: Date.now(),
        requestId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        url: url
      }
      
      // 构建完整URL
      const fullUrl = customConfig.baseURL ? customConfig.baseURL + url : config.baseURL + url
      
      // 记录请求日志
      uploadLog({
        type: 'request',
        method: 'PUT',
        url: fullUrl,
        baseURL: customConfig.baseURL || config.baseURL,
        headers: buildHeaders(customConfig.headers),
        data,
        requestId: requestInfo.requestId
      })
      
      uni.request({
        url: fullUrl,
        method: 'PUT',
        data: data,
        header: buildHeaders(customConfig.headers),
        timeout: customConfig.timeout || config.timeout,
        success: (response) => {
          try {
            const result = handleResponse(response, requestInfo)
            resolve(result)
          } catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          handleRequestError(error, requestInfo)
          reject(error)
        }
      })
    })
  },
  
  // DELETE请求
  delete(url, params = {}, customConfig = {}) {
    return new Promise((resolve, reject) => {
      const requestInfo = {
        startTime: Date.now(),
        requestId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        url: url
      }
      
      // 构建完整URL
      const fullUrl = customConfig.baseURL ? customConfig.baseURL + url : config.baseURL + url
      
      // 构建查询参数
      const queryString = Object.keys(params).length > 0 
        ? '?' + Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
        : ''
      
      // 记录请求日志
      uploadLog({
        type: 'request',
        method: 'DELETE',
        url: fullUrl + queryString,
        baseURL: customConfig.baseURL || config.baseURL,
        headers: buildHeaders(customConfig.headers),
        params,
        requestId: requestInfo.requestId
      })
      
      uni.request({
        url: fullUrl + queryString,
        method: 'DELETE',
        header: buildHeaders(customConfig.headers),
        timeout: customConfig.timeout || config.timeout,
        success: (response) => {
          try {
            const result = handleResponse(response, requestInfo)
            resolve(result)
          } catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          handleRequestError(error, requestInfo)
          reject(error)
        }
      })
    })
  },
  
  // PATCH请求
  patch(url, data = {}, customConfig = {}) {
    return new Promise((resolve, reject) => {
      const requestInfo = {
        startTime: Date.now(),
        requestId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        url: url
      }
      
      // 构建完整URL
      const fullUrl = customConfig.baseURL ? customConfig.baseURL + url : config.baseURL + url
      
      // 记录请求日志
      uploadLog({
        type: 'request',
        method: 'PATCH',
        url: fullUrl,
        baseURL: customConfig.baseURL || config.baseURL,
        headers: buildHeaders(customConfig.headers),
        data,
        requestId: requestInfo.requestId
      })
      
      uni.request({
        url: fullUrl,
        method: 'PUT', // uni-app 不支持 PATCH，使用 PUT 代替
        data: data,
        header: buildHeaders(customConfig.headers),
        timeout: customConfig.timeout || config.timeout,
        success: (response) => {
          try {
            const result = handleResponse(response, requestInfo)
            resolve(result)
          } catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          handleRequestError(error, requestInfo)
          reject(error)
        }
      })
    })
  },
  
  // 文件上传
  upload(url, filePath, formData = {}, customConfig = {}) {
    return new Promise((resolve, reject) => {
      const requestId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      // 构建完整URL
      const fullUrl = customConfig.baseURL ? customConfig.baseURL + url : config.baseURL + url
      
      // 记录上传开始日志
      uploadLog({
        type: 'upload_start',
        url: fullUrl,
        filePath,
        formData,
        requestId
      })
      
      const uploadTask = uni.uploadFile({
        url: fullUrl,
        filePath,
        name: 'file',
        formData,
        header: buildHeaders(customConfig.headers),
        success: (res) => {
          uploadLog({
            type: 'upload_success',
            url: fullUrl,
            statusCode: res.statusCode,
            data: res.data,
            requestId
          })
          
          try {
            const data = JSON.parse(res.data)
            resolve(data)
          } catch (error) {
            resolve(res.data)
          }
        },
        fail: (error) => {
          uploadLog({
            type: 'upload_error',
            url: fullUrl,
            error: error.errMsg,
            requestId
          })
          
          // 根据错误类型提供中文提示
          let errorMessage = '上传失败'
          if (error.errMsg && error.errMsg.includes('timeout')) {
            errorMessage = '上传超时，请重试'
          } else if (error.errMsg && error.errMsg.includes('网络')) {
            errorMessage = '网络异常，上传失败'
          }
          
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
          })
          reject(error)
        }
      })
      
      // 返回上传任务，支持进度监听和取消
      return uploadTask
    })
  },
  
  // 文件下载
  download(url, customConfig = {}) {
    return new Promise((resolve, reject) => {
      const requestId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      // 构建完整URL
      const fullUrl = customConfig.baseURL ? customConfig.baseURL + url : config.baseURL + url
      
      uploadLog({
        type: 'download_start',
        url: fullUrl,
        requestId
      })
      
      const downloadTask = uni.downloadFile({
        url: fullUrl,
        header: buildHeaders(customConfig.headers),
        success: (res) => {
          uploadLog({
            type: 'download_success',
            url: fullUrl,
            statusCode: res.statusCode,
            tempFilePath: res.tempFilePath,
            requestId
          })
          
          resolve(res)
        },
        fail: (error) => {
          uploadLog({
            type: 'download_error',
            url: fullUrl,
            error: error.errMsg,
            requestId
          })
          
          // 根据错误类型提供中文提示
          let errorMessage = '下载失败'
          if (error.errMsg && error.errMsg.includes('timeout')) {
            errorMessage = '下载超时，请重试'
          } else if (error.errMsg && error.errMsg.includes('网络')) {
            errorMessage = '网络异常，下载失败'
          }
          
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
          })
          reject(error)
        }
      })
      
      return downloadTask
    })
  }
}

// 导出CID和用户ID管理函数
export { setCid, clearCid, getCid, setUserId, clearUserId, getUserId, setToken, clearToken, getToken }

export default http 