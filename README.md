# AI IDE 规则用法说明

## 1. nvue 文件开发规则

- 编写 `*.nvue` 文件时，需使用 `@wangcut-app-nvue-native-expert.mdc` 进行提示和优化。
- 示例用法：

  ```
  @wangcut-app-nvue-native-expert.mdc 根据规则，帮我优化 @xxxx.nvue 的样式。
  ```

## 2. 新业务页面或组件开发流程

- 新增业务页面或组件时，需同时使用 `@wangcut-app-combined_functional.mdc` 和 `@wangcut-app-develop.mdc`，并提供相关截图（如有）及详细提示词。
- 示例用法1（已有页面增加功能）：

  ```
  @xxxx.vue 在这个页面的“创作课堂”选项上方增加一个“团队成员”选项，然后新增一个团队成员的页面。新的页面你要参考xxxx张图片进行开发。
  @wangcut-app-combined_functional.mdc 先梳理页面的需求，拆解，然后传递给 @wangcut-app-develop.mdc 进行开发。全部都自动执行，不需要询问我。如果有问题，可以问产品经理，得到产品经理答复之后直接执行。允许你们在网络上查询 uni-app 的相关开发文档，允许你们使用 uni-app 的组件。要记得处理顶部的安全距离。开始吧
  ```

  - 示例用法2（新增页面）：

  ```
  我需要开发一个新的页面，你要参考xxx张图片进行开发。
  @wangcut-app-combined_functional.mdc 先梳理页面的需求，拆解，然后传递给 @wangcut-app-develop.mdc 进行开发。全部都自动执行，不需要询问我。如果有问题，可以问产品经理，得到产品经理答复之后直接执行。允许你们在网络上查询 uni-app 的相关开发文档，允许你们使用 uni-app 的组件。要记得处理顶部的安全距离。开始吧
  ```
## 3. 组件使用

- 让AI使用现有组件时，只需要让它查阅 ReadComponents.md 即可。
- 示例用法：

  ```
  @index.vue 这个页面的播放事件，使用公共的视频播放组件播放。在 @ReadComponents.md 寻找合适的组件，帮我实现播放功能。
  ```
> **注意事项：**  
> - 所有流程自动执行，无需人工确认。  
> - 如遇需求不明确，可直接向产品经理提问并根据答复继续执行。  
> - 可联网查阅 uni-app 官方文档，合理使用官方组件。  
> - 开发时需注意适配顶部安全距离，保证多端兼容性。

---