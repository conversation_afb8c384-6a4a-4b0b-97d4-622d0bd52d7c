<template>
  <view class="collapse-panel">
    <!-- 面板头部 -->
    <view 
      class="collapse-panel__header"
      :class="{ 'collapse-panel__header--active': isExpanded }"
      @tap="togglePanel"
    >
      <view class="collapse-panel__title">
        <text v-if="icon" class="collapse-panel__icon">{{ icon }}</text>
        <text class="collapse-panel__text">{{ title }}</text>
      </view>
      
      <view class="collapse-panel__arrow">
        <text class="collapse-panel__arrow-icon">{{ isExpanded ? '▼' : '▶' }}</text>
      </view>
    </view>
    
    <!-- 面板内容 -->
    <view 
      class="collapse-panel__content"
      :class="{ 'collapse-panel__content--expanded': isExpanded }"
      :style="contentStyle"
    >
      <view class="collapse-panel__body">
        <slot></slot>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: ''
  },
  expanded: {
    type: <PERSON>olean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  animation: {
    type: <PERSON>olean,
    default: true
  }
})

// Emits
const emit = defineEmits(['change', 'expand', 'collapse'])

// State
const isExpanded = ref(props.expanded)

// Computed
const contentStyle = computed(() => {
  if (!props.animation) {
    return {
      height: isExpanded.value ? 'auto' : '0',
      overflow: isExpanded.value ? 'visible' : 'hidden'
    }
  }
  
  return {
    height: isExpanded.value ? 'auto' : '0',
    transition: 'height 0.3s ease-in-out',
    overflow: 'hidden'
  }
})

// Methods
const togglePanel = () => {
  if (props.disabled) return
  
  isExpanded.value = !isExpanded.value
  
  emit('change', isExpanded.value)
  
  if (isExpanded.value) {
    emit('expand')
  } else {
    emit('collapse')
  }
}

// Watch props.expanded
import { watch } from 'vue'
watch(() => props.expanded, (newVal) => {
  isExpanded.value = newVal
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.collapse-panel {
  border: 1rpx solid $border-light;
  border-radius: $radius-base;
  overflow: hidden;
  background-color: $bg-primary;
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg;
    background-color: $bg-secondary;
    cursor: pointer;
    transition: all $transition-fast;
    
    &:active {
      background-color: $bg-tertiary;
    }
    
    &--active {
      background-color: $primary-color;
      
      .collapse-panel__text {
        color: $text-inverse;
      }
      
      .collapse-panel__arrow-icon {
        color: $text-inverse;
      }
    }
  }
  
  &__title {
    display: flex;
    align-items: center;
    flex: 1;
  }
  
  &__icon {
    font-size: $font-size-lg;
    margin-right: $spacing-sm;
    color: $text-secondary;
  }
  
  &__text {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $text-primary;
    line-height: $line-height-normal;
  }
  
  &__arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
  }
  
  &__arrow-icon {
    font-size: $font-size-sm;
    color: $text-secondary;
    transition: transform $transition-fast;
  }
  
  &__content {
    height: 0;
    overflow: hidden;
    transition: height 0.3s ease-in-out;
  }
  
  &__body {
    padding: $spacing-lg;
    color: $text-primary;
    line-height: $line-height-normal;
  }
}

// 禁用状态
.collapse-panel--disabled {
  .collapse-panel__header {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:active {
      background-color: $bg-secondary;
    }
  }
}

// 无动画模式
.collapse-panel--no-animation {
  .collapse-panel__content {
    transition: none;
  }
}
</style> 