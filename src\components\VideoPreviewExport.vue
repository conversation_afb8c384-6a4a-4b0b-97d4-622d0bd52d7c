<template>
	<view class="video-preview-container">
		<!-- 顶部导航栏 -->
		<view class="top-nav-bar">
			<view class="status-bar"></view>
			<view class="nav-content">
				<view class="nav-left" @click="onClose">
					<text class="close-icon">×</text>
				</view>
				<view class="nav-right">
					<picker 
						:value="resolutionIndex" 
						:range="resolutionLabels" 
						@change="onResolutionChange"
						class="resolution-picker"
					>
						<view class="resolution-btn">
							<text class="resolution-text">{{ currentResolution }}</text>
							<text class="arrow-icon">▼</text>
						</view>
					</picker>
					<view class="export-btn" @click="onExport">
						<text class="export-text">导出</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 视频播放区域 -->
		<view class="video-container">
			<video 
				:src="videoUrl" 
				class="video-player"
				:controls="true"
				:autoplay="false"
				:loop="false"
				@loadedmetadata="onVideoLoaded"
				@ended="onVideoEnded"
				ref="videoPlayer"
			></video>
		</view>
	</view>
</template>

<script>
import videoService from '@/service/video.js'

export default {
	name: 'VideoPreviewExport',
	// 页面配置，隐藏tabbar
	onLoad() {
		// #ifdef H5
		// 在H5端隐藏滚动
		document.body.style.overflow = 'hidden'
		// #endif
		
		// 全局隐藏tabbar
		uni.hideTabBar()
	},
	
	onUnload() {
		// #ifdef H5
		// 页面卸载时恢复滚动
		document.body.style.overflow = 'auto'
		// #endif
		
		// 全局恢复tabbar
		uni.showTabBar()
	},
	props: {
		// 视频URL
		videoUrl: {
			type: String,
			required: true
		},
		// 视频标题
		videoTitle: {
			type: String,
			default: '视频预览'
		},
		// 视频ID
		videoId: {
			type: [String, Number],
			required: true
		}
	},
	data() {
		return {
			duration: 0,
			currentResolution: '702P',
			resolutionOptions: [
				{ label: '480P', value: '480' },
				{ label: '720P', value: '720' },
				{ label: '702P', value: '702' },
				{ label: '1080P', value: '1080' }
			],
			resolutionIndex: 2 // 默认选中702P
		}
	},
	computed: {
		resolutionLabels() {
			return this.resolutionOptions.map(item => item.label)
		},

	},
	methods: {
		// 关闭预览
		onClose() {
			this.$emit('close')
		},
		
		// 导出视频
		onExport() {
			uni.showLoading({
				title: `正在获取${this.currentResolution}下载地址...`
			})
			
			// 先获取下载地址，再下载视频
			this.getDownloadUrlAndDownload()
		},
		
		// 获取下载地址并下载
		getDownloadUrlAndDownload() {
			const resolution = this.resolutionOptions[this.resolutionIndex].value
			
			videoService.getVideoDownloadUrl(this.videoId)
				.then(res => {
					if (res.status_code === 1 && res.data?.download_url) {
						// 获取到下载地址，开始下载
						this.downloadVideo(res.data.download_url)
					} else {
						uni.hideLoading()
						uni.showToast({
							title: '获取下载地址失败',
							icon: 'none',
							duration: 2000
						})
					}
				})
				.catch(err => {
					uni.hideLoading()
					console.error('获取下载地址失败:', err)
					uni.showToast({
						title: '获取下载地址失败，请重试',
						icon: 'none',
						duration: 2000
					})
				})
		},
		
		// 下载视频到本地
		downloadVideo(downloadUrl) {
			uni.showLoading({
				title: `正在下载${this.currentResolution}视频...`
			})
			
			uni.downloadFile({
				url: downloadUrl,
				success: (res) => {
					if (res.statusCode === 200) {
						uni.hideLoading()
						
						// 显示下载成功提示
						uni.showToast({
							title: '视频下载成功',
							icon: 'success',
							duration: 2000
						})
						
						// 显示后续操作选项
						this.showDownloadOptions(res.tempFilePath)
						
					} else {
						uni.hideLoading()
						uni.showToast({
							title: '下载失败',
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: (err) => {
					uni.hideLoading()
					console.error('下载失败:', err)
					uni.showToast({
						title: '下载失败，请重试',
						icon: 'none',
						duration: 2000
					})
				}
			})
		},
		
		// 显示下载后的操作选项
		showDownloadOptions(tempFilePath) {
			uni.showActionSheet({
				itemList: ['保存到相册', '分享给朋友'],
				success: (res) => {
					switch (res.tapIndex) {
						case 0:
							this.saveToAlbum(tempFilePath)
							break
						case 1:
							this.shareVideo()
							break
					}
				}
			})
		},
		
		// 保存到相册
		saveToAlbum(tempFilePath) {
			uni.showLoading({ title: '保存中...' })
			
			// 直接保存已下载的文件到相册
			uni.saveVideoToPhotosAlbum({
				filePath: tempFilePath,
				success: () => {
					uni.hideLoading()
					uni.showToast({
						title: '已保存到相册',
						icon: 'success'
					})
				},
				fail: (err) => {
					uni.hideLoading()
					if (err.errMsg.includes('auth deny')) {
						uni.showModal({
							title: '提示',
							content: '需要您授权保存到相册',
							success: (modalRes) => {
								if (modalRes.confirm) {
									uni.openSetting()
								}
							}
						})
					} else {
						uni.showToast({
							title: '保存失败',
							icon: 'none'
						})
					}
				}
			})
		},
		
		// 分享视频
		shareVideo() {
			// #ifdef APP-PLUS
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				type: 2, // 视频类型
				href: this.videoUrl,
				title: this.videoTitle,
				success: () => {
					uni.showToast({
						title: '分享成功',
						icon: 'success'
					})
				},
				fail: () => {
					uni.showToast({
						title: '分享失败',
						icon: 'none'
					})
				}
			})
			// #endif
			
			// #ifdef H5
			// H5环境下的分享处理
			if (navigator.share) {
				navigator.share({
					title: this.videoTitle,
					url: this.videoUrl
				}).then(() => {
					uni.showToast({
						title: '分享成功',
						icon: 'success'
					})
				}).catch(() => {
					uni.showToast({
						title: '分享失败',
						icon: 'none'
					})
				})
			} else {
				// 复制链接
				uni.setClipboardData({
					data: this.videoUrl,
					success: () => {
						uni.showToast({
							title: '链接已复制',
							icon: 'success'
						})
					}
				})
			}
			// #endif
		},
		
		// 分辨率选择改变
		onResolutionChange(e) {
			console.log('分辨率选择改变:', e.detail)
			const index = e.detail.value
			this.resolutionIndex = index
			const selectedResolution = this.resolutionOptions[index]
			this.currentResolution = selectedResolution.label
			console.log('选中分辨率:', selectedResolution)
			this.$emit('resolution-change', selectedResolution)
		},
		
		// 视频加载完成
		onVideoLoaded(e) {
			this.duration = e.detail.duration
		},
		
		// 视频播放结束
		onVideoEnded() {
			// 视频播放结束时的处理
		},
		
		// 撤销
		onUndo() {
			this.$emit('undo')
		},
		
		// 重做
		onRedo() {
			this.$emit('redo')
		},
		
		// 全屏
		onFullscreen() {
			this.$emit('fullscreen')
		}
	}
}
</script>

<style scoped>
.video-preview-container {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #000000;
	display: flex;
	z-index: 999;
}

/* 顶部导航栏 */
.top-nav-bar {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	background-color: rgba(0, 0, 0, 0.8);
  display: flex;
	flex-direction: column;
}

.nav-content {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	padding: 0 16px;
	height: 44px;
}

.nav-left {
	width: 32px;
	height: 32px;
	justify-content: center;
	align-items: center;
}

.close-icon {
	color: #ffffff;
	font-size: 24px;
	font-weight: bold;
}

.nav-right {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.resolution-btn {
	display: flex;
	flex-direction: row;
	align-items: center;
	background-color: #333333;
	border-radius: 4px;
	padding: 6px 12px;
	margin-right: 12px;
}

.resolution-text {
	color: #ffffff;
	font-size: 14px;
	margin-right: 4px;
}

.arrow-icon {
	color: #ffffff;
	font-size: 12px;
}


.export-btn {
	background-color: #ff0043;
	border-radius: 4px;
	padding: 8px 16px;
}

.export-text {
	color: #ffffff;
	font-size: 14px;
	font-weight: bold;
}

/* 分辨率选择器 */
.resolution-picker {
	display: inline-block;
}



/* 视频播放区域 */
.video-container {
	flex: 1;
	justify-content: center;
	align-items: center;
	position: relative;
	margin-top: calc(44px + var(--status-bar-height)); /* 为状态栏和导航栏留出空间 */
}

.video-player {
	width: 100%;
	height: 100%;
}
.status-bar {
  height: var(--status-bar-height);
  width: 100%;
}
</style> 