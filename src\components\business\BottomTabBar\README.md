# BottomTabBar 底部标签栏组件

## 组件描述

BottomTabBar 是一个固定在页面底部的标签导航组件，用于应用的主要页面切换。支持自定义标签配置和图标切换效果。

## 功能特性

- 🎯 **固定定位** - 固定在页面底部，始终可见
- 🎨 **图标切换** - 支持激活状态和默认状态的图标切换
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🔄 **页面导航** - 自动处理页面跳转逻辑
- 🎭 **状态管理** - 支持当前激活标签的状态管理

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| currentIndex | Number | 0 | 当前激活的标签索引 |
| tabList | Array | [] | 自定义标签列表，为空时使用默认配置 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | index: Number | 标签切换时触发，返回新的标签索引 |

## 默认标签配置

组件内置了以下默认标签：

1. **首页** - 跳转到 `pages/index/index`
2. **穿版** - 跳转到 `pages/template/index`
3. **爆款模板** - 跳转到 `pages/trending/index`
4. **创作历史** - 跳转到 `pages/history/index`
5. **我的** - 跳转到 `pages/profile/index`

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <!-- 页面内容 -->
    <BottomTabBar :current-index="currentTab" @change="handleTabChange" />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import BottomTabBar from '@/components/business/BottomTabBar/index.vue'

const currentTab = ref(0)

const handleTabChange = (index) => {
  currentTab.value = index
  console.log('切换到标签:', index)
}
</script>
```

### 自定义标签配置

```vue
<template>
  <view>
    <BottomTabBar 
      :current-index="currentTab" 
      :tab-list="customTabList"
      @change="handleTabChange" 
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import BottomTabBar from '@/components/business/BottomTabBar/index.vue'

const currentTab = ref(0)

const customTabList = [
  { 
    text: '首页', 
    icon: '/static/icons/home.png', 
    activeIcon: '/static/icons/home-active.png',
    path: 'pages/index/index' 
  },
  { 
    text: '设置', 
    icon: '/static/icons/settings.png', 
    activeIcon: '/static/icons/settings-active.png',
    path: 'pages/settings/index' 
  }
]

const handleTabChange = (index) => {
  currentTab.value = index
}
</script>
```

## 样式定制

组件使用 SCSS 编写，主要样式变量：

- 标签栏高度：`112rpx`
- 图标尺寸：`44rpx`
- 文字大小：`20rpx`
- 激活状态颜色：`rgba(255, 0, 67, 1)`
- 默认状态颜色：`rgba(140, 140, 140, 1)`

## 注意事项

1. 组件会自动处理页面跳转，使用 `uni.redirectTo` 进行导航
2. 图标路径需要指向有效的静态资源
3. 组件固定在底部，注意页面内容需要预留底部空间
4. 支持自定义标签配置，但需要提供完整的图标路径

## 依赖资源

组件依赖以下静态图标资源：
- `@/static/icons/tab/tab_home.png`
- `@/static/icons/tab/tab_home_active.png`
- `@/static/icons/tab/tab_template.png`
- `@/static/icons/tab/tab_template_active.png`
- `@/static/icons/tab/tab_trending.png`
- `@/static/icons/tab/tab_trending_active.svg`
- `@/static/icons/tab/tab_history.png`
- `@/static/icons/tab/tab_history_active.png`
- `@/static/icons/tab/tab_profile.png`
- `@/static/icons/tab/tab_profile_active.svg` 