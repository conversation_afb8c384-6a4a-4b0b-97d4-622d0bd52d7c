// 视频剪辑完整流程测试
const VideoEditingHelpers = require('../../helpers/video-editing-helpers')
const CommonHelpers = require('../common/common-helpers')
const TestData = require('../../helpers/test-data')

describe('视频剪辑功能 - 完整流程测试', () => {
  let page
  
  beforeAll(async () => {
    // 测试套件开始前的设置
    console.log('🚀 开始完整流程测试')
  })
  
  beforeEach(async () => {
    // 每个测试前重置页面状态
    await VideoEditingHelpers.navigateToVideoEditingPage()
    await VideoEditingHelpers.waitForPageLoad()
    await VideoEditingHelpers.resetPageState()
    page = await program.currentPage()
  })
  
  afterEach(async () => {
    // 测试后截图
    await VideoEditingHelpers.takeScreenshot('complete-flow-test-end')
  })
  
  afterAll(async () => {
    console.log('🏁 完整流程测试结束')
  })
  
  describe('标准完整流程', () => {
    
    test('完整的视频创作流程 - 单个素材', async () => {
      console.log('🧪 测试：完整的视频创作流程 - 单个素材')
      
      // 步骤1：上传视频素材
      console.log('📤 步骤1：上传视频素材')
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      await VideoEditingHelpers.checkUploadedMaterialCount(1)
      await VideoEditingHelpers.takeScreenshot('step1-upload-material')
      
      // 步骤2：设置文案
      console.log('📝 步骤2：设置文案')
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      await VideoEditingHelpers.checkScriptCount(2)
      await VideoEditingHelpers.takeScreenshot('step2-set-script')
      
      // 步骤3：配置配音设置
      console.log('🎤 步骤3：配置配音设置')
      await VideoEditingHelpers.selectVoiceMode('system')
      await VideoEditingHelpers.selectVoiceType(1) // 选择活泼女声
      await VideoEditingHelpers.takeScreenshot('step3-voice-settings')
      
      // 步骤4：配置背景音乐
      console.log('🎵 步骤4：配置背景音乐')
      await VideoEditingHelpers.selectMusicMode('system')
      await VideoEditingHelpers.takeScreenshot('step4-music-settings')
      
      // 步骤5：生成视频
      console.log('🎬 步骤5：生成视频')
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      await VideoEditingHelpers.takeScreenshot('step5-generate-success')
      
      // 步骤6：处理生成成功后的操作
      console.log('✅ 步骤6：处理生成成功后的操作')
      await VideoEditingHelpers.handleGenerationSuccessModal('continue')
      await VideoEditingHelpers.takeScreenshot('step6-continue-creating')
      
      console.log('✅ 单个素材完整流程测试通过')
    })
    
    test('完整的视频创作流程 - 多个素材', async () => {
      console.log('🧪 测试：完整的视频创作流程 - 多个素材')
      
      // 步骤1：上传多个视频素材
      console.log('📤 步骤1：上传多个视频素材')
      const materials = [
        TestData.videoMaterials.normal,
        TestData.videoMaterials.small,
        TestData.videoMaterials.large
      ]
      
      for (let i = 0; i < materials.length; i++) {
        await VideoEditingHelpers.uploadVideoMaterial(materials[i])
        await CommonHelpers.wait(500)
      }
      
      await VideoEditingHelpers.checkUploadedMaterialCount(3)
      await VideoEditingHelpers.takeScreenshot('multi-materials-uploaded')
      
      // 步骤2：设置多条文案
      console.log('📝 步骤2：设置多条文案')
      await VideoEditingHelpers.setCustomScript(TestData.scripts.multiple)
      await VideoEditingHelpers.checkScriptCount(5)
      await VideoEditingHelpers.takeScreenshot('multi-scripts-set')
      
      // 步骤3：配置配音设置
      console.log('🎤 步骤3：配置配音设置')
      await VideoEditingHelpers.selectVoiceMode('system')
      await VideoEditingHelpers.selectVoiceType(2) // 选择通用男声
      await VideoEditingHelpers.takeScreenshot('voice-settings-configured')
      
      // 步骤4：配置自定义背景音乐
      console.log('🎵 步骤4：配置自定义背景音乐')
      await VideoEditingHelpers.selectMusicMode('custom')
      await VideoEditingHelpers.selectCustomMusic(1) // 选择轻快节拍
      await VideoEditingHelpers.takeScreenshot('custom-music-selected')
      
      // 步骤5：生成视频
      console.log('🎬 步骤5：生成视频')
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      await VideoEditingHelpers.takeScreenshot('multi-generate-success')
      
      // 步骤6：选择前往查看任务
      console.log('📋 步骤6：选择前往查看任务')
      await VideoEditingHelpers.handleGenerationSuccessModal('goto')
      await CommonHelpers.wait(2000)
      await VideoEditingHelpers.takeScreenshot('goto-history-page')
      
      console.log('✅ 多个素材完整流程测试通过')
    })
    
    test('完整的视频创作流程 - 自定义配置', async () => {
      console.log('🧪 测试：完整的视频创作流程 - 自定义配置')
      
      // 步骤1：上传视频素材
      console.log('📤 步骤1：上传视频素材')
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      await VideoEditingHelpers.checkUploadedMaterialCount(1)
      
      // 步骤2：设置特殊字符文案
      console.log('📝 步骤2：设置特殊字符文案')
      await VideoEditingHelpers.setCustomScript(TestData.scripts.specialChars)
      await VideoEditingHelpers.checkScriptCount(2)
      
      // 步骤3：配置人声采集（如果支持）
      console.log('🎤 步骤3：配置人声采集')
      await VideoEditingHelpers.selectVoiceMode('record')
      await CommonHelpers.wait(1000)
      
      // 步骤4：配置自定义音乐并测试播放
      console.log('🎵 步骤4：配置自定义音乐并测试播放')
      await VideoEditingHelpers.selectMusicMode('custom')
      await VideoEditingHelpers.selectCustomMusic(2) // 选择温馨背景
      
      // 测试音乐播放
      await VideoEditingHelpers.toggleMusicPlay(2)
      await CommonHelpers.wait(2000)
      await VideoEditingHelpers.toggleMusicPlay(2) // 停止播放
      
      // 步骤5：生成视频
      console.log('🎬 步骤5：生成视频')
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      
      console.log('✅ 自定义配置完整流程测试通过')
    })
    
  })
  
  describe('边界值完整流程', () => {
    
    test('最小配置完整流程', async () => {
      console.log('🧪 测试：最小配置完整流程')
      
      // 使用最小配置
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.small)
      await VideoEditingHelpers.setCustomScript(TestData.scripts.single)
      
      // 使用默认配置
      await VideoEditingHelpers.selectVoiceMode('system')
      await VideoEditingHelpers.selectMusicMode('system')
      
      // 生成视频
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      
      console.log('✅ 最小配置完整流程测试通过')
    })
    
    test('最大配置完整流程', async () => {
      console.log('🧪 测试：最大配置完整流程')
      
      // 使用最大配置
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.large)
      await VideoEditingHelpers.setCustomScript(TestData.scripts.longText)
      
      // 配置所有选项
      await VideoEditingHelpers.selectVoiceMode('system')
      await VideoEditingHelpers.selectVoiceType(3) // 选择少年男声
      await VideoEditingHelpers.selectMusicMode('custom')
      await VideoEditingHelpers.selectCustomMusic(4) // 选择舒缓旋律
      
      // 生成视频
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      
      console.log('✅ 最大配置完整流程测试通过')
    })
    
    test('边界值素材完整流程', async () => {
      console.log('🧪 测试：边界值素材完整流程')
      
      // 测试超短视频
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.short)
      await VideoEditingHelpers.setCustomScript(TestData.scripts.singleChar)
      
      await VideoEditingHelpers.selectVoiceMode('system')
      await VideoEditingHelpers.selectMusicMode('system')
      
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      
      console.log('✅ 边界值素材完整流程测试通过')
    })
    
  })
  
  describe('异常处理完整流程', () => {
    
    test('中途修改配置的完整流程', async () => {
      console.log('🧪 测试：中途修改配置的完整流程')
      
      // 初始配置
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      await VideoEditingHelpers.selectVoiceMode('system')
      await VideoEditingHelpers.selectMusicMode('system')
      
      // 中途修改配置
      console.log('🔄 中途修改配置')
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.small)
      await VideoEditingHelpers.setCustomScript(TestData.scripts.multiple)
      await VideoEditingHelpers.selectVoiceMode('record')
      await VideoEditingHelpers.selectMusicMode('custom')
      await VideoEditingHelpers.selectCustomMusic(0)
      
      // 再次修改配置
      console.log('🔄 再次修改配置')
      await VideoEditingHelpers.selectVoiceMode('system')
      await VideoEditingHelpers.selectVoiceType(1)
      
      // 生成视频
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      
      console.log('✅ 中途修改配置完整流程测试通过')
    })
    
    test('重复操作的完整流程', async () => {
      console.log('🧪 测试：重复操作的完整流程')
      
      // 第一次完整流程
      console.log('🔄 第一次完整流程')
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      await VideoEditingHelpers.selectVoiceMode('system')
      await VideoEditingHelpers.selectMusicMode('system')
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      await VideoEditingHelpers.handleGenerationSuccessModal('continue')
      
      // 第二次完整流程
      console.log('🔄 第二次完整流程')
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.small)
      await VideoEditingHelpers.setCustomScript(TestData.scripts.single)
      await VideoEditingHelpers.selectVoiceMode('system')
      await VideoEditingHelpers.selectVoiceType(2)
      await VideoEditingHelpers.selectMusicMode('custom')
      await VideoEditingHelpers.selectCustomMusic(1)
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      
      console.log('✅ 重复操作完整流程测试通过')
    })
    
    test('错误恢复的完整流程', async () => {
      console.log('🧪 测试：错误恢复的完整流程')
      
      // 先触发错误
      console.log('❌ 触发错误：无素材生成')
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationValidation('请先上传视频素材')
      
      // 修复错误并继续
      console.log('🔧 修复错误并继续')
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      
      // 再次触发错误
      console.log('❌ 再次触发错误：空文案')
      await VideoEditingHelpers.setCustomScript(TestData.scripts.empty)
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationValidation('请先设置文案内容')
      
      // 再次修复错误
      console.log('🔧 再次修复错误')
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      
      // 成功生成
      console.log('✅ 成功生成')
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      
      console.log('✅ 错误恢复完整流程测试通过')
    })
    
  })
  
  describe('性能和稳定性测试', () => {
    
    test('快速操作完整流程', async () => {
      console.log('🧪 测试：快速操作完整流程')
      
      // 快速连续操作
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      
      // 快速切换配置
      await VideoEditingHelpers.selectVoiceMode('system')
      await VideoEditingHelpers.selectVoiceMode('record')
      await VideoEditingHelpers.selectVoiceMode('system')
      
      await VideoEditingHelpers.selectMusicMode('system')
      await VideoEditingHelpers.selectMusicMode('custom')
      await VideoEditingHelpers.selectMusicMode('system')
      
      // 快速点击生成
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      
      console.log('✅ 快速操作完整流程测试通过')
    })
    
    test('长时间操作完整流程', async () => {
      console.log('🧪 测试：长时间操作完整流程')
      
      // 模拟用户长时间操作
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      await CommonHelpers.wait(5000) // 等待5秒
      
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      await CommonHelpers.wait(5000) // 等待5秒
      
      // 测试音乐播放
      await VideoEditingHelpers.selectMusicMode('custom')
      await VideoEditingHelpers.selectCustomMusic(0)
      await VideoEditingHelpers.toggleMusicPlay(0)
      await CommonHelpers.wait(10000) // 播放10秒
      await VideoEditingHelpers.toggleMusicPlay(0) // 停止播放
      
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      
      console.log('✅ 长时间操作完整流程测试通过')
    })
    
  })
  
  describe('用户体验流程', () => {
    
    test('典型用户使用流程', async () => {
      console.log('🧪 测试：典型用户使用流程')
      
      // 模拟真实用户的操作习惯
      console.log('👤 模拟用户进入页面')
      await VideoEditingHelpers.takeScreenshot('user-enter-page')
      
      console.log('👤 用户查看页面内容')
      await CommonHelpers.wait(3000)
      
      console.log('👤 用户上传视频')
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      await VideoEditingHelpers.takeScreenshot('user-upload-video')
      
      console.log('👤 用户预览视频')
      await VideoEditingHelpers.previewMaterial(0)
      await CommonHelpers.wait(2000)
      
      console.log('👤 用户设置文案')
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      await VideoEditingHelpers.takeScreenshot('user-set-script')
      
      console.log('👤 用户试听不同声音')
      await VideoEditingHelpers.selectVoiceType(0)
      await CommonHelpers.wait(2000)
      await VideoEditingHelpers.selectVoiceType(1)
      await CommonHelpers.wait(2000)
      await VideoEditingHelpers.selectVoiceType(2)
      await CommonHelpers.wait(2000)
      
      console.log('👤 用户试听不同音乐')
      await VideoEditingHelpers.selectMusicMode('custom')
      await VideoEditingHelpers.toggleMusicPlay(0)
      await CommonHelpers.wait(3000)
      await VideoEditingHelpers.toggleMusicPlay(0)
      await VideoEditingHelpers.toggleMusicPlay(1)
      await CommonHelpers.wait(3000)
      await VideoEditingHelpers.toggleMusicPlay(1)
      
      console.log('👤 用户选择最终配置')
      await VideoEditingHelpers.selectCustomMusic(1)
      await VideoEditingHelpers.takeScreenshot('user-final-config')
      
      console.log('👤 用户生成视频')
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.checkGenerationSuccess()
      await VideoEditingHelpers.takeScreenshot('user-generate-success')
      
      console.log('👤 用户选择继续创作')
      await VideoEditingHelpers.handleGenerationSuccessModal('continue')
      
      console.log('✅ 典型用户使用流程测试通过')
    })
    
  })
  
}) 