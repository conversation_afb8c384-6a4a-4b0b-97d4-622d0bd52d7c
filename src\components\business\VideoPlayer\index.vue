<template>
  <view class="video-player" :class="{ 'video-player--fullscreen': isFullscreen }">
    <!-- 状态栏占位 -->
    <view class="video-player__status-bar"></view>
    
    <!-- 视频播放器容器 -->
    <view class="video-player__container">
      <!-- 顶部控制栏 -->
      <view class="video-player__top-controls">
        <view class="video-player__top-left">
          <view class="video-player__close-btn" @click="handleClose">
            <text class="video-player__close-icon">×</text>
          </view>
        </view>
        <view class="video-player__top-right">
          <view class="video-player__quality-btn" @click="handleQualityChange">
            <text class="video-player__quality-text">{{ currentQuality }}</text>
            <text class="video-player__quality-arrow">▼</text>
          </view>
          <view class="video-player__export-btn" @click="handleExport">
            <text class="video-player__export-text">导出</text>
          </view>
        </view>
      </view>

      <!-- 视频播放区域 -->
      <view class="video-player__video-area" @dblclick="togglePlay">
        <!-- 当有弹窗时隐藏video组件 -->
        <video
          v-if="!hasModal"
          id="videoPlayer"
          class="video-player__video"
          :src="videoSrc"
          :poster="poster"
          :controls="false"
          :show-center-play-btn="false"
          :show-play-btn="false"
          :show-fullscreen-btn="false"
          :show-progress="false"
          :enable-progress-gesture="false"
          :object-fit="objectFit"
          :loop="loop"
          :muted="muted"
          @play="handlePlay"
          @pause="handlePause"
          @ended="handleEnded"
          @timeupdate="handleTimeUpdate"
          @loadedmetadata="handleLoadedMetadata"
          @loadstart="handleLoadStart"
          @canplay="handleCanPlay"
          @loadeddata="handleLoadedData"
          @canplaythrough="handleCanPlayThrough"
          @error="handleError"
        ></video>
        
        <!-- 弹窗时的占位区域 -->
        <view v-else class="video-player__video-placeholder">
          <text class="video-player__placeholder-text">视频播放已暂停</text>
        </view>
        
        <!-- 播放/暂停覆盖按钮 -->
        <view class="video-player__play-overlay" v-show="!isPlaying && !hasModal" @click="togglePlay">
          <view class="video-player__play-icon">▶</view>
        </view>
        
        <!-- 加载指示器 -->
        <view class="video-player__loading" v-show="isLoading && !isPlaying && !hasModal">
          <view class="video-player__loading-spinner"></view>
          <text class="video-player__loading-text">加载中...</text>
        </view>
      </view>

      <!-- 底部控制栏 -->
      <view class="video-player__bottom-controls">
        <view class="video-player__bottom-left">
          <text class="video-player__time">{{ currentTime }} / {{ totalTime }}</text>
        </view>
        <view class="video-player__bottom-center">
          <view class="video-player__play-btn" @click="togglePlay">
            <text class="video-player__play-btn-icon">{{ isPlaying ? '❚❚' : '▶' }}</text>
          </view>
        </view>
        <view class="video-player__bottom-right">
          <view class="video-player__control-btn" @click="handleRewind">
            <text class="video-player__control-icon">⏪</text>
          </view>
          <view class="video-player__control-btn" @click="handleForward">
            <text class="video-player__control-icon">⏩</text>
          </view>
          <view class="video-player__control-btn" @click="toggleFullscreen">
            <text class="video-player__control-icon">⛶</text>
          </view>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="video-player__progress-container">
        <view class="video-player__progress-bar" @click="handleProgressClick">
          <view class="video-player__progress-bg"></view>
          <view 
            class="video-player__progress-fill" 
            :style="{ width: progressPercent + '%' }"
          ></view>
          <view 
            class="video-player__progress-thumb" 
            :style="{ left: progressPercent + '%' }"
            @touchstart="handleProgressTouchStart"
            @touchmove="handleProgressTouchMove"
            @touchend="handleProgressTouchEnd"
          ></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useAppStore } from '@/store/modules/app.js'

// Props 定义
const props = defineProps({
  videoSrc: {
    type: String,
    required: true
  },
  poster: {
    type: String,
    default: ''
  },
  objectFit: {
    type: String,
    default: 'contain'
  },
  autoPlay: {
    type: Boolean,
    default: false
  },
  loop: {
    type: Boolean,
    default: false
  },
  muted: {
    type: Boolean,
    default: false
  },
  // 新增：弹窗状态
  hasModal: {
    type: Boolean,
    default: false
  }
})

// Emits 定义
const emit = defineEmits([
  'close',
  'play',
  'pause',
  'ended',
  'timeupdate',
  'progress',
  'fullscreen',
  'export',
  'error'
])

// 获取app store
const appStore = useAppStore()

// 响应式数据
const isPlaying = ref(false)
const showControls = ref(true)
const isFullscreen = ref(false)
const isLoading = ref(true)
const currentTime = ref('00:00')
const totalTime = ref('00:00')
const duration = ref(0)
const currentQuality = ref('702P')
const controlsTimer = ref(null)
const videoContext = ref(null)
const loadingTimer = ref(null)
// 新增：记录弹窗显示前的播放状态
const wasPlayingBeforeModal = ref(false)

// 计算属性
const progressPercent = computed(() => {
  if (duration.value === 0) {
    console.log('VideoPlayer: 进度条计算 - 总时长为0')
    return 0
  }
  const current = parseTimeToSeconds(currentTime.value)
  const percent = (current / duration.value) * 100
  console.log('VideoPlayer: 进度条计算', { current, duration: duration.value, percent })
  return percent
})

// 工具函数
const parseTimeToSeconds = (timeStr) => {
  if (typeof timeStr === 'number') return timeStr
  const parts = timeStr.split(':')
  if (parts.length === 2) {
    return parseInt(parts[0]) * 60 + parseInt(parts[1])
  }
  return 0
}

const formatTime = (seconds) => {
  if (typeof seconds === 'string') {
    return seconds
  }
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 方法
const getVideoContext = () => {
  if (!videoContext.value) {
    videoContext.value = uni.createVideoContext('videoPlayer')
  }
  return videoContext.value
}

const handleClose = () => {
  console.log('VideoPlayer: 触发关闭事件')
  
  try {
    // 主要方式：触发close事件（让父组件处理）
    console.log('VideoPlayer: 发送close事件给父组件')
    emit('close')
    
    // 添加一个延迟检查，如果父组件没有处理，则使用备选方案
    setTimeout(() => {
      console.log('VideoPlayer: 检查是否需要使用备选关闭方案')
      
      // 检查当前页面栈
      const pages = getCurrentPages()
      console.log('VideoPlayer: 当前页面栈数量:', pages.length)
      
      // 如果页面栈没有变化，说明父组件可能没有处理close事件
      // 这里我们可以尝试其他方式，但主要应该依赖父组件处理
      
    }, 200)
    
  } catch (error) {
    console.error('VideoPlayer关闭失败:', error)
    // 只有在emit失败时才使用备选方案
    try {
      const pages = getCurrentPages()
      if (pages.length > 1) {
        uni.navigateBack({ delta: 1 })
      } else {
        uni.closeWindow()
      }
    } catch (backupError) {
      console.error('VideoPlayer备选关闭方案也失败:', backupError)
    }
  }
}

const toggleControls = () => {
  // 移除控制栏切换逻辑，永远保持显示
  // showControls.value = !showControls.value
  // 控制栏永远显示，不需要切换
}

const startControlsTimer = () => {
  // 移除自动隐藏定时器逻辑
  // 控制栏永远显示，不需要定时器
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
    controlsTimer.value = null
  }
}

const togglePlay = () => {
  // 如果有弹窗，不允许播放
  if (props.hasModal) {
    console.log('VideoPlayer: 弹窗显示中，不允许播放')
    return
  }
  
  const context = getVideoContext()
  if (isPlaying.value) {
    context.pause()
  } else {
    context.play()
  }
}

const handlePlay = () => {
  isPlaying.value = true
  isLoading.value = false // 播放时隐藏加载指示器
  emit('play')
  // 移除控制栏自动隐藏定时器
  // startControlsTimer()
}

const handlePause = () => {
  isPlaying.value = false
  emit('pause')
  // 移除定时器清理逻辑，控制栏永远显示
  // if (controlsTimer.value) {
  //   clearTimeout(controlsTimer.value)
  //   controlsTimer.value = null
  // }
  // showControls.value = true
}

const handleEnded = () => {
  isPlaying.value = false
  emit('ended')
}

const handleTimeUpdate = (e) => {
  const current = e.detail.currentTime || 0
  const total = e.detail.duration || 0
  
  console.log('VideoPlayer: 时间更新', { current, total })
  
  currentTime.value = formatTime(current)
  
  // 确保总时长正确设置
  if (total > 0 && duration.value !== total) {
    duration.value = total
    totalTime.value = formatTime(total)
    console.log('VideoPlayer: 设置总时长', total)
  }
  
  emit('timeupdate', { current, total })
}

const handleLoadedMetadata = (e) => {
  const total = e.detail.duration || 0
  console.log('VideoPlayer: 元数据加载完成', { total })
  
  if (total > 0) {
    duration.value = total
    totalTime.value = formatTime(total)
    console.log('VideoPlayer: 元数据设置总时长', total)
  }
  
  emit('loadedmetadata', e.detail)
}

const handleLoadStart = () => {
  console.log('VideoPlayer: 开始加载视频')
  isLoading.value = true
  
  // 设置加载超时，防止加载指示器一直显示
  if (loadingTimer.value) {
    clearTimeout(loadingTimer.value)
  }
  loadingTimer.value = setTimeout(() => {
    isLoading.value = false
    console.log('VideoPlayer: 加载超时，强制隐藏加载指示器')
  }, 10000) // 10秒后强制隐藏加载指示器
}

const handleCanPlay = () => {
  console.log('VideoPlayer: 视频可以播放')
  isLoading.value = false
  if (loadingTimer.value) {
    clearTimeout(loadingTimer.value)
    loadingTimer.value = null
  }
}

const handleLoadedData = () => {
  console.log('VideoPlayer: 视频数据加载完成')
  isLoading.value = false
  if (loadingTimer.value) {
    clearTimeout(loadingTimer.value)
    loadingTimer.value = null
  }
}

const handleCanPlayThrough = () => {
  console.log('VideoPlayer: 视频可以流畅播放')
  isLoading.value = false
  if (loadingTimer.value) {
    clearTimeout(loadingTimer.value)
    loadingTimer.value = null
  }
}

const handleError = (e) => {
  console.error('视频播放错误:', e.detail)
  emit('error', e.detail)
}

const handleQualityChange = () => {
  const qualities = ['480P', '720P', '1080P']
  const currentIndex = qualities.indexOf(currentQuality.value)
  const nextIndex = (currentIndex + 1) % qualities.length
  currentQuality.value = qualities[nextIndex]
}

const handleExport = () => {
  emit('export')
}

const handleRewind = () => {
  const context = getVideoContext()
  const current = parseTimeToSeconds(currentTime.value)
  const targetTime = Math.max(0, current - 10)
  context.seek(targetTime)
}

const handleForward = () => {
  const context = getVideoContext()
  const current = parseTimeToSeconds(currentTime.value)
  const targetTime = Math.min(duration.value, current + 10)
  context.seek(targetTime)
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  emit('fullscreen', isFullscreen.value)
}

const handleProgressClick = (e) => {
  const rect = e.target.getBoundingClientRect()
  const clickX = e.clientX - rect.left
  const percent = (clickX / rect.width) * 100
  const targetTime = (percent / 100) * duration.value
  
  const context = getVideoContext()
  context.seek(targetTime)
}

const handleProgressTouchStart = (e) => {
  e.preventDefault()
}

const handleProgressTouchMove = (e) => {
  e.preventDefault()
  const touch = e.touches[0]
  const rect = e.target.getBoundingClientRect()
  const clickX = touch.clientX - rect.left
  const percent = Math.max(0, Math.min(100, (clickX / rect.width) * 100))
  const targetTime = (percent / 100) * duration.value
  
  const context = getVideoContext()
  context.seek(targetTime)
}

const handleProgressTouchEnd = (e) => {
  e.preventDefault()
}

// 监听hasModal属性变化
watch(() => props.hasModal, (newHasModal, oldHasModal) => {
  console.log('VideoPlayer: hasModal变化', { newHasModal, oldHasModal })
  
  if (oldHasModal && !newHasModal) {
    // 弹窗关闭，恢复播放状态
    console.log('VideoPlayer: 弹窗关闭，检查是否需要恢复播放')
    if (wasPlayingBeforeModal.value) {
      console.log('VideoPlayer: 恢复播放')
      nextTick(() => {
        setTimeout(() => {
          const context = getVideoContext()
          context.play()
        }, 100) // 延迟100ms确保视频组件已重新渲染
      })
    }
  } else if (!oldHasModal && newHasModal) {
    // 弹窗显示，记录当前播放状态并暂停视频
    console.log('VideoPlayer: 弹窗显示，记录播放状态并暂停视频', isPlaying.value)
    wasPlayingBeforeModal.value = isPlaying.value
    
    // 如果当前正在播放，则暂停
    if (isPlaying.value) {
      const context = getVideoContext()
      context.pause()
    }
  }
})

// 生命周期
onMounted(() => {
  // 隐藏底部tab
  appStore.setTabBarVisible(false)
  
  // 确保控制栏初始显示
  showControls.value = true
  
  // 添加物理返回键监听
  // #ifdef APP-PLUS
  // 方法1：使用uni.addInterceptor拦截返回事件
  const backInterceptor = uni.addInterceptor({
    returnValue(args) {
      // 拦截返回事件
      if (args && args.eventType === 'back') {
        console.log('VideoPlayer: 拦截到返回事件')
        handleClose()
        return false // 阻止默认返回行为
      }
      return args
    }
  })
  
  // 方法2：同时使用plus.key.addEventListener作为备选方案
  plus.key.addEventListener('backbutton', (e) => {
    console.log('VideoPlayer: 捕获到物理返回键')
    
    try {
      // 阻止事件冒泡，不触发全局返回事件
      if (e && e.preventDefault) {
        e.preventDefault()
        console.log('VideoPlayer: 已阻止默认事件')
      }
      if (e && e.stopPropagation) {
        e.stopPropagation()
        console.log('VideoPlayer: 已阻止事件冒泡')
      }
      
      // 直接实现关闭逻辑，不依赖handleClose方法
      console.log('VideoPlayer: 直接触发关闭事件')
      emit('close')
      
      console.log('VideoPlayer: 关闭事件已发送')
      
    } catch (error) {
      console.error('VideoPlayer: 物理返回键处理失败:', error)
      // 备选方案：尝试页面返回
      try {
        const pages = getCurrentPages()
        if (pages.length > 1) {
          uni.navigateBack({ delta: 1 })
        } else {
          uni.closeWindow()
        }
      } catch (backupError) {
        console.error('VideoPlayer: 备选关闭方案也失败:', backupError)
      }
    }
    
    return false
  })
  // #endif
  
  // #ifdef MP-WEIXIN
  // 微信小程序使用页面返回监听
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  if (currentPage && currentPage.$vm) {
    currentPage.$vm.$on('back', (e) => {
      // 阻止事件冒泡
      if (e && e.preventDefault) {
        e.preventDefault()
      }
      handleClose()
      return false
    })
  }
  // #endif
  
  // 初始化时延迟隐藏加载指示器，防止一直显示
  setTimeout(() => {
    isLoading.value = false
  }, 3000)
  
  if (props.autoPlay) {
    console.log('VideoPlayer: 启用自动播放')
    nextTick(() => {
      setTimeout(() => {
        console.log('VideoPlayer: 尝试自动播放')
        try {
          const context = getVideoContext()
          context.play()
          console.log('VideoPlayer: 自动播放命令已发送')
        } catch (error) {
          console.error('VideoPlayer: 自动播放失败', error)
        }
      }, 500)
    })
  } else {
    console.log('VideoPlayer: 未启用自动播放')
  }
})

onUnmounted(() => {
  // 显示底部tab
  appStore.setTabBarVisible(true)
  
  // 清理返回键监听
  // #ifdef APP-PLUS
  // 移除uni.addInterceptor拦截
  // 注意：uni.removeInterceptor() 会移除所有拦截器，这里我们只移除返回键相关的
  // 在实际使用中，可能需要更精确的清理方式
  
  // 移除plus.key.addEventListener监听
  plus.key.removeEventListener('backbutton')
  // #endif
  
  // #ifdef MP-WEIXIN
  // 清理微信小程序返回监听
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  if (currentPage && currentPage.$vm) {
    currentPage.$vm.$off('back', handleClose)
  }
  // #endif
  
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
  if (loadingTimer.value) {
    clearTimeout(loadingTimer.value)
  }
})
</script>

<style lang="scss" scoped>
.video-player {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 102; // 确保大于100，同时给下载组件留出空间
  display: flex;
  flex-direction: column;

  &--fullscreen {
    .video-player__container {
      height: 100vh;
    }
  }

  &__status-bar {
    height: var(--status-bar-height);
    width: 100%;
  }

  &__container {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  &__top-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 103; // 确保显示在视频播放器之上
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 24rpx;
    background: rgba(0, 0, 0, 0.9); // 使用更明显的背景
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.1); // 添加边框
  }

  &__top-left {
    display: flex;
    align-items: center;
  }

  &__close-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3); // 增强背景
    border: 1rpx solid rgba(255, 255, 255, 0.2); // 添加边框
  }

  &__close-icon {
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
  }

  &__top-right {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  &__quality-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 16rpx;
    background: rgba(0, 0, 0, 0.8); // 增强背景
    border: 1rpx solid rgba(255, 255, 255, 0.2); // 添加边框
    border-radius: 8rpx;
  }

  &__quality-text {
    color: #fff;
    font-size: 24rpx;
  }

  &__quality-arrow {
    color: #fff;
    font-size: 20rpx;
  }

  &__export-btn {
    padding: 12rpx 24rpx;
    background: #ff2d55;
    border: 1rpx solid rgba(255, 255, 255, 0.2); // 添加边框
    border-radius: 8rpx;
  }

  &__export-text {
    color: #fff;
    font-size: 24rpx;
    font-weight: 500;
  }

  &__video-area {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100vh - var(--status-bar-height));
  }

  &__video {
    width: 100%;
    height: 1280rpx;
    object-fit: contain;
  }

  &__video-placeholder {
    width: 100%;
    height: 1280rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
  }

  &__placeholder-text {
    color: #fff;
    font-size: 28rpx;
    opacity: 0.7;
  }

  &__play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120rpx;
    height: 120rpx;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__play-icon {
    color: #fff;
    font-size: 48rpx;
    margin-left: 8rpx;
  }

  &__loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
  }

  &__loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid rgba(255, 255, 255, 0.3);
    border-top: 4rpx solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  &__loading-text {
    color: #fff;
    font-size: 24rpx;
  }

  &__bottom-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 103; // 确保显示在视频播放器之上
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 24rpx;
    background: rgba(0, 0, 0, 0.9); // 使用更明显的背景
    border-top: 1rpx solid rgba(255, 255, 255, 0.1); // 添加边框
  }

  &__bottom-left {
    display: flex;
    align-items: center;
  }

  &__time {
    color: #fff;
    font-size: 24rpx;
    font-weight: 500; // 增强字体粗细
  }

  &__bottom-center {
    display: flex;
    align-items: center;
  }

  &__play-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3); // 增强背景
    border: 1rpx solid rgba(255, 255, 255, 0.2); // 添加边框
  }

  &__play-btn-icon {
    color: #fff;
    font-size: 32rpx;
  }

  &__bottom-right {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  &__control-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3); // 增强背景
    border: 1rpx solid rgba(255, 255, 255, 0.2); // 添加边框
  }

  &__control-icon {
    color: #fff;
    font-size: 24rpx;
  }

  &__progress-container {
    position: absolute;
    bottom: 120rpx;
    left: 24rpx;
    right: 24rpx;
    z-index: 103; // 确保显示在视频播放器之上
  }

  &__progress-bar {
    position: relative;
    height: 12rpx; // 增加高度，更容易点击
    background: rgba(255, 255, 255, 0.2);
    border-radius: 6rpx;
    cursor: pointer;
    border: 1rpx solid rgba(255, 255, 255, 0.1); // 添加边框
  }

  &__progress-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 6rpx;
  }

  &__progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #ff2d55;
    border-radius: 6rpx;
    transition: width 0.1s ease;
  }

  &__progress-thumb {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 24rpx; // 增加大小
    height: 24rpx; // 增加大小
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5); // 增强阴影
    border: 2rpx solid #ff2d55; // 添加边框
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>