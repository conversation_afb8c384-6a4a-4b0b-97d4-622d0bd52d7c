import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const isDev = mode === 'development'
  
  return {
    server: {
      open: true, // 启动后自动打开浏览器
      // ...其他server配置
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/_functions.scss" as *; @use "@/styles/variables.scss" as *; @use "@/styles/mixins.scss" as *;`,
          // 忽略 Sass 弃用警告
          quietDeps: true,
          // 禁用弃用警告
          silenceDeprecations: ['color-functions']
        }
      }
    },
    build: {
      // 开发环境构建配置
      minify: isDev ? false : 'esbuild', // 开发环境不压缩代码
      sourcemap: isDev, // 开发环境生成 sourcemap
      rollupOptions: {
        output: {
          // 开发环境保持代码可读性
          compact: !isDev
        }
      }
    },
    plugins: [
      uni(),
    ],
  }
})
