<template>
  <view class="video-player" :class="{ 'video-player--fullscreen': isFullscreen }">
    <!-- 状态栏占位 -->
    <view class="video-player__status-bar"></view>
    
    <!-- 视频播放器容器 -->
    <view class="video-player__container">
      <!-- 顶部控制栏 -->
      <view class="video-player__top-controls" v-show="showControls">
        <view class="video-player__top-left">
          <view class="video-player__close-btn" @click="handleClose">
            <text class="video-player__close-icon">×</text>
          </view>
        </view>
        <view class="video-player__top-right">
          <view class="video-player__quality-btn" @click="handleQualityChange">
            <text class="video-player__quality-text">{{ currentQuality }}</text>
            <text class="video-player__quality-arrow">▼</text>
          </view>
          <view class="video-player__export-btn" @click="handleExport">
            <text class="video-player__export-text">导出</text>
          </view>
        </view>
      </view>

      <!-- 视频播放区域 -->
      <view class="video-player__video-area" @click="toggleControls" @dblclick="togglePlay">
        <video
          id="videoPlayer"
          class="video-player__video"
          :src="videoSrc"
          :poster="poster"
          :controls="false"
          :show-center-play-btn="false"
          :show-play-btn="false"
          :show-fullscreen-btn="false"
          :show-progress="false"
          :enable-progress-gesture="false"
          :object-fit="objectFit"
          :loop="loop"
          :muted="muted"
          @play="handlePlay"
          @pause="handlePause"
          @ended="handleEnded"
          @timeupdate="handleTimeUpdate"
          @loadedmetadata="handleLoadedMetadata"
          @loadstart="handleLoadStart"
          @canplay="handleCanPlay"
          @error="handleError"
        ></video>
        
        <!-- 播放/暂停覆盖按钮 -->
        <view class="video-player__play-overlay" v-show="!isPlaying && showControls" @click="togglePlay">
          <view class="video-player__play-icon">▶</view>
        </view>
        
        <!-- 加载指示器 -->
        <view class="video-player__loading" v-show="isLoading">
          <view class="video-player__loading-spinner"></view>
          <text class="video-player__loading-text">加载中...</text>
        </view>
      </view>

      <!-- 底部控制栏 -->
      <view class="video-player__bottom-controls" v-show="showControls">
        <view class="video-player__bottom-left">
          <text class="video-player__time">{{ currentTime }} / {{ totalTime }}</text>
        </view>
        <view class="video-player__bottom-center">
          <view class="video-player__play-btn" @click="togglePlay">
            <text class="video-player__play-btn-icon">{{ isPlaying ? '❚❚' : '▶' }}</text>
          </view>
        </view>
        <view class="video-player__bottom-right">
          <view class="video-player__control-btn" @click="handleRewind">
            <text class="video-player__control-icon">⏪</text>
          </view>
          <view class="video-player__control-btn" @click="handleForward">
            <text class="video-player__control-icon">⏩</text>
          </view>
          <view class="video-player__control-btn" @click="toggleFullscreen">
            <text class="video-player__control-icon">⛶</text>
          </view>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="video-player__progress-container" v-show="showControls">
        <view class="video-player__progress-bar" @click="handleProgressClick">
          <view class="video-player__progress-bg"></view>
          <view 
            class="video-player__progress-fill" 
            :style="{ width: progressPercent + '%' }"
          ></view>
          <view 
            class="video-player__progress-thumb" 
            :style="{ left: progressPercent + '%' }"
            @touchstart="handleProgressTouchStart"
            @touchmove="handleProgressTouchMove"
            @touchend="handleProgressTouchEnd"
          ></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// Props 定义
const props = defineProps({
  videoSrc: {
    type: String,
    required: true
  },
  poster: {
    type: String,
    default: ''
  },
  objectFit: {
    type: String,
    default: 'contain'
  },
  autoPlay: {
    type: Boolean,
    default: false
  },
  loop: {
    type: Boolean,
    default: false
  },
  muted: {
    type: Boolean,
    default: false
  }
})

// Emits 定义
const emit = defineEmits([
  'close',
  'play',
  'pause',
  'ended',
  'timeupdate',
  'progress',
  'fullscreen',
  'export',
  'error'
])

// 响应式数据
const isPlaying = ref(false)
const showControls = ref(true)
const isFullscreen = ref(false)
const isLoading = ref(true)
const currentTime = ref('00:00')
const totalTime = ref('00:00')
const duration = ref(0)
const currentQuality = ref('702P')
const controlsTimer = ref(null)
const videoContext = ref(null)

// 计算属性
const progressPercent = computed(() => {
  if (duration.value === 0) return 0
  const current = parseTimeToSeconds(currentTime.value)
  return (current / duration.value) * 100
})

// 工具函数
const parseTimeToSeconds = (timeStr) => {
  if (typeof timeStr === 'number') return timeStr
  const parts = timeStr.split(':')
  if (parts.length === 2) {
    return parseInt(parts[0]) * 60 + parseInt(parts[1])
  }
  return 0
}

const formatTime = (seconds) => {
  if (typeof seconds === 'string') {
    return seconds
  }
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 方法
const getVideoContext = () => {
  if (!videoContext.value) {
    videoContext.value = uni.createVideoContext('videoPlayer')
  }
  return videoContext.value
}

const handleClose = () => {
  emit('close')
}

const toggleControls = () => {
  showControls.value = !showControls.value
  if (showControls.value) {
    startControlsTimer()
  }
}

const startControlsTimer = () => {
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
  controlsTimer.value = setTimeout(() => {
    if (isPlaying.value) {
      showControls.value = false
    }
  }, 3000)
}

const togglePlay = () => {
  const context = getVideoContext()
  if (isPlaying.value) {
    context.pause()
  } else {
    context.play()
  }
}

const handlePlay = () => {
  isPlaying.value = true
  emit('play')
  startControlsTimer()
}

const handlePause = () => {
  isPlaying.value = false
  emit('pause')
}

const handleEnded = () => {
  isPlaying.value = false
  emit('ended')
}

const handleTimeUpdate = (e) => {
  const current = e.detail.currentTime || 0
  const total = e.detail.duration || 0
  
  currentTime.value = formatTime(current)
  duration.value = total
  
  emit('timeupdate', { current, total })
}

const handleLoadedMetadata = (e) => {
  const total = e.detail.duration || 0
  totalTime.value = formatTime(total)
  duration.value = total
}

const handleLoadStart = () => {
  isLoading.value = true
}

const handleCanPlay = () => {
  isLoading.value = false
}

const handleError = (e) => {
  console.error('视频播放错误:', e.detail)
  emit('error', e.detail)
}

const handleQualityChange = () => {
  const qualities = ['480P', '720P', '1080P']
  const currentIndex = qualities.indexOf(currentQuality.value)
  const nextIndex = (currentIndex + 1) % qualities.length
  currentQuality.value = qualities[nextIndex]
}

const handleExport = () => {
  emit('export')
}

const handleRewind = () => {
  const context = getVideoContext()
  const current = parseTimeToSeconds(currentTime.value)
  const targetTime = Math.max(0, current - 10)
  context.seek(targetTime)
}

const handleForward = () => {
  const context = getVideoContext()
  const current = parseTimeToSeconds(currentTime.value)
  const targetTime = Math.min(duration.value, current + 10)
  context.seek(targetTime)
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  emit('fullscreen', isFullscreen.value)
}

const handleProgressClick = (e) => {
  const rect = e.target.getBoundingClientRect()
  const clickX = e.clientX - rect.left
  const percent = (clickX / rect.width) * 100
  const targetTime = (percent / 100) * duration.value
  
  const context = getVideoContext()
  context.seek(targetTime)
}

const handleProgressTouchStart = (e) => {
  e.preventDefault()
}

const handleProgressTouchMove = (e) => {
  e.preventDefault()
  const touch = e.touches[0]
  const rect = e.target.getBoundingClientRect()
  const clickX = touch.clientX - rect.left
  const percent = Math.max(0, Math.min(100, (clickX / rect.width) * 100))
  const targetTime = (percent / 100) * duration.value
  
  const context = getVideoContext()
  context.seek(targetTime)
}

const handleProgressTouchEnd = (e) => {
  e.preventDefault()
}

// 生命周期
onMounted(() => {
  if (props.autoPlay) {
    nextTick(() => {
      setTimeout(() => {
        const context = getVideoContext()
        context.play()
      }, 500)
    })
  }
})

onUnmounted(() => {
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
})
</script>

<style lang="scss" scoped>
.video-player {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 101;
  display: flex;
  flex-direction: column;

  &--fullscreen {
    .video-player__container {
      height: 100vh;
    }
  }

  &__status-bar {
    height: var(--status-bar-height);
    width: 100%;
  }

  &__container {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  &__top-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 24rpx;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5), transparent);
  }

  &__top-left {
    display: flex;
    align-items: center;
  }

  &__close-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
  }

  &__close-icon {
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
  }

  &__top-right {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  &__quality-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 16rpx;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 8rpx;
  }

  &__quality-text {
    color: #fff;
    font-size: 24rpx;
  }

  &__quality-arrow {
    color: #fff;
    font-size: 20rpx;
  }

  &__export-btn {
    padding: 12rpx 24rpx;
    background: #ff2d55;
    border-radius: 8rpx;
  }

  &__export-text {
    color: #fff;
    font-size: 24rpx;
    font-weight: 500;
  }

  &__video-area {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  &__play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120rpx;
    height: 120rpx;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__play-icon {
    color: #fff;
    font-size: 48rpx;
    margin-left: 8rpx;
  }

  &__loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
  }

  &__loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid rgba(255, 255, 255, 0.3);
    border-top: 4rpx solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  &__loading-text {
    color: #fff;
    font-size: 24rpx;
  }

  &__bottom-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 24rpx;
    background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);
  }

  &__bottom-left {
    display: flex;
    align-items: center;
  }

  &__time {
    color: #fff;
    font-size: 24rpx;
  }

  &__bottom-center {
    display: flex;
    align-items: center;
  }

  &__play-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
  }

  &__play-btn-icon {
    color: #fff;
    font-size: 32rpx;
  }

  &__bottom-right {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  &__control-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
  }

  &__control-icon {
    color: #fff;
    font-size: 24rpx;
  }

  &__progress-container {
    position: absolute;
    bottom: 120rpx;
    left: 24rpx;
    right: 24rpx;
    z-index: 10;
  }

  &__progress-bar {
    position: relative;
    height: 8rpx;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4rpx;
    cursor: pointer;
  }

  &__progress-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4rpx;
  }

  &__progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #ff2d55;
    border-radius: 4rpx;
    transition: width 0.1s ease;
  }

  &__progress-thumb {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 20rpx;
    height: 20rpx;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>