<template>
  <view 
    class="function-card interactive"
    :class="[
      `function-card--${type}`,
      `function-card--${size}`,
      { 'function-card--disabled': disabled }
    ]"
    :style="cardStyle"
    @tap="handleClick"
  >
    <view class="function-card__content">
      <!-- 图标区域 -->
      <view class="function-card__icon" v-if="icon || iconSrc">
        <image 
          v-if="iconSrc" 
          :src="iconSrc" 
          mode="aspectFit" 
          class="function-card__icon-image"
        />
        <text v-else class="function-card__icon-text">{{ icon }}</text>
      </view>
      
      <!-- 徽章 -->
      <view v-if="badge" class="function-card__badge">
        <text class="function-card__badge-text">{{ badge }}</text>
      </view>
      
      <!-- 文字区域 -->
      <view class="function-card__text">
        <text class="function-card__title">{{ title }}</text>
        <text v-if="subtitle" class="function-card__subtitle">{{ subtitle }}</text>
      </view>
      
      <!-- 装饰元素 -->
      <view class="function-card__decoration" v-if="showDecoration">
        <view class="function-card__decoration-item"></view>
        <view class="function-card__decoration-item"></view>
        <view class="function-card__decoration-item"></view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  iconSrc: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'primary', // primary, secondary, success, warning, info
    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'info', 'custom'].includes(value)
  },
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  backgroundColor: {
    type: String,
    default: ''
  },
  backgroundImage: {
    type: String,
    default: ''
  },
  textColor: {
    type: String,
    default: ''
  },
  badge: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showDecoration: {
    type: Boolean,
    default: true
  },
  link: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['click'])

// Computed
const cardStyle = computed(() => {
  const styles = {}
  
  if (props.backgroundColor) {
    styles.backgroundColor = props.backgroundColor
  }
  
  if (props.backgroundImage) {
    styles.backgroundImage = `url(${props.backgroundImage})`
  }
  
  if (props.textColor) {
    styles.color = props.textColor
  }
  
  return styles
})

// Methods
const handleClick = () => {
  if (props.disabled) return
  
  emit('click', props)
  
  // 默认跳转行为
  if (props.link) {
    uni.navigateTo({
      url: props.link
    })
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.function-card {
  position: relative;
  border-radius: $radius-lg;
  overflow: hidden;
  box-shadow: $shadow-base;
  transition: all $transition-fast;
  
  &:active {
    transform: translateY(2rpx);
    box-shadow: $shadow-sm;
  }
  
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:active {
      transform: none;
    }
  }
  
  // 大小变体
  &--small {
    .function-card__content {
      padding: $spacing-base;
      min-height: 120rpx;
    }
    
    .function-card__icon {
      width: 48rpx;
      height: 48rpx;
      margin-bottom: $spacing-sm;
    }
    
    .function-card__title {
      font-size: $font-size-base;
    }
    
    .function-card__subtitle {
      font-size: $font-size-xs;
    }
  }
  
  &--medium {
    .function-card__content {
      padding: $spacing-lg;
      min-height: 160rpx;
    }
    
    .function-card__icon {
      width: 64rpx;
      height: 64rpx;
      margin-bottom: $spacing-base;
    }
    
    .function-card__title {
      font-size: $font-size-lg;
    }
    
    .function-card__subtitle {
      font-size: $font-size-sm;
    }
  }
  
  &--large {
    .function-card__content {
      padding: $spacing-xl;
      min-height: 200rpx;
    }
    
    .function-card__icon {
      width: 80rpx;
      height: 80rpx;
      margin-bottom: $spacing-lg;
    }
    
    .function-card__title {
      font-size: $font-size-xl;
    }
    
    .function-card__subtitle {
      font-size: $font-size-base;
    }
  }
  
  // 类型变体
  &--primary {
    background: $gradient-primary;
    color: $text-inverse;
  }
  
  &--secondary {
    background: $gradient-secondary;
    color: $text-inverse;
  }
  
  &--success {
    background: linear-gradient(135deg, $success-color 0%, lighten($success-color, 10%) 100%);
    color: $text-inverse;
  }
  
  &--warning {
    background: linear-gradient(135deg, $warning-color 0%, lighten($warning-color, 10%) 100%);
    color: $text-inverse;
  }
  
  &--info {
    background: linear-gradient(135deg, $info-color 0%, lighten($info-color, 10%) 100%);
    color: $text-inverse;
  }
  
  &--custom {
    background: $bg-primary;
    color: $text-primary;
    border: 1rpx solid $border-light;
  }
  
  &__content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    z-index: 2;
  }
  
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: $radius-base;
    background-color: rgba($text-inverse, 0.1);
    
    &-image {
      width: 100%;
      height: 100%;
    }
    
    &-text {
      font-size: 32rpx;
      font-weight: $font-weight-medium;
    }
  }
  
  &__badge {
    position: absolute;
    top: $spacing-sm;
    right: $spacing-sm;
    background-color: $accent-color;
    padding: 4rpx $spacing-sm;
    border-radius: $radius-full;
    
    &-text {
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;
      color: $text-inverse;
    }
  }
  
  &__text {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
  }
  
  &__title {
    font-weight: $font-weight-semibold;
    line-height: $line-height-tight;
    margin-bottom: $spacing-xs;
    @include text-ellipsis;
  }
  
  &__subtitle {
    opacity: 0.8;
    line-height: $line-height-normal;
    @include text-ellipsis-2;
  }
  
  &__decoration {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
    
    &-item {
      position: absolute;
      width: 40rpx;
      height: 40rpx;
      border-radius: $radius-full;
      background-color: rgba($text-inverse, 0.05);
      
      &:nth-child(1) {
        top: 20rpx;
        right: 60rpx;
        animation: float 3s ease-in-out infinite;
      }
      
      &:nth-child(2) {
        top: 80rpx;
        right: 20rpx;
        width: 24rpx;
        height: 24rpx;
        animation: float 3s ease-in-out infinite 1s;
      }
      
      &:nth-child(3) {
        bottom: 40rpx;
        right: 80rpx;
        width: 16rpx;
        height: 16rpx;
        animation: float 3s ease-in-out infinite 2s;
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

// 响应式适配
@media (max-width: 400px) {
  .function-card {
    &--small {
      .function-card__content {
        padding: $spacing-sm;
        min-height: 100rpx;
      }
    }
    
    &--medium {
      .function-card__content {
        padding: $spacing-base;
        min-height: 140rpx;
      }
    }
    
    &--large {
      .function-card__content {
        padding: $spacing-lg;
        min-height: 180rpx;
      }
    }
  }
}
</style> 