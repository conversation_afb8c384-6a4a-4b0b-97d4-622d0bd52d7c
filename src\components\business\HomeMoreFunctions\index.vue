<template>
  <view class="home-more-functions">
    <text class="home-more-functions__title">{{ computedTitle }}</text>
    <view class="home-more-functions__grid">
      <view 
        v-for="(item, index) in computedFunctionList" 
        :key="index"
        class="home-more-functions__item"
        @click="handleItemClick(item, index)"
      >
        <view class="home-more-functions__icon">
          <text class="home-more-functions__icon-text">{{ item.icon }}</text>
        </view>
        <text class="home-more-functions__text">{{ item.title }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, computed, getCurrentInstance } from 'vue'

// Props
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 功能列表
  functionList: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['item-click'])

// 获取当前实例
const instance = getCurrentInstance()

// Computed
const computedTitle = computed(() => {
  return props.title || instance.proxy.$t('index.moreFeatures')
})

const computedFunctionList = computed(() => {
  if (props.functionList.length > 0) {
    return props.functionList
  }
  
  return [
    { title: instance.proxy.$t('index.accountManage'), icon: '👤', link: '/pages/account/index' },
    { title: instance.proxy.$t('index.distributionTask'), icon: '📤', link: '/pages/distribution/index' },
    { title: instance.proxy.$t('index.privateMessage'), icon: '💬', link: '/pages/message/index' },
    { title: instance.proxy.$t('index.privateMessage'), icon: '💬', link: '/pages/message/index' },
    { title: instance.proxy.$t('index.commentManage'), icon: '💭', link: '/pages/comment/index' },
    { title: instance.proxy.$t('index.tutorial'), icon: '📖', link: '/pages/tutorial/index' },
    { title: instance.proxy.$t('index.dataStats'), icon: '📊', link: '/pages/stats/index' }
  ]
})

// Methods
const handleItemClick = (item, index) => {
  emit('item-click', { item, index })
  
  if (item.link) {
    uni.navigateTo({
      url: item.link
    })
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;

.home-more-functions {
  margin-bottom: 40rpx;
  padding: 0 40rpx;
  
  &__title {
    font-size: 32rpx;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    display: block;
    text-align: center;
    margin-bottom: 50rpx;
  }
  
  &__grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 50rpx 40rpx;
  }
  
  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  &__icon {
    width: 100rpx;
    height: 100rpx;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16rpx;
    backdrop-filter: blur(10rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.15);
  }
  
  &__icon-text {
    font-size: 44rpx;
    opacity: 0.9;
  }
  
  &__text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    font-weight: 400;
  }
}

// 响应式适配
@media (max-width: 400px) {
  .home-more-functions {
    &__grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 40rpx 30rpx;
    }
    
    &__icon {
      width: 90rpx;
      height: 90rpx;
    }
    
    &__icon-text {
      font-size: 40rpx;
    }
    
    &__text {
      font-size: 22rpx;
    }
  }
}
</style> 