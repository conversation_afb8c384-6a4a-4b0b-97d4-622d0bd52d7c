# HomeBanner 首页轮播组件

## 组件描述

HomeBanner 是一个专门为首页设计的轮播图片组件，支持自动轮播、指示器和点击跳转功能。相比通用 Banner 组件，HomeBanner 更专注于图片展示和简单交互。

## 功能特性

- 🎯 **图片轮播** - 支持多张图片自动轮播
- 🎨 **简洁设计** - 专注于图片展示，界面简洁
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🔄 **交互事件** - 支持点击跳转和轮播切换事件
- 🎭 **指示器** - 支持轮播指示器显示

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| bannerList | Array | [] | 轮播图片列表 |
| defaultImage | String | '' | 默认图片（当 bannerList 为空时使用） |
| defaultLink | String | '' | 默认跳转链接 |
| showIndicators | Boolean | true | 是否显示指示器 |
| autoplay | Boolean | true | 是否自动轮播 |
| interval | Number | 3000 | 轮播间隔时间（毫秒） |
| duration | Number | 500 | 滑动动画时长（毫秒） |
| circular | Boolean | true | 是否循环轮播 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | { item, index, link } | 轮播项点击时触发 |
| change | { current, item } | 轮播切换时触发 |

## 数据结构

### bannerList 数据格式

```javascript
[
  {
    image: '/static/images/banner1.jpg',  // 必填：轮播图片
    link: '/pages/detail/index'           // 可选：跳转链接
  }
]
```

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <HomeBanner 
      :banner-list="bannerList"
      @click="handleBannerClick"
      @change="handleBannerChange"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import HomeBanner from '@/components/business/HomeBanner/index.vue'

const bannerList = ref([
  {
    image: '/static/images/banner1.jpg',
    link: '/pages/promotion/index'
  },
  {
    image: '/static/images/banner2.jpg',
    link: '/pages/new-feature/index'
  },
  {
    image: '/static/images/banner3.jpg',
    link: '/pages/activity/index'
  }
])

const handleBannerClick = ({ item, index, link }) => {
  console.log('点击轮播:', index, link)
}

const handleBannerChange = ({ current, item }) => {
  console.log('轮播切换到:', current)
}
</script>
```

### 使用默认图片

```vue
<template>
  <view>
    <HomeBanner 
      :banner-list="bannerList"
      default-image="/static/images/default-banner.jpg"
      default-link="/pages/default/index"
      @click="handleBannerClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import HomeBanner from '@/components/business/HomeBanner/index.vue'

const bannerList = ref([]) // 空列表，将使用默认图片

const handleBannerClick = ({ item, index, link }) => {
  console.log('点击默认轮播:', link)
}
</script>
```

### 自定义轮播配置

```vue
<template>
  <view>
    <HomeBanner 
      :banner-list="bannerList"
      :show-indicators="false"
      :autoplay="false"
      :interval="5000"
      :duration="800"
      :circular="true"
      @click="handleBannerClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import HomeBanner from '@/components/business/HomeBanner/index.vue'

const bannerList = ref([
  {
    image: '/static/images/feature1.jpg',
    link: '/pages/feature1/index'
  },
  {
    image: '/static/images/feature2.jpg',
    link: '/pages/feature2/index'
  }
])

const handleBannerClick = ({ item, index, link }) => {
  // 自定义点击处理逻辑
  if (link) {
    uni.navigateTo({
      url: link,
      fail: (err) => {
        console.error('页面跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  }
}
</script>
```

### 动态加载轮播数据

```vue
<template>
  <view>
    <HomeBanner 
      :banner-list="bannerList"
      :loading="loading"
      @click="handleBannerClick"
    />
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import HomeBanner from '@/components/business/HomeBanner/index.vue'

const bannerList = ref([])
const loading = ref(true)

onMounted(async () => {
  try {
    // 模拟 API 请求
    const response = await fetch('/api/banners')
    const data = await response.json()
    bannerList.value = data.banners
  } catch (error) {
    console.error('加载轮播数据失败:', error)
  } finally {
    loading.value = false
  }
})

const handleBannerClick = ({ item, index, link }) => {
  console.log('点击轮播:', item, index)
}
</script>
```

## 样式定制

组件使用 SCSS 编写，主要样式特点：

- 高度：`400rpx`（小屏幕 `320rpx`）
- 圆角：`24rpx`（小屏幕 `20rpx`）
- 图片模式：`aspectFill`
- 指示器：白色半透明圆点

## 响应式设计

组件支持响应式设计，在小屏幕设备上会自动调整：

- 高度从 `400rpx` 调整为 `320rpx`
- 圆角从 `24rpx` 调整为 `20rpx`
- 网格间距相应调整

## 注意事项

1. 组件会自动处理页面跳转，使用 `uni.navigateTo`
2. 图片建议使用 `aspectFill` 模式以保持比例
3. 轮播数据为空时会使用默认图片
4. 点击事件只在设置了链接时触发
5. 组件会自动处理轮播切换逻辑

## 最佳实践

1. **图片优化**：使用适当尺寸的图片，建议宽高比 2:1
2. **加载性能**：预加载轮播图片以提升用户体验
3. **内容简洁**：轮播内容应该简洁明了，突出重点
4. **跳转逻辑**：确保跳转链接的有效性
5. **用户体验**：合理设置轮播间隔，避免过快或过慢

## 与 Banner 组件的区别

| 特性 | HomeBanner | Banner |
|------|------------|--------|
| 设计风格 | 简洁图片展示 | 丰富内容展示 |
| 内容支持 | 仅图片 | 图片、文字、按钮 |
| 使用场景 | 首页轮播 | 推广横幅 |
| 复杂度 | 简单 | 复杂 |
| 自定义程度 | 低 | 高 | 