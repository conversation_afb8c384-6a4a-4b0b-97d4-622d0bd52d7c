<template>
  <view 
    class="primary-button"
    :class="[
      `primary-button--${type}`,
      `primary-button--${size}`,
      { 'primary-button--disabled': disabled, 'primary-button--loading': loading }
    ]"
    :style="buttonStyle"
    @tap="handleClick"
  >
    <view class="primary-button__content">
      <!-- 加载状态 -->
      <view v-if="loading" class="primary-button__loading">
        <text class="primary-button__loading-icon">⏳</text>
      </view>
      
      <!-- 图标 -->
      <view v-if="icon && !loading" class="primary-button__icon">
        <text class="primary-button__icon-text">{{ icon }}</text>
      </view>
      
      <!-- 文字 -->
      <text v-if="text" class="primary-button__text">{{ text }}</text>
      
      <!-- 插槽内容 -->
      <slot></slot>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  text: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'primary', // primary, secondary, success, warning, danger, info
    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  size: {
    type: String,
    default: 'medium', // small, medium, large
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  backgroundColor: {
    type: String,
    default: ''
  },
  textColor: {
    type: String,
    default: ''
  },
  borderColor: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['click', 'tap'])

// Computed
const buttonStyle = computed(() => {
  const styles = {}
  
  if (props.backgroundColor) {
    styles.backgroundColor = props.backgroundColor
  }
  
  if (props.textColor) {
    styles.color = props.textColor
  }
  
  if (props.borderColor) {
    styles.borderColor = props.borderColor
  }
  
  return styles
})

// Methods
const handleClick = () => {
  if (props.disabled || props.loading) return
  
  emit('click')
  emit('tap') // 同时emit tap事件
}
</script>

<style lang="scss" scoped>
@use "sass:color";
.primary-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: $radius-base;
  border: 1rpx solid transparent;
  font-weight: $font-weight-medium;
  text-align: center;
  cursor: pointer;
  transition: all $transition-fast;
  user-select: none;
  
  &:active {
    transform: translateY(1rpx);
  }
  
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:active {
      transform: none;
    }
  }
  
  &--loading {
    cursor: wait;
    
    &:active {
      transform: none;
    }
  }
  
  // 类型样式
  &--primary {
    background-color: $primary-color;
    color: $text-inverse;
    border-color: $primary-color;
    
    &:active {
      background-color: color.adjust($primary-color, $lightness: -10%);
    }
  }
  
  &--secondary {
    background-color: transparent;
    color: $primary-color;
    border-color: $primary-color;
    
    &:active {
      background-color: rgba($primary-color, 0.1);
    }
  }
  
  &--success {
    background-color: $success-color;
    color: $text-inverse;
    border-color: $success-color;
    
    &:active {
      background-color: color.adjust($success-color, $lightness: -10%);
    }
  }
  
  &--warning {
    background-color: $warning-color;
    color: $text-inverse;
    border-color: $warning-color;
    
    &:active {
      background-color: color.adjust($warning-color, $lightness: -10%);
    }
  }
  
  &--danger {
    background-color: $error-color;
    color: $text-inverse;
    border-color: $error-color;
    
    &:active {
      background-color: color.adjust($error-color, $lightness: -10%);
    }
  }
  
  &--info {
    background-color: $info-color;
    color: $text-inverse;
    border-color: $info-color;
    
    &:active {
      background-color: color.adjust($info-color, $lightness: -10%);
    }
  }
  
  // 尺寸样式
  &--small {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
    min-height: 64rpx;
    
    .primary-button__icon {
      margin-right: $spacing-xs;
    }
  }
  
  &--medium {
    padding: $spacing-sm $spacing-lg;
    font-size: $font-size-base;
    min-height: 80rpx;
    
    .primary-button__icon {
      margin-right: $spacing-sm;
    }
  }
  
  &--large {
    padding: $spacing-base $spacing-xl;
    font-size: $font-size-lg;
    min-height: 96rpx;
    
    .primary-button__icon {
      margin-right: $spacing-base;
    }
  }
  
  &__content {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    
    &-text {
      font-size: 1.2em;
    }
  }
  
  &__text {
    line-height: 1;
  }
  
  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: $spacing-sm;
    
    &-icon {
      font-size: 1.2em;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 