<template>
  <view class="ai-template-page">
    <!-- 固定顶部区域 -->
    <view class="fixed-header">
      <!-- 顶部导航栏 -->
      <view class="header">
        <view class="header-left" @tap="goBack">
          <text class="back-icon">‹</text>
        </view>
        <view class="header-center">
          <text class="header-title">AI穿版</text>
        </view>
        <view class="header-right" @tap="goToHistory">
          <text class="history-icon">🕒</text>
          <text class="history-text">历史</text>
        </view>
      </view>
      <!-- 步骤指示器 -->
      <view class="step-indicator">
        <view class="step-item">
          <view class="step-icon step-icon--completed">
            <text class="step-check">✓</text>
          </view>
          <view class="step-line step-line--completed"></view>
          <text class="step-text">上传款式图</text>
        </view>
        <view class="step-item">
          <view class="step-icon step-icon--current">
            <text class="step-number">2</text>
          </view>
          <view class="step-line"></view>
          <text class="step-text">生成图片</text>
        </view>
        <view class="step-item">
          <view class="step-icon">
            <text class="step-number">3</text>
          </view>
          <text class="step-text">生成视频</text>
        </view>
      </view>
    </view>
    <!-- 可滚动内容区域 -->
    <scroll-view class="scroll-content" scroll-y="true">
      <view class="main-content">
        <!-- 仅在非生成中状态下显示未上传内容 -->
        <view v-if="pageStatus !== 'generating'">
          <!-- 上传穿版图片区域 -->
          <view class="upload-section">
            <view class="upload-header">
              <text class="upload-title">上传穿版图片</text>
              <view class="model-type-switch">
                <view :class="['model-type-btn', {active: modelType === 'real'}]" @tap="setModelType('real')">真人模特</view>
                <view :class="['model-type-btn', {active: modelType === 'virtual'}]" @tap="setModelType('virtual')">假人模特</view>
              </view>
            </view>
            <text class="upload-desc">请上传真人模特穿版图</text>
            <view class="upload-box upload-box--dashed" @tap="uploadImage">
              <view class="upload-placeholder">
                <text class="upload-icon">＋</text>
                <text class="upload-tip">上传单件衣服\n(仅支持.jpg)</text>
              </view>
            </view>
          </view>
          <!-- 选择模特区 -->
          <view class="select-section">
            <text class="section-title">选择模特</text>
            <view class="filter-row">
              <view v-for="f in ['all','custom','male','female']" :key="f" :class="['filter-btn', {active: modelFilter === f}]" @tap="setModelFilter(f)">
                {{ f === 'all' ? '全部' : f === 'custom' ? '自定义' : f === 'male' ? '男性' : '女性' }}
              </view>
            </view>
            <view class="item-list">
              <view v-for="m in filteredModels" :key="m.id" :class="['item-card', {selected: selectedModelId === m.id}]" @tap="selectModel(m.id)">
                <template v-if="m.id === 0">
                  <view class="no-change">不换模特</view>
                </template>
                <template v-else>
                  <image :src="m.url" class="item-img" mode="aspectFill" />
                </template>
              </view>
              <view class="item-card upload-item" @tap="uploadModel">
                <view class="upload-icon">＋</view>
                <text class="upload-tip">上传模特图</text>
              </view>
            </view>
          </view>
          <!-- 选择场景区 -->
          <view class="select-section">
            <text class="section-title">选择场景</text>
            <view class="filter-row">
              <view v-for="f in ['all','custom','life','solid']" :key="f" :class="['filter-btn', {active: sceneFilter === f}]" @tap="setSceneFilter(f)">
                {{ f === 'all' ? '全部' : f === 'custom' ? '自定义' : f === 'life' ? '生活场景' : '纯色背景' }}
              </view>
            </view>
            <view class="item-list">
              <view v-for="s in filteredScenes" :key="s.id" :class="['item-card', {selected: selectedSceneId === s.id}]" @tap="selectScene(s.id)">
                <template v-if="s.id === 0">
                  <view class="no-change">不换场景</view>
                </template>
                <template v-else>
                  <image :src="s.url" class="item-img" mode="aspectFill" />
                  <text class="item-label">{{ s.name }}</text>
                </template>
              </view>
              <view class="item-card upload-item" @tap="uploadScene">
                <view class="upload-icon">＋</view>
                <text class="upload-tip">上传场景图</text>
              </view>
            </view>
          </view>
          <!-- 更多个性化设置 -->
          <view class="more-setting">
            <view class="more-setting-toggle" @tap="toggleMoreSetting">
              <text>更多个性化设置</text>
              <text class="arrow-icon">{{ moreSettingOpen ? '▲' : '▼' }}</text>
            </view>
            <view v-if="moreSettingOpen" class="more-setting-content">
              <view class="setting-row">
                <text class="setting-label">比例</text>
                <picker :range="ratioOptions" :value="selectedRatioIndex" @change="onRatioChange">
                <view class="picker-value">{{ ratioOptions[selectedRatioIndex] }}</view>
</picker>
                <text class="setting-label">张数</text>
                <picker :range="countOptions" :value="selectedCountIndex" @change="onCountChange">
                <view class="picker-value">{{ countOptions[selectedCountIndex] }}</view>
</picker>
              </view>
            </view>
          </view>
          <!-- 底部操作按钮 -->
          <view class="bottom-actions">
            <view class="action-button action-button--primary" @tap="onGenerate">
              <text class="action-button-text action-button-text--primary">
                <text class="video-icon">✦</text>
                生成图片
              </text>
            </view>
          </view>
        </view>
        <!-- 生成中页面内容完整保留在此 -->
        <view v-else>
          <!-- 图片生成进度卡片 -->
          <view class="progress-card">
            <view class="progress-icon">
              <text class="star-icon">✦</text>
            </view>
            <text class="progress-title">图片生成中...</text>
            <text class="progress-subtitle">预计还需10分钟</text>
            <view class="progress-button" @tap="showLater">
              <text class="progress-button-text">稍后查看</text>
            </view>
          </view>
          <!-- 底部生成中的小卡片 -->
          <view class="generating-cards">
            <view v-for="(item, index) in generatingItems" :key="index" class="generating-card">
              <view class="card-icon">
                <text class="card-star">✦</text>
              </view>
              <text class="card-text">生成中...</text>
            </view>
          </view>
          <!-- 底部操作按钮 -->
          <view class="bottom-actions">
            <view class="action-row">
              <view class="action-button action-button--secondary" @tap="reEdit">
                <text class="action-button-text">重新编辑</text>
              </view>
              <view class="action-button action-button--secondary" @tap="reGenerate">
                <text class="action-button-text">重新生成</text>
              </view>
            </view>
            <view class="action-button action-button--primary" @tap="generateVideo">
              <text class="action-button-text action-button-text--primary">
                <text class="video-icon">✦</text>
                生成视频
              </text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'


// 生成中的项目数据
const generatingItems = ref([
  { id: 1 },
  { id: 2 },
  { id: 3 },
  { id: 4 }
])

// 未上传状态相关数据和交互
const pageStatus = ref('init') // 'init' | 'generating'
const modelType = ref('real')
const setModelType = (type) => { modelType.value = type }
const modelFilter = ref('custom')
const setModelFilter = (f) => { modelFilter.value = f }
const modelList = ref([
  { id: 0, name: '不换模特', url: '', isCustom: false, gender: 'all' },
  { id: 1, name: '女模特A', url: 'https://dummyimage.com/100x140/eee/333', isCustom: false, gender: 'female' },
])
const selectedModelId = ref(0)
const selectModel = (id) => { selectedModelId.value = id }
const uploadModel = () => { /* 上传逻辑 */ }
const filteredModels = computed(() => {
  if (modelFilter.value === 'all') return modelList.value
  if (modelFilter.value === 'custom') return modelList.value.filter(m => m.isCustom)
  return modelList.value.filter(m => m.gender === modelFilter.value || m.id === 0)
})
const sceneFilter = ref('custom')
const setSceneFilter = (f) => { sceneFilter.value = f }
const sceneList = ref([
  { id: 0, name: '不换场景', url: '', type: 'all' },
  { id: 1, name: '沙滩', url: 'https://dummyimage.com/100x70/ffe/333', type: 'life' },
])
const selectedSceneId = ref(0)
const selectScene = (id) => { selectedSceneId.value = id }
const uploadScene = () => { /* 上传逻辑 */ }
const filteredScenes = computed(() => {
  if (sceneFilter.value === 'all') return sceneList.value
  if (sceneFilter.value === 'custom') return sceneList.value.filter(s => s.type === 'custom')
  if (sceneFilter.value === 'life') return sceneList.value.filter(s => s.type === 'life' || s.id === 0)
  if (sceneFilter.value === 'solid') return sceneList.value.filter(s => s.type === 'solid' || s.id === 0)
  return sceneList.value
})
const moreSettingOpen = ref(false)
const toggleMoreSetting = () => { moreSettingOpen.value = !moreSettingOpen.value }
const ratioOptions = ['4:3', '1:1', '16:9']
const countOptions = ['1张', '2张', '4张']
const selectedRatioIndex = ref(0)
const selectedCountIndex = ref(1)
const onCountChange = (e) => {
  selectedCountIndex.value = e.detail.value
}
const onRatioChange = (e) => {
  selectedRatioIndex.value = e.detail.value
}

const uploadImage = () => { /* 上传图片逻辑 */ }

// 方法
const goBack = () => {
  uni.navigateBack()
}

const goToHistory = () => {
  uni.navigateTo({
    url: '/pages/history/index'
  })
}

const showLater = () => {
  uni.showToast({
    title: '已加入后台生成队列',
    icon: 'success'
  })
}

const reEdit = () => {
  uni.showToast({
    title: '重新编辑',
    icon: 'none'
  })
}

const reGenerate = () => {
  uni.showToast({
    title: '重新生成',
    icon: 'none'
  })
}

const generateVideo = () => {
  uni.showToast({
    title: '开始生成视频',
    icon: 'success'
  })
}

// 生成图片按钮点击事件
const onGenerate = () => {
  pageStatus.value = 'generating'
}

onMounted(() => {
  // 页面加载完成
})
</script>

<style lang="scss" scoped>
.ai-template-page {
  height: 100vh;
  background-color: #000000;
  color: #ffffff;
  position: relative;
}

// 固定顶部区域
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #000000;
}

// 可滚动内容区域
.scroll-content {
  position: absolute;
  top: 308rpx; // 顶部固定头部高度 (88rpx导航栏 + 220rpx步骤指示器)
  left: 0;
  right: 0;
  bottom: 112rpx; // 滚动到底部，为原生tabBar预留空间
  background-color: #000000;
}

// 顶部导航栏
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #000000;
  
  &-left {
    display: flex;
    align-items: center;
    width: 80rpx;
  }
  
  &-center {
    flex: 1;
    text-align: center;
  }
  
  &-right {
    display: flex;
    align-items: center;
    min-width: 120rpx; // 增大宽度，防止换行
    justify-content: flex-end;
    white-space: nowrap; // 保证不换行
  }
  
  &-title {
    font-size: 36rpx;
    font-weight: 500;
    color: #ffffff;
  }
}

.back-icon {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: 300;
}

.history-icon {
  font-size: 32rpx;
  color: #ffffff;
  margin-right: 8rpx;
}

.history-text {
  font-size: 28rpx;
  color: #ffffff;
}

// 步骤指示器
.step-indicator {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 60rpx 32rpx;
  gap: 60rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 120rpx;
}

.step-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #333333;
  margin-bottom: 16rpx;
  position: relative;
  
  &--completed {
    background-color: #ffffff;
    
    .step-check {
      color: #000000;
      font-size: 24rpx;
      font-weight: bold;
    }
  }
  
  &--current {
    background-color: #666666;
    
    .step-number {
      color: #ffffff;
      font-size: 24rpx;
      font-weight: bold;
    }
  }
}

.step-number {
  color: #666666;
  font-size: 24rpx;
  font-weight: bold;
}

.step-line {
  position: absolute;
  top: 30rpx;
  left: 90rpx;
  width: 60rpx;
  height: 2rpx;
  background-color: #333333;
  
  &--completed {
    background-color: #ffffff;
  }
}

.step-item:last-child .step-line {
  display: none;
}

.step-text {
  font-size: 24rpx;
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
}

// 主要内容区域
.main-content {
  padding: 32rpx 32rpx 0 32rpx; // 上左右内边距，底部由bottom-actions处理
}

// 进度卡片
.progress-card {
  background: linear-gradient(135deg, #FF6B9D 0%, #FF8A65 100%);
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.progress-icon {
  margin-bottom: 32rpx;
}

.star-icon {
  font-size: 60rpx;
  color: #ffffff;
}

.progress-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.progress-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40rpx;
}

.progress-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  background-color: transparent;
}

.progress-button-text {
  font-size: 28rpx;
  color: #ffffff;
}

// 生成中的小卡片
.generating-cards {
  display: flex;
  gap: 16rpx;
  margin-bottom: 60rpx;
}

.generating-card {
  flex: 1;
  background: linear-gradient(135deg, #FF6B9D 0%, #FF8A65 100%);
  border-radius: 16rpx;
  padding: 32rpx 16rpx;
  text-align: center;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.card-icon {
  margin-bottom: 12rpx;
}

.card-star {
  font-size: 32rpx;
  color: #ffffff;
}

.card-text {
  font-size: 24rpx;
  color: #ffffff;
  white-space: nowrap;
}

// 底部操作按钮
.bottom-actions {
  background-color: #000000;
  padding: 32rpx;
  padding-bottom: 140rpx; // 为原生tabBar预留空间 (112rpx tabBar + 28rpx 缓冲)
  margin-top: 40rpx; // 与上方内容的间距
}

.action-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.action-button {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
  }
  
  &--secondary {
    background-color: #333333;
    
    .action-button-text {
      color: #ffffff;
    }
    
    &:active {
      background-color: #444444;
    }
  }
  
  &--primary {
    background-color: #333333;
    
    .action-button-text--primary {
      color: #ffffff;
      display: flex;
      align-items: center;
      gap: 8rpx;
    }
    
    &:active {
      background-color: #444444;
    }
  }
}

.action-button-text {
  font-size: 32rpx;
  font-weight: 500;
}

.video-icon {
  font-size: 32rpx;
}

// 上传穿版图片区域
.upload-section {
  background: #19191a;
  border-radius: 20rpx;
  padding: 0 0 32rpx 0;
  margin-bottom: 32rpx;
}
.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 0 32rpx;
}
.upload-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}
.model-type-switch {
  display: flex;
  gap: 16rpx;
}
.model-type-btn {
  min-width: 104rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 24rpx;
  background: #232325;
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  padding: 0 24rpx;
  border: none;
  transition: background 0.2s;
  &.active {
    background: #ff2d55;
    color: #fff;
  }
}
.upload-desc {
  font-size: 24rpx;
  color: #b2b2b2;
  padding: 0 32rpx 24rpx 32rpx;
}
.upload-box {
  margin: 0 32rpx;
  height: 240rpx;
  border-radius: 20rpx;
  background: #232325;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  &.upload-box--dashed {
    border: 2rpx dashed #b2b2b2;
    background: #232325;
  }
}
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-icon {
  font-size: 56rpx;
  color: #b2b2b2;
  margin-bottom: 12rpx;
}
.upload-tip {
  font-size: 26rpx;
  color: #b2b2b2;
  text-align: center;
  white-space: pre-line;
}

// 选择模特/场景区
.select-section {
  background: #19191a;
  border-radius: 20rpx;
  padding: 0 0 32rpx 0;
  margin-bottom: 32rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  padding: 32rpx 32rpx 0 32rpx;
}
.filter-row {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 0 32rpx;
}
.filter-btn {
  min-width: 96rpx;
  height: 44rpx;
  line-height: 44rpx;
  border-radius: 22rpx;
  background: #232325;
  color: #fff;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  padding: 0 20rpx;
  border: none;
  transition: background 0.2s;
  &.active {
    background: #ff2d55;
    color: #fff;
  }
}
.item-list {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
  padding: 24rpx 32rpx 0 32rpx;
}
.item-card {
  width: 120rpx;
  height: 140rpx;
  border-radius: 16rpx;
  background: #232325;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 2rpx solid transparent;
  &.selected {
    border: 2rpx solid #ff2d55;
  }
  &.upload-item {
    border: 2rpx dashed #b2b2b2;
    background: #232325;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
.item-img {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  object-fit: cover;
}
.no-change {
  font-size: 24rpx;
  color: #b2b2b2;
  text-align: center;
}
.item-label {
  position: absolute;
  bottom: 8rpx;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 22rpx;
  color: #fff;
  background: rgba(0,0,0,0.4);
  border-radius: 0 0 16rpx 16rpx;
  padding: 4rpx 0;
}

// 更多个性化设置
.more-setting {
  background: #19191a;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
}
.more-setting-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 0 32rpx;
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}
.arrow-icon {
  font-size: 28rpx;
  color: #b2b2b2;
}
.more-setting-content {
  padding: 24rpx 32rpx 0 32rpx;
}
.setting-row {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.setting-label {
  font-size: 26rpx;
  color: #fff;
  min-width: 80rpx;
}
.picker-value {
  font-size: 26rpx;
  color: #fff;
  background: #232325;
  border-radius: 12rpx;
  padding: 8rpx 24rpx;
  margin-right: 16rpx;
}

// 生成图片按钮
.bottom-actions {
  background: transparent;
  padding: 0 32rpx 32rpx 32rpx;
}
.action-button--primary {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: #ff2d55;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-top: 0;
  &.disabled {
    background: #232325;
    color: #b2b2b2;
  }
}
.action-button-text--primary {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 32rpx;
}
.video-icon {
  font-size: 32rpx;
}
</style> 