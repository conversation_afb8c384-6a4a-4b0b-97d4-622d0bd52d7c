module.exports = {
  globalTeardown: '@dcloudio/uni-automator/dist/teardown.js',
  testEnvironment: '@dcloudio/uni-automator/dist/environment.js',
  testEnvironmentOptions: {
    compile: true,
    h5: {
      options: {
        headless: false
      }
    }
  },
  testTimeout: 10000,
  testMatch: [
    "<rootDir>/src/tests/e2e/**/home-main-card.test.js",
    "<rootDir>/src/tests/e2e/*.e2e.js"
  ],
  moduleFileExtensions: ['js', 'json', 'vue'],
  rootDir: __dirname,
  testPathIgnorePatterns: ['/node_modules/'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './test-results/reports',
      filename: 'test-report-unit.html',
      pageTitle: '旺剪App视频剪辑功能测试报告',
      expand: true,
      openReport: true,
      testPathIgnorePatterns: ['<rootDir>/node_modules/'], // 忽略的测试文件路径模式
    }]
  ],
};