# FunctionCard 功能卡片组件

## 组件描述

FunctionCard 是一个多功能卡片组件，用于展示各种功能入口。支持多种样式变体、自定义背景、图标和交互效果。

## 功能特性

- 🎯 **多种样式** - 支持 primary、secondary、success、warning、info、custom 等样式变体
- 🎨 **尺寸可选** - 支持 small、medium、large 三种尺寸
- 📱 **自定义背景** - 支持背景色和背景图片
- 🔄 **交互效果** - 点击缩放和阴影变化
- 🎭 **状态管理** - 支持禁用状态和徽章显示

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | - | 卡片标题（必填） |
| subtitle | String | '' | 卡片副标题 |
| icon | String | '' | 图标文本 |
| iconSrc | String | '' | 图标图片路径 |
| type | String | 'primary' | 卡片类型：primary/secondary/success/warning/info/custom |
| size | String | 'medium' | 卡片尺寸：small/medium/large |
| backgroundColor | String | '' | 自定义背景色 |
| backgroundImage | String | '' | 自定义背景图片 |
| textColor | String | '' | 自定义文字颜色 |
| badge | String | '' | 徽章文本 |
| disabled | Boolean | false | 是否禁用 |
| showDecoration | Boolean | true | 是否显示装饰元素 |
| link | String | '' | 跳转链接 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | props: Object | 卡片点击时触发，返回组件 props |

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <FunctionCard 
      title="AI 文案生成"
      subtitle="智能生成优质文案"
      icon="✍️"
      @click="handleClick"
    />
  </view>
</template>

<script setup>
import FunctionCard from '@/components/business/FunctionCard/index.vue'

const handleClick = (props) => {
  console.log('点击卡片:', props.title)
  uni.navigateTo({
    url: '/pages/ai-copywriting/index'
  })
}
</script>
```

### 不同类型和尺寸

```vue
<template>
  <view class="card-demo">
    <!-- 主要样式 -->
    <FunctionCard 
      title="视频制作"
      subtitle="一键生成视频"
      icon="🎬"
      type="primary"
      size="large"
      @click="handleVideoCreate"
    />
    
    <!-- 成功样式 -->
    <FunctionCard 
      title="数据分析"
      subtitle="查看数据统计"
      icon="📊"
      type="success"
      size="medium"
      @click="handleAnalytics"
    />
    
    <!-- 警告样式 -->
    <FunctionCard 
      title="系统设置"
      subtitle="配置系统参数"
      icon="⚙️"
      type="warning"
      size="small"
      @click="handleSettings"
    />
  </view>
</template>

<script setup>
import FunctionCard from '@/components/business/FunctionCard/index.vue'

const handleVideoCreate = () => {
  uni.navigateTo({ url: '/pages/video-create/index' })
}

const handleAnalytics = () => {
  uni.navigateTo({ url: '/pages/analytics/index' })
}

const handleSettings = () => {
  uni.navigateTo({ url: '/pages/settings/index' })
}
</script>
```

### 自定义样式

```vue
<template>
  <view>
    <!-- 自定义背景色 -->
    <FunctionCard 
      title="VIP 功能"
      subtitle="专属特权功能"
      icon="👑"
      type="custom"
      background-color="#ffd700"
      text-color="#333"
      badge="VIP"
      @click="handleVipFeature"
    />
    
    <!-- 自定义背景图片 -->
    <FunctionCard 
      title="背景音乐"
      subtitle="精选 BGM 库"
      icon="🎵"
      type="custom"
      background-image="/static/images/bgm-bg.jpg"
      text-color="#fff"
      @click="handleBgmSelector"
    />
  </view>
</template>

<script setup>
import FunctionCard from '@/components/business/FunctionCard/index.vue'

const handleVipFeature = () => {
  uni.showModal({
    title: '提示',
    content: '此功能需要 VIP 会员',
    success: (res) => {
      if (res.confirm) {
        uni.navigateTo({ url: '/pages/vip/index' })
      }
    }
  })
}

const handleBgmSelector = () => {
  uni.navigateTo({ url: '/pages/bgm-selector/index' })
}
</script>
```

### 禁用状态

```vue
<template>
  <view>
    <FunctionCard 
      title="高级功能"
      subtitle="即将上线"
      icon="🚀"
      :disabled="true"
      @click="handleAdvancedFeature"
    />
  </view>
</template>

<script setup>
import FunctionCard from '@/components/business/FunctionCard/index.vue'

const handleAdvancedFeature = () => {
  // 禁用状态下不会触发此函数
  console.log('功能暂未开放')
}
</script>
```

### 使用图片图标

```vue
<template>
  <view>
    <FunctionCard 
      title="模板中心"
      subtitle="海量模板选择"
      icon-src="/static/icons/template.png"
      badge="HOT"
      @click="handleTemplateCenter"
    />
  </view>
</template>

<script setup>
import FunctionCard from '@/components/business/FunctionCard/index.vue'

const handleTemplateCenter = () => {
  uni.navigateTo({ url: '/pages/template-center/index' })
}
</script>
```

## 样式变体

### 类型样式

- **primary**: 主要功能，蓝色主题
- **secondary**: 次要功能，灰色主题
- **success**: 成功状态，绿色主题
- **warning**: 警告状态，橙色主题
- **info**: 信息状态，蓝色主题
- **custom**: 自定义样式

### 尺寸规格

- **small**: 小尺寸，适合紧凑布局
- **medium**: 中等尺寸，默认规格
- **large**: 大尺寸，适合重要功能

## 样式定制

组件使用 SCSS 编写，主要样式特点：

- 圆角：`$radius-lg`
- 阴影：`$shadow-base`
- 过渡动画：`$transition-fast`
- 交互效果：点击时向下移动 2rpx

## 注意事项

1. `title` 是必填参数，不能为空
2. 图标支持文本和图片两种形式，优先使用 `iconSrc`
3. 自定义背景色和背景图片会覆盖默认样式
4. 禁用状态下不会触发点击事件
5. 组件会自动处理页面跳转

## 最佳实践

1. **标题简洁**：保持标题简洁明了，建议不超过 8 个字符
2. **副标题补充**：用副标题补充说明功能用途
3. **图标统一**：建议使用统一的图标风格
4. **徽章使用**：用于突出重要功能或新功能
5. **颜色搭配**：自定义颜色时注意对比度和可读性 