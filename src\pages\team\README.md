# 团队成员页面

## 功能描述
团队成员管理页面，用于查看和管理团队成员的权限分配。

## 主要功能
1. **查看团队成员列表** - 显示创建者和普通成员
2. **修改成员权限** - 支持运营、协作者、成员三种权限级别
3. **移除成员** - 从团队中移除指定成员
4. **分配算力** - 进入算力分配页面（功能待开发）

## 页面结构
- 顶部导航栏：返回按钮、页面标题、分配算力按钮
- 创建者区域：显示团队创建者信息
- 成员区域：显示普通成员列表
- 权限修改弹窗：底部弹出的权限设置界面

## 权限级别说明
- **运营**：可进行成员管理、素材管理
- **协作者**：可进行素材管理
- **成员**：可浏览、保存文案和素材

## 技术实现
- 使用Vue 3 Composition API
- 响应式设计，支持不同屏幕尺寸
- 自定义导航栏，处理安全距离
- 底部弹窗组件，支持权限选择和成员移除

## 文件结构
```
src/pages/team/
├── index.vue          # 主页面文件
└── README.md          # 说明文档
```

## 相关资源
- 图标资源：`src/asset/img/team/`
- 头像资源：复用个人中心页面的头像