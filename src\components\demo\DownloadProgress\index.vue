<template>
  <view class="download-progress-demo">
    <view class="demo-header">
      <text class="demo-title">下载进度组件演示</text>
    </view>
    
    <view class="demo-content">
      <view class="demo-section">
        <text class="demo-section-title">基础用法</text>
        <view class="demo-buttons">
          <view class="demo-btn" @click="showBasicDemo">
            <text class="demo-btn-text">显示下载进度</text>
          </view>
        </view>
      </view>
      
      <view class="demo-section">
        <text class="demo-section-title">模拟下载</text>
        <view class="demo-buttons">
          <view class="demo-btn" @click="startMockDownload">
            <text class="demo-btn-text">开始模拟下载</text>
          </view>
          <view class="demo-btn demo-btn--secondary" @click="cancelMockDownload">
            <text class="demo-btn-text">取消下载</text>
          </view>
        </view>
      </view>
      
      <view class="demo-section">
        <text class="demo-section-title">不同状态</text>
        <view class="demo-buttons">
          <view class="demo-btn" @click="showCompletedDemo">
            <text class="demo-btn-text">下载完成</text>
          </view>
          <view class="demo-btn" @click="showFailedDemo">
            <text class="demo-btn-text">下载失败</text>
          </view>
          <view class="demo-btn" @click="showCancelledDemo">
            <text class="demo-btn-text">下载取消</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 下载进度组件 -->
    <DownloadProgress
      :visible="showDownloadProgress"
      :progress="downloadProgress"
      :file-name="downloadFileName"
      :status="downloadStatus"
      @cancel="handleCancelDownload"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import DownloadProgress from '@/components/business/DownloadProgress/index.vue'

// 下载进度相关变量
const showDownloadProgress = ref(false)
const downloadProgress = ref(0)
const downloadFileName = ref('')
const downloadStatus = ref('downloading')
let mockDownloadTimer = null

// 基础演示
function showBasicDemo() {
  downloadFileName.value = '演示视频.mp4'
  downloadProgress.value = 50
  downloadStatus.value = 'downloading'
  showDownloadProgress.value = true
  
  setTimeout(() => {
    showDownloadProgress.value = false
  }, 3000)
}

// 模拟下载
function startMockDownload() {
  downloadFileName.value = '模拟下载文件.mp4'
  downloadProgress.value = 0
  downloadStatus.value = 'downloading'
  showDownloadProgress.value = true
  
  // 模拟进度更新
  mockDownloadTimer = setInterval(() => {
    downloadProgress.value += Math.random() * 10
    if (downloadProgress.value >= 100) {
      downloadProgress.value = 100
      downloadStatus.value = 'completed'
      clearInterval(mockDownloadTimer)
      mockDownloadTimer = null
      
      setTimeout(() => {
        showDownloadProgress.value = false
      }, 2000)
    }
  }, 500)
}

// 取消模拟下载
function cancelMockDownload() {
  if (mockDownloadTimer) {
    clearInterval(mockDownloadTimer)
    mockDownloadTimer = null
  }
  handleCancelDownload()
}

// 显示完成状态
function showCompletedDemo() {
  downloadFileName.value = '已完成文件.mp4'
  downloadProgress.value = 100
  downloadStatus.value = 'completed'
  showDownloadProgress.value = true
  
  setTimeout(() => {
    showDownloadProgress.value = false
  }, 2000)
}

// 显示失败状态
function showFailedDemo() {
  downloadFileName.value = '失败文件.mp4'
  downloadProgress.value = 45
  downloadStatus.value = 'failed'
  showDownloadProgress.value = true
  
  setTimeout(() => {
    showDownloadProgress.value = false
  }, 3000)
}

// 显示取消状态
function showCancelledDemo() {
  downloadFileName.value = '已取消文件.mp4'
  downloadProgress.value = 30
  downloadStatus.value = 'cancelled'
  showDownloadProgress.value = true
  
  setTimeout(() => {
    showDownloadProgress.value = false
  }, 2000)
}

// 取消下载处理
function handleCancelDownload() {
  if (mockDownloadTimer) {
    clearInterval(mockDownloadTimer)
    mockDownloadTimer = null
  }
  
  downloadStatus.value = 'cancelled'
  setTimeout(() => {
    showDownloadProgress.value = false
  }, 1000)
}
</script>

<style lang="scss" scoped>
.download-progress-demo {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 32rpx;
}

.demo-header {
  margin-bottom: 48rpx;
}

.demo-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #262626;
  line-height: 64rpx;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 48rpx;
}

.demo-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
}

.demo-section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #262626;
  line-height: 44rpx;
  margin-bottom: 24rpx;
}

.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.demo-btn {
  height: 88rpx;
  background: #ff0043;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &--secondary {
    background: #f7f7f7;
  }
}

.demo-btn-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 44rpx;
  
  .demo-btn--secondary & {
    color: #595959;
  }
}
</style> 