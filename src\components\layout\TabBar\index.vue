<template>
  <view class="tabbar" :class="{ 'tabbar--hidden': !visible }">
    <view class="tabbar__content">
      <view 
        v-for="(tab, index) in tabList" 
        :key="index"
        class="tabbar__item interactive"
        :class="{ 'tabbar__item--active': currentIndex === index }"
        @tap="handleTabClick(tab, index)"
      >
        <view class="tabbar__item-icon">
          <image 
            v-if="tab.iconSrc"
            :src="currentIndex === index ? (tab.selectedIconSrc || tab.iconSrc) : tab.iconSrc"
            mode="aspectFit"
            class="tabbar__item-icon-image"
          />
          <text 
            v-else
            class="tabbar__item-icon-text"
            :style="{ color: currentIndex === index ? activeColor : inactiveColor }"
          >
            {{ currentIndex === index ? (tab.selectedIcon || tab.icon) : tab.icon }}
          </text>
        </view>
        
        <text 
          class="tabbar__item-text"
          :style="{ color: currentIndex === index ? activeColor : inactiveColor }"
        >
          {{ tab.text }}
        </text>
        
        <!-- 徽章 -->
        <view v-if="tab.badge" class="tabbar__item-badge">
          <text class="tabbar__item-badge-text">{{ tab.badge }}</text>
        </view>
        
        <!-- 红点 -->
        <view v-if="tab.dot && !tab.badge" class="tabbar__item-dot"></view>
      </view>
    </view>
    <view class="tabbar__safe-area"></view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
// 移除vue-i18n导入，使用uni-app官方国际化方案

// Props
const props = defineProps({
  currentIndex: {
    type: Number,
    default: 0
  },
  tabList: {
    type: Array,
    default: () => [
      {
        text: '首页',
        iconSrc: '/static/icons/tab/tab_home.png',
        selectedIconSrc: '/static/icons/tab/tab_home_active.png',
        pagePath: '/pages/index/index'
      },
      {
        text: '穿版',
        iconSrc: '/static/icons/tab/tab_template.png',
        selectedIconSrc: '/static/icons/tab/tab_template_active.png',
        pagePath: '/pages/template/index'
      },
      {
        text: '爆款模版',
        iconSrc: '/static/icons/tab/tab_trending.png',
        selectedIconSrc: '/static/icons/tab/tab_trending_active.svg',
        pagePath: '/pages/trending/index'
      },
      {
        text: '创作历史',
        iconSrc: '/static/icons/tab/tab_history.png',
        selectedIconSrc: '/static/icons/tab/tab_history_active.png',
        pagePath: '/pages/history/index'
      },
      {
        text: '我的',
        iconSrc: '/static/icons/tab/tab_profile.png',
        selectedIconSrc: '/static/icons/tab/tab_profile_active.svg',
        pagePath: '/pages/profile/index'
      }
    ]
  },
  activeColor: {
    type: String,
    default: '#4A90E2'
  },
  inactiveColor: {
    type: String,
    default: '#999999'
  },
  backgroundColor: {
    type: String,
    default: '#ffffff'
  },
  borderColor: {
    type: String,
    default: '#e5e5e5'
  },
  visible: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['change', 'tab-click'])

// Composables
// 注意：如果需要使用翻译功能，请从全局属性获取 $t

// Methods
const handleTabClick = (tab, index) => {
  if (props.currentIndex === index) return
  
  emit('change', index, tab)
  emit('tab-click', tab, index)
  
  // 默认跳转行为
  if (tab.pagePath) {
    uni.switchTab({
      url: tab.pagePath
    })
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: v-bind(backgroundColor);
  border-top: 1rpx solid v-bind(borderColor);
  transition: transform $transition-normal;
  
  &--hidden {
    transform: translateY(100%);
  }
  
  &__content {
    display: flex;
    align-items: center;
    height: $tabbar-height;
    padding: 0 $spacing-sm;
  }
  
  &__item {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xs;
    border-radius: $radius-base;
    transition: all $transition-fast;
    
    &:active {
      background-color: rgba($neutral-500, 0.05);
    }
    
    &--active {
      .tabbar__item-icon {
        transform: scale(1.1);
      }
      
      .tabbar__item-text {
        font-weight: $font-weight-semibold;
      }
    }
    
    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      margin-bottom: $spacing-xs;
      transition: transform $transition-fast;
      
      &-image {
        width: 40rpx;
        height: 40rpx;
      }
      
      &-text {
        font-size: $font-size-xl;
        line-height: 1;
      }
    }
    
    &-text {
      font-size: $font-size-xs;
      line-height: $line-height-tight;
      text-align: center;
      @include text-ellipsis;
      max-width: 100%;
    }
    
    &-badge {
      position: absolute;
      top: 4rpx;
      right: 20rpx;
      background-color: $accent-color;
      color: $text-inverse;
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;
      padding: 2rpx $spacing-xs;
      border-radius: $radius-full;
      min-width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: scale(0.8);
      
      &-text {
        line-height: 1;
        white-space: nowrap;
      }
    }
    
    &-dot {
      position: absolute;
      top: 8rpx;
      right: 32rpx;
      width: 16rpx;
      height: 16rpx;
      background-color: $accent-color;
      border-radius: $radius-full;
      transform: scale(0.8);
    }
  }
  
  &__safe-area {
    height: $safe-area-inset-bottom;
    background-color: inherit;
  }
}

// 响应式适配
@media (max-width: 400px) {
  .tabbar {
    &__item {
      padding: $spacing-xs $spacing-xs;
      
      &-icon {
        width: 40rpx;
        height: 40rpx;
        
        &-image {
          width: 32rpx;
          height: 32rpx;
        }
        
        &-text {
          font-size: $font-size-lg;
        }
      }
      
      &-text {
        font-size: 20rpx;
      }
    }
  }
}

// 横屏适配
@media (orientation: landscape) {
  .tabbar {
    &__content {
      height: 60rpx;
      padding: 0 $spacing-base;
    }
    
    &__item {
      flex-direction: row;
      
      &-icon {
        width: 36rpx;
        height: 36rpx;
        margin-bottom: 0;
        margin-right: $spacing-xs;
        
        &-image {
          width: 28rpx;
          height: 28rpx;
        }
        
        &-text {
          font-size: $font-size-base;
        }
      }
      
      &-text {
        font-size: $font-size-sm;
      }
    }
  }
}

// 暗黑模式适配
@media (prefers-color-scheme: dark) {
  .tabbar {
    background-color: $bg-dark;
    border-top-color: $border-dark;
    
    &__item {
      &:active {
        background-color: rgba($text-inverse, 0.05);
      }
    }
  }
}
</style> 