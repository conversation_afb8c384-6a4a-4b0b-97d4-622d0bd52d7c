## 国际化翻译实现说明

### 实现过程

1. **依赖安装**
   ```bash
   pnpm add vue-i18n@9.1.9
   ```

2. **语言文件创建**
   在`src/`目录下创建语言文件：
   - `zh-Hans.json` - 中文简体
   - `en.json` - 英文
   - `zh-Hant.json` - 繁体中文

3. **main.js配置**
   ```javascript
   import { createI18n } from 'vue-i18n'
   import en from './en.json'
   import zhHans from './zh-Hans.json'
   import zhHant from './zh-Hant.json'
   
   const messages = { en, 'zh-Hans': zhHans, 'zh-Hant': zhHant }
   const i18n = createI18n({
     locale: uni.getLocale(),
     messages
   })
   
   app.use(i18n)
   ```

### 使用方法

#### 页面中使用
```vue
<template>
  <text>{{ $t('index.title') }}</text>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { locale } = useI18n()

// 切换语言
const switchLanguage = (lang) => {
  locale.value = lang
  uni.setLocale(lang)
}
</script>
```

#### 组件中使用
```vue
<template>
  <text>{{ computedText }}</text>
</template>

<script setup>
import { computed, getCurrentInstance } from 'vue'

const instance = getCurrentInstance()

const computedText = computed(() => {
  return instance.proxy.$t('banner.title')
})
</script>
```

### 语言文件格式
```json
{
  "index": {
    "title": "视频创作",
    "startCreate": "开始创作"
  },
  "banner": {
    "title": "形象、声音一键克隆",
    "button": "新人免费，抢先定制"
  }
}
```

### 支持语言
- `zh-Hans` - 中文简体（默认）
- `en` - 英文
- `zh-Hant` - 繁体中文

### 注意事项
1. 翻译key使用嵌套结构，如`index.title`
2. 页面中直接使用`$t()`，组件中使用`getCurrentInstance().proxy.$t()`
3. 语言切换后会自动重新渲染界面
4. 系统会自动获取设备语言作为默认语言