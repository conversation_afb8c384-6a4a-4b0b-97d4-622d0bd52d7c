<template>
  <view class="member-team">
    <!-- 顶部导航栏 -->
    <view class="member-team__navbar-wrapper">
      <view class="member-team__status-bar"></view>
      <view class="member-team__navbar">
        <view class="member-team__back" @tap="goBack">
          <image class="member-team__back-icon" src="@/asset/img/create/ai_setting_icon.png" />
        </view>
        <view class="member-team__navbar-btns">
          <view class="member-team__case-btn" @tap="goToCase">
            <text class="member-team__navbar-icon">📊</text>
            <text class="member-team__case-text">案例</text>
          </view>
          <view class="member-team__service-btn" @tap="contactService">
            <text class="member-team__navbar-icon">🎧</text>
            <text class="member-team__service-text">客服</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 页面内容区 - 使用scroll-view确保可滚动 -->
    <scroll-view 
      class="member-team__content" 
      scroll-y="true" 
      :enable-flex="true"
      :show-scrollbar="false"
      :enable-back-to-top="true"
    >
      <!-- AI卡片区域 -->
      <view class="member-team__ai-wrapper">
        <view class="member-team__ai-card">
          <view class="member-team__logo">
            <text class="member-team__logo-text">旺剪</text>
            <view class="member-team__logo-tag">
              <text class="member-team__logo-tag-text">团队版</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="member-team__content-inner">
        <!-- 会员套餐选项 -->
        <view class="member-team__plans">
          <view 
            class="member-team__plan-item" 
            :class="{'member-team__plan-item--active': activePlan === 0}"
            @tap="selectPlan(0)"
          >
            <view class="member-team__plan-content">
              <view class="member-team__plan-title">
                <text class="member-team__plan-title-text">年会员</text>
              </view>
              <view class="member-team__plan-price">
                <text class="member-team__plan-price-symbol">¥</text>
                <text class="member-team__plan-price-value">398</text>
              </view>
              <view class="member-team__plan-original">
                <text class="member-team__plan-original-label">原价 </text><text class="member-team__plan-original-text">¥748</text>
              </view>
            </view>
            <view class="member-team__plan-tag" :class="{'member-team__plan-tag--active': activePlan === 0}">
              <text class="member-team__plan-tag-text">基础版</text>
            </view>
          </view>
          
          <view 
            class="member-team__plan-item" 
            :class="{'member-team__plan-item--active': activePlan === 1}"
            @tap="selectPlan(1)"
          >
            <view class="member-team__plan-content">
              <view class="member-team__plan-title">
                <text class="member-team__plan-title-text">年会员</text>
              </view>
              <view class="member-team__plan-price">
                <text class="member-team__plan-price-symbol">¥</text>
                <text class="member-team__plan-price-value">2998</text>
              </view>
              <view class="member-team__plan-original">
                <text class="member-team__plan-original-label">原价 </text><text class="member-team__plan-original-text">¥4798</text>
              </view>
            </view>
            <view class="member-team__plan-tag" :class="{'member-team__plan-tag--active': activePlan === 1}">
              <text class="member-team__plan-tag-text">专业版</text>
            </view>
          </view>
          
          <view 
            class="member-team__plan-item" 
            :class="{'member-team__plan-item--active': activePlan === 2}"
            @tap="selectPlan(2)"
          >
            <view class="member-team__plan-content">
              <view class="member-team__plan-title">
                <text class="member-team__plan-title-text">6个月会员</text>
              </view>
              <view class="member-team__plan-price">
                <text class="member-team__plan-price-symbol">¥</text>
                <text class="member-team__plan-price-value">6800</text>
              </view>
              <view class="member-team__plan-original">
                <text class="member-team__plan-original-label">原价 </text><text class="member-team__plan-original-text">¥13600</text>
              </view>
            </view>
            <view class="member-team__plan-tag" :class="{'member-team__plan-tag--active': activePlan === 2}">
              <text class="member-team__plan-tag-text">团队版</text>
            </view>
          </view>
        </view>
        
        <!-- 会员权益 -->
        <view class="member-team__benefits-section">
          <view class="member-team__benefits-header">
            <text class="member-team__benefits-title">会员权益</text>
            <view class="member-team__benefits-compare" @tap="compareBenefits">
              <text class="member-team__benefits-compare-text">权益对比</text>
              <image class="member-team__benefits-compare-icon" src="@/asset/img/profile/profile-menu-arrow.png" />
            </view>
          </view>
          
          <!-- 权益列表 -->
          <view class="member-team__benefit-list">
            <view class="member-team__benefit-item">
              <view class="member-team__benefit-icon member-team__benefit-icon--person">
                <text class="member-team__benefit-icon-text">👤</text>
              </view>
              <view class="member-team__benefit-info">
                <text class="member-team__benefit-text">专业版数字人·无限定制</text>
              </view>
            </view>
            
            <view class="member-team__benefit-item">
              <view class="member-team__benefit-icon member-team__benefit-icon--voice">
                <text class="member-team__benefit-icon-text">🔊</text>
              </view>
              <view class="member-team__benefit-info">
                <text class="member-team__benefit-text">专属声音·无限克隆</text>
              </view>
            </view>
            
            <view class="member-team__benefit-item">
              <view class="member-team__benefit-icon member-team__benefit-icon--power">
                <text class="member-team__benefit-icon-text">⚡</text>
              </view>
              <view class="member-team__benefit-info">
                <text class="member-team__benefit-text">30000 团队算力·高效生成</text>
                <text class="member-team__benefit-subtext">约 500 分钟时长（约 1000 条 30 秒视频）, <text>可加购</text></text>
              </view>
            </view>
            
            <view class="member-team__benefit-item">
              <view class="member-team__benefit-icon member-team__benefit-icon--cloud">
                <text class="member-team__benefit-icon-text">☁️</text>
              </view>
              <view class="member-team__benefit-info">
                <text class="member-team__benefit-text">100GB 团队素材库云空间</text>
              </view>
            </view>
            
            <view class="member-team__benefit-item">
              <view class="member-team__benefit-icon member-team__benefit-icon--team">
                <text class="member-team__benefit-icon-text">👥</text>
              </view>
              <view class="member-team__benefit-info">
                <text class="member-team__benefit-text">8 个团队成员·高效协作</text>
              </view>
            </view>
            
            <view class="member-team__benefit-item">
              <view class="member-team__benefit-icon member-team__benefit-icon--4k">
                <text class="member-team__benefit-icon-text">4K</text>
              </view>
              <view class="member-team__benefit-info">
                <text class="member-team__benefit-text">4K 高清数字人</text>
                <view class="member-team__benefit-tag">
                  <text class="member-team__benefit-tag-text">4K 定制</text>
                  <text class="member-team__benefit-tag-icon">›</text>
                </view>
              </view>
            </view>
            
            <view class="member-team__benefit-item">
              <view class="member-team__benefit-icon member-team__benefit-icon--support">
                <text class="member-team__benefit-icon-text">📋</text>
              </view>
              <view class="member-team__benefit-info">
                <text class="member-team__benefit-text">14 天全流程陪跑</text>
                <view class="member-team__benefit-tag">
                  <text class="member-team__benefit-tag-text">了解权益</text>
                  <text class="member-team__benefit-tag-icon">›</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 底部购买区域 -->
        <view class="member-team__footer">
          <view class="member-team__purchase-btn" @tap="purchaseNow">
            <text class="member-team__purchase-btn-text">立即购买</text>
          </view>
          <view class="member-team__agreement">
            <text class="member-team__agreement-text">开通即同意</text>
            <text class="member-team__agreement-link" @tap="showAgreement">《旺剪服务协议》</text>
          </view>
        </view>
        
        <!-- 底部空间，确保内容可以滚动足够距离 -->
        <view class="member-team__bottom-space"></view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 激活的套餐
const activePlan = ref(2); // 默认选中团队版

// 方法
const goBack = () => {
  uni.navigateBack();
};

const goToCase = () => {
  // 跳转到案例页面
  uni.showToast({
    title: '查看案例',
    icon: 'none'
  });
};

const contactService = () => {
  // 联系客服
  uni.showToast({
    title: '联系客服',
    icon: 'none'
  });
};

const selectPlan = (index) => {
  activePlan.value = index;
};

const compareBenefits = () => {
  // 跳转到权益对比页面
  uni.showToast({
    title: '查看权益对比',
    icon: 'none'
  });
};

const purchaseNow = () => {
  // 执行购买流程
  uni.showToast({
    title: '开始购买流程',
    icon: 'none'
  });
};

const showAgreement = () => {
  // 显示服务协议
  uni.showToast({
    title: '查看服务协议',
    icon: 'none'
  });
};

onMounted(() => {
  // 页面加载后，确保内容区域可滚动
  console.log('团队版会员页面加载完成');
});
</script>

<style lang="scss" scoped>
.member-team {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
  background-color: #FFFFFF;
  position: relative;
  overflow: hidden;
}

.member-team__status-bar {
  height: var(--status-bar-height);
  width: 100%;
}

.member-team__navbar-wrapper {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #000000;
}

.member-team__navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 32rpx;
  height: 88rpx;
}

.member-team__back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.member-team__back-icon {
  width: 16rpx;
  height: 24rpx;
}

.member-team__navbar-btns {
  display: flex;
  align-items: center;
}

.member-team__case-btn, .member-team__service-btn {
  padding: 0 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 32rpx;
}

.member-team__navbar-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.member-team__case-text, .member-team__service-text {
  font-size: 24rpx;
  color: #FFFFFF;
}

.member-team__content {
  flex: 1;
  position: relative;
  width: 100%;
  height: calc(100vh - var(--status-bar-height) - 88rpx); /* 视口高度减去导航栏高度 */
  margin-top: calc(var(--status-bar-height) + 88rpx);
  box-sizing: border-box;
  overflow-y: auto;
}

/* AI卡片区域 */
.member-team__ai-wrapper {
  background-color: #000000;
  padding-bottom: 0; /* 移除底部padding */
}

.member-team__ai-card {
  margin: 0 24rpx; /* 增加左右边距 */
  padding: 58rpx 50rpx; /* 调整高度 */
  background: linear-gradient(to right, #1A1A1A, #262626);
  border-radius: 20rpx 20rpx 0 0;
}

.member-team__logo {
  display: flex;
  align-items: center;
}

.member-team__logo-text {
  font-size: 38rpx;
  font-weight: bold;
  color: #FAE9C0; /* 淡金色 */
  margin-right: 16rpx;
}

.member-team__logo-tag {
  background: linear-gradient(to right, #fff7e5, #ffd1b6); /* 添加左到右渐变效果 */
  padding: 0 10rpx;
  border-radius: 6rpx;
}

.member-team__logo-tag-text {
  font-size: 20rpx;
  color: #000000;
  font-weight: bold; /* 加粗字体 */
}

.member-team__content-inner {
  padding-bottom: 40rpx; /* 给底部留出空间 */
}

/* 会员套餐选项 */
.member-team__plans {
  display: flex;
  justify-content: space-between;
  padding: 0 16rpx; /* 减少左右内边距，给卡片间留更多空间 */
  margin: 30rpx 0 40rpx;
  width: 100%;
  box-sizing: border-box;
  padding-right: 60rpx;
}

.member-team__plan-item {
  flex: 0 0 calc(33.33% - 20rpx); /* 减小卡片宽度，增加间距 */
  display: flex;
  flex-direction: column;
  margin: 0 10rpx; /* 增加卡片间距 */
  border-radius: 16rpx;
  overflow: hidden;
//   box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 4rpx solid #EEEEEE;
  box-sizing: border-box;
}

.member-team__plan-content {
  padding: 12rpx 16rpx 16rpx; /* 增加底部内边距，为原价留出空间 */
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.member-team__plan-item--active {
  border: 4rpx solid #000000; /* 增加边框厚度 */
}

.member-team__plan-title {
  margin-bottom: 8rpx; /* 减少标题底部间距 */
  text-align: center;
}

.member-team__plan-title-text {
  font-size: 24rpx;
  color: #333333;
}

.member-team__plan-item--active .member-team__plan-title-text {
  color: #333333; /* 选中时保持黑色 */
}

.member-team__plan-price {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4rpx; /* 进一步减少底部边距 */
  height: 54rpx; /* 减小高度 */
}

.member-team__plan-price-symbol {
  font-size: 24rpx;
  color: #333333;
  font-weight: bold;
  margin-top: 10rpx;
}

.member-team__plan-price-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1;
}

.member-team__plan-original {
  height: 24rpx;
  margin-bottom: 16rpx; /* 增加与底部的间距 */
}

.member-team__plan-original-label {
  font-size: 20rpx;
  color: #999999;
}

.member-team__plan-original-text {
  font-size: 20rpx;
  color: #999999;
  text-decoration: line-through;
}

.member-team__plan-tag {
  width: 100%;
  padding: 10rpx 0; /* 减少上下内边距 */
  text-align: center;
  background-color: #f0f3f4;
}

.member-team__plan-tag--active {
  background-color: #000000;
}

.member-team__plan-tag-text {
  font-size: 22rpx;
  color: #333333;
}

.member-team__plan-tag--active .member-team__plan-tag-text {
  color: #FFD480; /* 浅橙色 */
}

/* 会员权益部分 */
.member-team__benefits-section {
  background-color: #FFFFFF;
  padding: 20rpx 32rpx 30rpx;
}

.member-team__benefits-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.member-team__benefits-title {
  font-size: 32rpx; /* 增大会员权益字体 */
  font-weight: bold;
  color: #333333;
}

.member-team__benefits-compare {
  display: flex;
  align-items: center;
}

.member-team__benefits-compare-text {
  font-size: 22rpx;
  color: #999999;
  margin-right: 6rpx;
}

.member-team__benefits-compare-icon {
  width: 14rpx;
  height: 20rpx;
  margin-left: 6rpx;
}

.member-team__benefit-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 会员权益下图标与文字居中对齐 */
.member-team__benefit-item {
  display: flex;
  align-items: center; /* 居中对齐 */
}

.member-team__benefit-icon {
  width: 54rpx;
  height: 54rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  background-color: #F5F5F5;
  flex-shrink: 0;
}

.member-team__benefit-icon-text {
  font-size: 28rpx;
}

.member-team__benefit-icon--4k {
  background-color: #D4AF37;
}

.member-team__benefit-info {
  flex: 1;
  display: flex;
  flex-direction: column; /* 默认为列布局 */
  align-items: flex-start; /* 左对齐 */
}

.member-team__benefit-text {
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 6rpx; /* 恢复底部间距，用于上下布局的情况 */
}

.member-team__benefit-subtext {
  font-size: 20rpx;
  color: #999999;
}

.member-team__benefit-subtext span {
  color: #333;
}

.member-team__benefit-tag {
  display: inline-flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 4rpx 12rpx;
  border-radius: 100rpx;
  margin-top: 0; /* 移除顶部间距 */
}

.member-team__benefit-tag-text {
  font-size: 18rpx;
  color: #333;
  margin-right: 6rpx;
}

.member-team__benefit-tag-icon {
  font-size: 18rpx;
  color: #666666;
}

/* 特殊处理30000团队算力的权益项 */
.member-team__benefit-icon--power + .member-team__benefit-info {
  flex-direction: column; /* 恢复为列布局 */
  align-items: flex-start; /* 左对齐 */
  justify-content: flex-start; /* 从上往下排列 */
}

/* 4K高清数字人和14天全流程陪跑使用行内排列 */
.member-team__benefit-icon--4k + .member-team__benefit-info,
.member-team__benefit-icon--support + .member-team__benefit-info {
  flex-direction: row; /* 行布局 */
  align-items: center; /* 垂直居中 */
  justify-content: flex-start; /* 从左到右排列 */
}

/* 修改4K定制和了解权益的标签背景为渐变色 */
.member-team__benefit-icon--4k + .member-team__benefit-info .member-team__benefit-tag,
.member-team__benefit-icon--support + .member-team__benefit-info .member-team__benefit-tag {
  margin-left: 10rpx;
  margin-top: 0;
  background: linear-gradient(to right, #fff7e5, #ffd1b6); /* 添加左到右渐变效果 */
  border-radius: 100rpx;
  padding: 4rpx 12rpx;
}

.member-team__benefit-icon--4k + .member-team__benefit-info .member-team__benefit-tag-text,
.member-team__benefit-icon--support + .member-team__benefit-info .member-team__benefit-tag-text {
  color: #FF8C00; /* 橙色文字 */
  font-size: 18rpx;
}

.member-team__benefit-icon--4k + .member-team__benefit-info .member-team__benefit-tag-icon,
.member-team__benefit-icon--support + .member-team__benefit-info .member-team__benefit-tag-icon {
  color: #FF8C00; /* 橙色图标 */
  font-size: 18rpx;
}

.member-team__bottom-space {
  height: 40rpx;
}

/* 底部购买区域 */
.member-team__footer {
  margin-top: 40rpx;
  padding: 24rpx 32rpx 30rpx;
  background-color: #FFFFFF;
}

.member-team__purchase-btn {
  width: 100%;
  height: 88rpx;
  background-color: #000000;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.member-team__purchase-btn:active {
  opacity: 0.9;
}

.member-team__purchase-btn-text {
  color: #FFFFFF;
  font-size: 30rpx;
  font-weight: 500;
}

.member-team__agreement {
  text-align: center;
  margin-top: 16rpx;
}

.member-team__agreement-text {
  font-size: 20rpx;
  color: #999999;
}

.member-team__agreement-link {
  font-size: 20rpx;
  color: #666666;
}
</style> 