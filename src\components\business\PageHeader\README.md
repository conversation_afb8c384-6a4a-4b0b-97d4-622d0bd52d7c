# PageHeader 页面头部组件

## 组件描述

PageHeader 是一个固定在页面顶部的头部导航组件，提供标题显示、返回按钮和右侧操作按钮功能。适用于各种页面的统一头部样式。

## 功能特性

- 🎯 **固定定位** - 固定在页面顶部，始终可见
- 🎨 **统一样式** - 黑色渐变背景，白色文字
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🔄 **交互事件** - 支持返回、右侧按钮和整体点击事件
- 🎭 **灵活配置** - 可配置标题、返回按钮和右侧按钮

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '' | 页面标题 |
| rightText | String | '' | 右侧按钮文字 |
| showBack | Boolean | false | 是否显示返回按钮 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| back | - | 返回按钮点击时触发 |
| rightClick | - | 右侧按钮点击时触发 |
| click | - | 头部整体点击时触发 |

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <PageHeader 
      title="我的页面"
      @back="handleBack"
      @click="handleHeaderClick"
    />
    <!-- 页面内容 -->
  </view>
</template>

<script setup>
import PageHeader from '@/components/business/PageHeader/index.vue'

const handleBack = () => {
  console.log('返回上一页')
  // 组件会自动调用 uni.navigateBack()
}

const handleHeaderClick = () => {
  console.log('点击头部')
}
</script>
```

### 带右侧按钮

```vue
<template>
  <view>
    <PageHeader 
      title="设置"
      right-text="保存"
      :show-back="true"
      @back="handleBack"
      @right-click="handleSave"
    />
    <!-- 设置页面内容 -->
  </view>
</template>

<script setup>
import PageHeader from '@/components/business/PageHeader/index.vue'

const handleBack = () => {
  console.log('返回上一页')
}

const handleSave = () => {
  console.log('保存设置')
  uni.showToast({
    title: '保存成功',
    icon: 'success'
  })
}
</script>
```

### 自定义事件处理

```vue
<template>
  <view>
    <PageHeader 
      title="编辑资料"
      right-text="完成"
      :show-back="true"
      @back="handleBack"
      @right-click="handleComplete"
    />
    <!-- 编辑表单 -->
  </view>
</template>

<script setup>
import PageHeader from '@/components/business/PageHeader/index.vue'

const handleBack = () => {
  // 检查是否有未保存的更改
  uni.showModal({
    title: '提示',
    content: '有未保存的更改，确定要离开吗？',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    }
  })
}

const handleComplete = () => {
  // 保存并返回
  uni.showLoading({ title: '保存中...' })
  
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    uni.navigateBack()
  }, 1000)
}
</script>
```

### 动态标题

```vue
<template>
  <view>
    <PageHeader 
      :title="pageTitle"
      right-text="分享"
      @right-click="handleShare"
    />
    <!-- 页面内容 -->
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import PageHeader from '@/components/business/PageHeader/index.vue'

const currentTab = ref(0)
const tabs = ['推荐', '热门', '最新']

const pageTitle = computed(() => {
  return tabs[currentTab.value] || '首页'
})

const handleShare = () => {
  uni.showActionSheet({
    itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
    success: (res) => {
      console.log('选择分享方式:', res.tapIndex)
    }
  })
}
</script>
```

## 样式定制

组件使用 SCSS 编写，主要样式特点：

- 高度：`88rpx` (44px)
- 背景：黑色渐变 `linear-gradient(180deg, #000000 0%, #1a1a1a 100%)`
- 标题字体：`36rpx`，白色，居中
- 返回按钮：圆形背景，半透明白色
- 右侧按钮：圆角背景，半透明白色

## 布局结构

```
PageHeader
├── 左侧区域 (120rpx)
│   └── 返回按钮 (可选)
├── 中间区域 (flex: 1)
│   └── 标题文字
└── 右侧区域 (120rpx)
    └── 操作按钮 (可选)
```

## 注意事项

1. 组件固定在顶部，注意页面内容需要预留顶部空间
2. 返回按钮会自动调用 `uni.navigateBack()`
3. 标题文字会自动居中显示
4. 右侧按钮文字建议不超过 4 个字符
5. 组件使用 `z-index: 100`，确保在其他元素之上

## 最佳实践

1. **标题简洁**：保持标题简洁明了，建议不超过 8 个字符
2. **右侧按钮**：使用动词性词汇，如"保存"、"完成"、"分享"
3. **返回逻辑**：在返回前检查是否有未保存的更改
4. **响应式**：在不同设备上测试显示效果
5. **一致性**：在整个应用中保持头部样式的一致性

## 与其他组件配合

PageHeader 通常与以下组件配合使用：

- **BottomTabBar**: 底部标签栏
- **CustomNavBar**: 自定义导航栏
- **TabBar**: 标签栏组件

确保页面内容在头部和底部之间正确显示。 