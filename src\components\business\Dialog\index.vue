<template>
  <view class="dialog">
    <view class="dialog__mask" v-if="visible" />
    <view class="dialog__container" v-if="visible">
      <!-- 标题栏 -->
      <view class="dialog__header flex-row justify-between">
        <text class="dialog__title">{{ title }}</text>
        <image
          class="dialog__close"
          :src="closeIcon"
          @click="handleClose"
          mode="aspectFit"
        />
      </view>
      <!-- 功能区 -->
      <view class="dialog__features" v-if="features && features.length === 2">
  <view class="dialog__feature-left flex-row" @click="() => handleFeatureClick(features[0])">
    <image v-if="features[0].icon" :src="features[0].icon" class="dialog__feature-icon" mode="aspectFit" />
    <text class="dialog__feature-text">{{ features[0].text }}</text>
  </view>
  <text class="dialog__feature-right" @click="() => handleFeatureClick(features[1])">{{ features[1].text }}</text>
</view>
      <!-- 主内容区 -->
      <view class="dialog__content">
        <slot name="content">
          <text class="dialog__main-text">{{ content }}</text>
        </slot>
      </view>
      <!-- 扩展功能区 -->
      <view class="dialog__extra flex-row justify-between" v-if="extraFeatures && extraFeatures.length">
        <view
          v-for="(feature, idx) in extraFeatures"
          :key="feature.key || idx"
          class="dialog__extra-btn flex-row"
          @click="() => handleFeatureClick(feature)"
        >
          <image :src="feature.icon" class="dialog__extra-icon" mode="aspectFit" />
          <text class="dialog__extra-text">{{ feature.text }}</text>
        </view>
      </view>
      <!-- 底部操作按钮区 -->
      <view class="dialog__actions flex-row justify-between">
        <view
          v-for="(action, idx) in actions"
          :key="action.key || idx"
          :class="['dialog__action-btn', action.type === 'primary' ? 'dialog__action-btn--primary' : 'dialog__action-btn--default']"
          @click="() => handleAction(action)"
        >
          <text class="dialog__action-text">{{ action.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';

const props = defineProps<{
  visible: boolean;
  title: string;
  content: string;
  features?: Array<{ key?: string; icon: string; text: string }>;
  extraFeatures?: Array<{ key?: string; icon: string; text: string }>;
  actions: Array<{ key?: string; text: string; type?: 'primary' | 'default' }>;
  closeIcon?: string;
}>();

const emits = defineEmits(['close', 'confirm', 'cancel', 'featureClick']);

const handleClose = () => {
  emits('close');
};
const handleAction = (action: { key?: string; text: string; type?: 'primary' | 'default' }) => {
  if (action.type === 'primary') {
    emits('confirm', action);
  } else {
    emits('cancel', action);
  }
};
const handleFeatureClick = (feature: { key?: string; icon: string; text: string }) => {
  emits('featureClick', feature);
};
const onMaskClick = () => {
  emits('close');
};

const closeIcon = computed(() =>
  props.closeIcon || '/src/asset/dialog/dialog-close.png'
);
</script>

<style lang="scss" scoped>
.dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  &__mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 101;
  }
  &__container {
    position: relative;
    z-index: 102;
    width: 630rpx;
    max-width: 90vw;
    background: url('/src/asset/dialog/dialog-bg.png') 100% no-repeat;
    background-size: 100% 100%;
    border-radius: 32rpx;
    padding: 36rpx 36rpx 44rpx 36rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
  &__header {
    width: 100%;
    margin-left: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    position: relative;
    .dialog__title {
      flex: 1;
      text-align: center;
    }
    .dialog__close {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  &__title {
    color: #fff;
    font-size: 36rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    line-height: 48rpx;
  }
  &__close {
    width: 28rpx;
    height: 28rpx;
    margin: 8rpx 0 12rpx 0;
  }
  &__features {
    background: #212124;
    border-radius: 16rpx;
    width: 100%;
    margin: 34rpx 0 0 0;
    padding: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
  }
  &__feature-left {
    background: #383839;
    border-radius: 12rpx;
    padding: 20rpx 80rpx 20rpx 80rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  &__feature-icon {
    width: 32rpx;
    height: 32rpx;
    margin: 4rpx 0 4rpx 0;
  }
  &__feature-text {
    color: #fff;
    font-size: 28rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    white-space: nowrap;
    line-height: 40rpx;
    margin-left: 8rpx;
  }
  &__feature-right {
    // background: #383839;
    border-radius: 12rpx;
    padding: 20rpx 80rpx 20rpx 80rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #fff;
    font-size: 28rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    white-space: nowrap;
    line-height: 40rpx;
    margin: 0;
    height: auto;
  }
  &__feature-btn {
    width: 120rpx;
    margin: 12rpx 0 12rpx 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  &__feature-icon {
    width: 28rpx;
    height: 28rpx;
    margin: 4rpx 0 4rpx 0;
  }
  &__feature-text {
    color: #fff;
    font-size: 26rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    white-space: nowrap;
    line-height: 40rpx;
  }
  &__content {
    background: #212124;
    border-radius: 24rpx;
    margin-top: 30rpx;
    padding: 24rpx 20rpx 24rpx 24rpx;
    min-height: 180rpx;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }
  &__main-text {
    width: 100%;
    color: #fff;
    font-size: 26rpx;
    font-weight: normal;
    text-align: left;
    line-height: 40rpx;
  }
  &__extra {
    width: 100%;
    margin: 20rpx 0 0 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    gap: 12rpx;
    box-sizing: border-box;
  }
  &__extra-btn {
    background: #383839;
    border-radius: 12rpx;
    padding: 10rpx 20rpx 10rpx 16rpx;
    display: flex;
    flex-direction: row;
    align-items: center;

    box-sizing: border-box;
    overflow: hidden;
    gap: 4rpx;
  }
  &__extra-text {
    color: #fff;
    font-size: 22rpx;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 32rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }
  &__extra-icon {
    width: 28rpx;
    height: 20rpx;
    display: block;
  }
  &__extra-text {
    color: #fff;
    font-size: 22rpx;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 32rpx;
  }
  &__actions {
    width: 100%;
    margin-top: 32rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 24rpx;
    gap: 16rpx;
  }
  &__action-btn {
    &--default {
      background: url('/src/asset/dialog/dialog-cancel-bg.png') 100% no-repeat;
      background-size: 100% 100%;
      padding: 18rpx 80rpx 18rpx 80rpx;
      border-radius: 24rpx;
    }
    &--primary {
      background: #ff0043;
      border-radius: 24rpx;
      padding: 18rpx 80rpx 18rpx 80rpx;
    }
  }
  &__action-text {
    color: #fff;
    font-size: 30rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    line-height: 44rpx;
  }
}
</style>
