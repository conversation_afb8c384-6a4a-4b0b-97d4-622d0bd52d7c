import { createPinia } from 'pinia'

// 创建Pinia实例
const pinia = createPinia()

// 持久化插件
const persistPlugin = ({ store }) => {
  // 需要持久化的状态列表
  const persistStores = ['user', 'app', 'create']
  
  if (persistStores.includes(store.$id)) {
    // 从本地存储恢复状态
    const savedState = uni.getStorageSync(`store_${store.$id}`)
    if (savedState) {
      store.$patch(savedState)
    }
    
    // 监听状态变化，自动保存到本地存储
    store.$subscribe((mutation, state) => {
      uni.setStorageSync(`store_${store.$id}`, state)
    }, { detached: true })
  }
}

// 添加持久化插件
pinia.use(persistPlugin)

export default pinia 