// 旺剪App 设计令牌
// ================================

// 主题色彩
$primary-color: #4A90E2;
$primary-light: #6BA3E8;
$primary-dark: #357ABD;

$secondary-color: #50C878;
$secondary-light: #6FD891;
$secondary-dark: #3FA05F;

$accent-color: #FF6B6B;
$accent-light: #FF8A8A;
$accent-dark: #E55555;

// 中性色彩
$neutral-50: #F9FAFB;
$neutral-100: #F3F4F6;
$neutral-200: #E5E7EB;
$neutral-300: #D1D5DB;
$neutral-400: #9CA3AF;
$neutral-500: #6B7280;
$neutral-600: #4B5563;
$neutral-700: #374151;
$neutral-800: #1F2937;
$neutral-900: #111827;

// 功能色彩
$success-color: #10B981;
$warning-color: #F59E0B;
$error-color: #EF4444;
$info-color: #3B82F6;

// 文本色彩
$text-primary: $neutral-900;
$text-secondary: $neutral-600;
$text-tertiary: $neutral-400;
$text-inverse: #FFFFFF;
$text-link: $primary-color;

// 背景色彩
$bg-primary: #FFFFFF;
$bg-secondary: $neutral-50;
$bg-tertiary: $neutral-100;
$bg-dark: $neutral-900;

// 边框色彩
$border-light: $neutral-200;
$border-medium: $neutral-300;
$border-dark: $neutral-400;

// 字体家族
$font-family-primary: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$font-family-secondary: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;

// 字体大小 (rpx)
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-2xl: 40rpx;
$font-size-3xl: 48rpx;
$font-size-4xl: 56rpx;
$font-size-5xl: 64rpx;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// 间距 (rpx)
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-base: 16rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;
$spacing-2xl: 48rpx;
$spacing-3xl: 64rpx;
$spacing-4xl: 80rpx;
$spacing-5xl: 96rpx;

// 圆角
$radius-xs: 4rpx;
$radius-sm: 8rpx;
$radius-base: 12rpx;
$radius-lg: 16rpx;
$radius-xl: 24rpx;
$radius-2xl: 32rpx;
$radius-full: 50%;

// 阴影
$shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
$shadow-base: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
$shadow-lg: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
$shadow-xl: 0 16rpx 32rpx rgba(0, 0, 0, 0.1);

// 层级
$z-index-dropdown: 1000;
$z-index-modal: 1100;
$z-index-toast: 1200;
$z-index-loading: 1300;

// 响应式断点
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

// 动画
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// 渐变
$gradient-primary: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
$gradient-secondary: linear-gradient(135deg, $secondary-color 0%, $secondary-light 100%);
$gradient-accent: linear-gradient(135deg, $accent-color 0%, $accent-light 100%);

// 组件特定变量
$header-height: 88rpx;
$tabbar-height: 98rpx;
$safe-area-inset-top: env(safe-area-inset-top);
$safe-area-inset-bottom: env(safe-area-inset-bottom);
$status-bar-height: 44rpx; // 状态栏高度，iOS为44rpx，Android为24rpx 