#!/usr/bin/env node

// 旺剪App视频剪辑功能自动化测试运行脚本
const path = require('path')
const fs = require('fs')

// 测试配置
const testConfig = {
  // 测试环境
  environment: process.env.NODE_ENV || 'test',
  
  // 测试类型
  testType: process.argv.includes('--unit') ? 'unit' : 
            process.argv.includes('--integration') ? 'integration' : 
            process.argv.includes('--e2e') ? 'e2e' : 'all',
  
  // 是否生成覆盖率报告
  coverage: process.argv.includes('--coverage'),
  
  // 是否显示详细日志
  verbose: process.argv.includes('--verbose'),
  
  // 是否在浏览器中显示
  headless: !process.argv.includes('--headed'),
  
  // 并行运行
  parallel: process.argv.includes('--parallel'),
  
  // 重试次数
  retries: process.argv.includes('--retries') ? 
           parseInt(process.argv[process.argv.indexOf('--retries') + 1]) || 1 : 1,
  
  // 超时时间
  timeout: process.argv.includes('--timeout') ? 
           parseInt(process.argv[process.argv.indexOf('--timeout') + 1]) || 30000 : 30000,
  
  // 测试文件模式
  pattern: process.argv.includes('--pattern') ? 
           process.argv[process.argv.indexOf('--pattern') + 1] : null
}

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

// 日志函数
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  title: (msg) => console.log(`${colors.bright}${colors.cyan}${msg}${colors.reset}`),
  subtitle: (msg) => console.log(`${colors.magenta}${msg}${colors.reset}`)
}

// 创建测试结果目录
function createTestDirectories() {
  const dirs = [
    'test-results',
    'test-results/screenshots',
    'test-results/videos',
    'test-results/reports',
    'coverage'
  ]
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
      log.info(`创建目录: ${dir}`)
    }
  })
}

// 检查依赖
function checkDependencies() {
  log.info('检查测试依赖...')
  
  const requiredPackages = [
    '@dcloudio/uni-automator',
    'jest',
    'jest-html-reporters'
  ]
  
  const packageJson = require(path.join(process.cwd(), 'package.json'))
  const allDeps = {
    ...packageJson.dependencies,
    ...packageJson.devDependencies
  }
  
  const missing = requiredPackages.filter(pkg => !allDeps[pkg])
  
  if (missing.length > 0) {
    log.error(`缺少依赖包: ${missing.join(', ')}`)
    log.info('请运行: npm install --save-dev ' + missing.join(' '))
    process.exit(1)
  }
  
  log.success('依赖检查通过')
}

// 构建Jest配置
function buildJestConfig() {
  const jestConfig = {
    globalTeardown: '@dcloudio/uni-automator/dist/teardown.js',
    testEnvironment: '@dcloudio/uni-automator/dist/environment.js',
    testEnvironmentOptions: {
      compile: true,
      h5: {
        url: "http://localhost:8080",
        options: {
          headless: testConfig.headless
        }
      }
    },
    testTimeout: testConfig.timeout,
    setupFilesAfterEnv: ['<rootDir>/src/tests/e2e/common/setup.js'],
    collectCoverage: testConfig.coverage,
    collectCoverageFrom: [
      'src/components/**/*.vue',
      'src/pages/**/*.vue',
      'src/utils/**/*.js',
      'src/api/**/*.js',
      '!src/tests/**',
      '!src/static/**',
      '!**/node_modules/**'
    ],
    coverageDirectory: 'coverage',
    coverageReporters: ['html', 'text', 'text-summary'],
    reporters: [
      'default',
      ['jest-html-reporters', {
        publicPath: './test-results/reports',
        filename: 'test-report.html',
        pageTitle: '旺剪App视频剪辑功能测试报告',
        expand: true,
        openReport: true
      }]
    ],
    verbose: testConfig.verbose,
    maxWorkers: testConfig.parallel ? '50%' : 1,
    bail: testConfig.retries === 1 ? 1 : 0
  }
  
  // 根据测试类型设置测试文件匹配规则
  switch (testConfig.testType) {
    case 'unit':
      jestConfig.testMatch = ['<rootDir>/src/tests/unit/**/*.test.js']
      break
    case 'integration':
      jestConfig.testMatch = ['<rootDir>/src/tests/integration/**/*.test.js']
      break
    case 'e2e':
      jestConfig.testMatch = ['<rootDir>/src/tests/e2e/**/*.test.js']
      break
    default:
      jestConfig.testMatch = [
        '<rootDir>/src/tests/unit/**/*.test.js',
        '<rootDir>/src/tests/integration/**/*.test.js',
        '<rootDir>/src/tests/e2e/**/*.test.js'
      ]
  }
  
  // 如果有特定的测试文件模式
  if (testConfig.pattern) {
    jestConfig.testMatch = [`<rootDir>/src/tests/**/*${testConfig.pattern}*.test.js`]
  }
  
  return jestConfig
}

// 运行测试
async function runTests() {
  const jest = require('jest')
  
  try {
    log.title('🚀 开始运行旺剪App视频剪辑功能自动化测试')
    log.subtitle(`测试环境: ${testConfig.environment}`)
    log.subtitle(`测试类型: ${testConfig.testType}`)
    log.subtitle(`无头模式: ${testConfig.headless}`)
    log.subtitle(`并行运行: ${testConfig.parallel}`)
    log.subtitle(`生成覆盖率: ${testConfig.coverage}`)
    
    // 创建测试目录
    createTestDirectories()
    
    // 检查依赖
    checkDependencies()
    
    // 构建Jest配置
    const jestConfig = buildJestConfig()
    
    // 运行测试
    const results = await jest.runCLI(jestConfig, [process.cwd()])
    
    // 输出测试结果
    if (results.results.success) {
      log.success('所有测试通过!')
      log.info(`测试用例总数: ${results.results.numTotalTests}`)
      log.info(`通过用例数: ${results.results.numPassedTests}`)
      log.info(`失败用例数: ${results.results.numFailedTests}`)
      log.info(`跳过用例数: ${results.results.numPendingTests}`)
      log.info(`执行时间: ${results.results.testResults.reduce((acc, result) => acc + result.perfStats.end - result.perfStats.start, 0)}ms`)
    } else {
      log.error('测试失败!')
      log.error(`失败用例数: ${results.results.numFailedTests}`)
      process.exit(1)
    }
    
  } catch (error) {
    log.error('测试执行出错:')
    console.error(error)
    process.exit(1)
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
${colors.bright}旺剪App视频剪辑功能自动化测试运行器${colors.reset}

使用方法:
  node run-tests.js [选项]

选项:
  --unit          运行单元测试
  --integration   运行集成测试  
  --e2e           运行端到端测试
  --coverage      生成覆盖率报告
  --verbose       显示详细日志
  --headed        在浏览器中显示测试过程
  --parallel      并行运行测试
  --retries <n>   设置重试次数
  --timeout <ms>  设置超时时间
  --pattern <str> 匹配特定的测试文件
  --help          显示帮助信息

示例:
  node run-tests.js --e2e --headed --verbose
  node run-tests.js --unit --coverage
  node run-tests.js --pattern "basic" --verbose
  node run-tests.js --integration --parallel --timeout 60000
`)
}

// 主函数
async function main() {
  // 检查是否需要显示帮助
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp()
    return
  }
  
  // 运行测试
  await runTests()
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  log.error('未处理的Promise拒绝:')
  console.error(reason)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  log.error('未捕获的异常:')
  console.error(error)
  process.exit(1)
})

// 运行主函数
main().catch(error => {
  log.error('运行出错:')
  console.error(error)
  process.exit(1)
}) 