# TabBar 布局标签栏组件

## 组件描述

TabBar 是一个通用的底部标签栏组件，提供页面切换、图标切换、徽章显示和红点提示功能。支持自定义样式、隐藏显示和丰富的交互效果。

## 功能特性

- 🎯 **页面切换** - 支持主要页面间的切换导航
- 🎨 **图标切换** - 支持激活状态和默认状态的图标切换
- 📱 **徽章显示** - 支持数字徽章和红点提示
- 🔄 **自定义样式** - 支持颜色、背景、边框等样式自定义
- 🎭 **显示控制** - 支持隐藏和显示动画效果

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| currentIndex | Number | 0 | 当前激活的标签索引 |
| tabList | Array | [] | 标签列表，为空时使用默认配置 |
| activeColor | String | '#4A90E2' | 激活状态颜色 |
| inactiveColor | String | '#999999' | 非激活状态颜色 |
| backgroundColor | String | '#ffffff' | 背景颜色 |
| borderColor | String | '#e5e5e5' | 边框颜色 |
| visible | Boolean | true | 是否显示标签栏 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | index: Number, tab: Object | 标签切换时触发 |
| tab-click | tab: Object, index: Number | 标签点击时触发 |

## 数据结构

### tabList 数据格式

```javascript
[
  {
    text: '标签文字',                    // 必填：标签文字
    iconSrc: '/static/icons/tab.png',    // 可选：默认图标路径
    selectedIconSrc: '/static/icons/tab_active.png', // 可选：激活图标路径
    icon: '🏠',                         // 可选：默认图标文本
    selectedIcon: '🏠',                 // 可选：激活图标文本
    pagePath: '/pages/index/index',      // 可选：页面路径
    badge: '99+',                       // 可选：徽章文字
    dot: true                           // 可选：是否显示红点
  }
]
```

## 默认标签配置

当 `tabList` 为空时，组件会使用以下默认配置：

1. **首页** - `/pages/index/index`
2. **穿版** - `/pages/template/index`
3. **爆款模版** - `/pages/trending/index`
4. **创作历史** - `/pages/history/index`
5. **我的** - `/pages/profile/index`

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <!-- 页面内容 -->
    <TabBar 
      :current-index="currentTab"
      @change="handleTabChange"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import TabBar from '@/components/layout/TabBar/index.vue'

const currentTab = ref(0)

const handleTabChange = (index, tab) => {
  currentTab.value = index
  console.log('切换到标签:', tab.text, index)
}
</script>
```

### 自定义标签配置

```vue
<template>
  <view>
    <!-- 页面内容 -->
    <TabBar 
      :current-index="currentTab"
      :tab-list="customTabList"
      active-color="#ff6b6b"
      inactive-color="#666666"
      @change="handleTabChange"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import TabBar from '@/components/layout/TabBar/index.vue'

const currentTab = ref(0)

const customTabList = ref([
  {
    text: '首页',
    icon: '🏠',
    selectedIcon: '🏠',
    pagePath: '/pages/index/index'
  },
  {
    text: '发现',
    icon: '🔍',
    selectedIcon: '🔍',
    pagePath: '/pages/discover/index',
    badge: 'NEW'
  },
  {
    text: '消息',
    icon: '💬',
    selectedIcon: '💬',
    pagePath: '/pages/message/index',
    dot: true
  },
  {
    text: '我的',
    icon: '👤',
    selectedIcon: '👤',
    pagePath: '/pages/profile/index'
  }
])

const handleTabChange = (index, tab) => {
  currentTab.value = index
  console.log('切换到:', tab.text)
}
</script>
```

### 使用图片图标

```vue
<template>
  <view>
    <!-- 页面内容 -->
    <TabBar 
      :current-index="currentTab"
      :tab-list="imageTabList"
      @change="handleTabChange"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import TabBar from '@/components/layout/TabBar/index.vue'

const currentTab = ref(0)

const imageTabList = ref([
  {
    text: '首页',
    iconSrc: '/static/icons/home.png',
    selectedIconSrc: '/static/icons/home-active.png',
    pagePath: '/pages/index/index'
  },
  {
    text: '模板',
    iconSrc: '/static/icons/template.png',
    selectedIconSrc: '/static/icons/template-active.png',
    pagePath: '/pages/template/index',
    badge: '5'
  },
  {
    text: '历史',
    iconSrc: '/static/icons/history.png',
    selectedIconSrc: '/static/icons/history-active.png',
    pagePath: '/pages/history/index'
  },
  {
    text: '我的',
    iconSrc: '/static/icons/profile.png',
    selectedIconSrc: '/static/icons/profile-active.png',
    pagePath: '/pages/profile/index'
  }
])

const handleTabChange = (index, tab) => {
  currentTab.value = index
}
</script>
```

### 动态显示隐藏

```vue
<template>
  <view>
    <!-- 页面内容 -->
    <TabBar 
      :current-index="currentTab"
      :visible="tabBarVisible"
      @change="handleTabChange"
    />
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import TabBar from '@/components/layout/TabBar/index.vue'

const currentTab = ref(0)
const tabBarVisible = ref(true)

// 监听页面滚动，自动隐藏/显示标签栏
let lastScrollTop = 0

const handleScroll = (e) => {
  const scrollTop = e.detail.scrollTop
  
  if (scrollTop > lastScrollTop && scrollTop > 100) {
    // 向下滚动，隐藏标签栏
    tabBarVisible.value = false
  } else if (scrollTop < lastScrollTop) {
    // 向上滚动，显示标签栏
    tabBarVisible.value = true
  }
  
  lastScrollTop = scrollTop
}

onMounted(() => {
  // 监听页面滚动
  uni.onPageScroll(handleScroll)
})

onUnmounted(() => {
  // 移除滚动监听
  uni.offPageScroll(handleScroll)
})

const handleTabChange = (index, tab) => {
  currentTab.value = index
}
</script>
```

### 自定义样式

```vue
<template>
  <view>
    <!-- 页面内容 -->
    <TabBar 
      :current-index="currentTab"
      :tab-list="tabList"
      active-color="#ff6b6b"
      inactive-color="#999999"
      background-color="#ffffff"
      border-color="#f0f0f0"
      @change="handleTabChange"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import TabBar from '@/components/layout/TabBar/index.vue'

const currentTab = ref(0)

const tabList = ref([
  {
    text: '首页',
    icon: '🏠',
    selectedIcon: '🏠',
    pagePath: '/pages/index/index'
  },
  {
    text: '发现',
    icon: '🔍',
    selectedIcon: '🔍',
    pagePath: '/pages/discover/index'
  },
  {
    text: '消息',
    icon: '💬',
    selectedIcon: '💬',
    pagePath: '/pages/message/index',
    badge: '3'
  },
  {
    text: '我的',
    icon: '👤',
    selectedIcon: '👤',
    pagePath: '/pages/profile/index'
  }
])

const handleTabChange = (index, tab) => {
  currentTab.value = index
}
</script>
```

## 样式定制

组件使用 SCSS 编写，主要样式特点：

- 高度：`$tabbar-height` + `$safe-area-inset-bottom`
- 布局：flex 布局，等宽分布
- 图标：支持图片和文本两种形式
- 徽章：圆形背景，支持数字和文字
- 红点：小圆点提示，优先级低于徽章

## 交互效果

- **点击效果**：点击时有背景色变化
- **激活效果**：激活状态图标放大 1.1 倍
- **切换动画**：使用 `uni.switchTab` 进行页面切换
- **隐藏动画**：向下滑动隐藏，向上滑动显示

## 注意事项

1. 组件固定在底部，注意页面内容需要预留底部空间
2. 页面跳转使用 `uni.switchTab`，确保页面在 tabBar 配置中
3. 图标支持图片和文本两种形式，优先使用图片
4. 徽章和红点不能同时显示，徽章优先级更高
5. 组件使用 `z-index: 1000`，确保在其他元素之上

## 最佳实践

1. **图标选择**：使用统一的图标风格，保持视觉一致性
2. **文字简洁**：标签文字保持简洁明了，建议 2-4 个字符
3. **徽章使用**：合理使用徽章和红点，避免过度使用
4. **页面配置**：确保所有页面都在 `pages.json` 的 tabBar 配置中
5. **用户体验**：考虑自动隐藏/显示功能，提升用户体验

## 与其他组件配合

TabBar 通常与以下组件配合使用：

- **Header**: 布局头部组件
- **PageHeader**: 页面头部组件
- **CustomNavBar**: 自定义导航栏

形成完整的页面布局体系。 