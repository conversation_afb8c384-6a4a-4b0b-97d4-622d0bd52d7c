/**
 * 权限相关接口服务
 */

import http from '@/utils/http'

const permissionService = {
  // 获取角色权限列表
  getRoles() {
    return http.get('/api/v1/permission/roles')
  },
  
  // 分配用户角色
  assignUserRoles(data) {
    return http.post('/api/v1/permission/user-roles', data)
  },
  
  // 删除用户角色
  deleteUserRole(userId, roleId, cid) {
    return http.delete(`/api/v1/permission/user-roles/${userId}/${roleId}/${cid}`)
  }
}

export default permissionService 