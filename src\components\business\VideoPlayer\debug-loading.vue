<template>
  <view class="debug-video-player">
    <!-- 状态栏占位 -->
    <view class="debug-video-player__status-bar"></view>
    
    <!-- 调试信息 -->
    <view class="debug-video-player__debug-info">
      <text class="debug-video-player__debug-title">加载状态调试</text>
      <text class="debug-video-player__debug-item">加载状态: {{ isLoading ? '加载中' : '已加载' }}</text>
      <text class="debug-video-player__debug-item">播放状态: {{ isPlaying ? '播放中' : '已暂停' }}</text>
      <text class="debug-video-player__debug-item">视频源: {{ videoSrc }}</text>
    </view>
    
    <!-- 视频播放区域 -->
    <view class="debug-video-player__video-container">
      <video
        id="debugVideo"
        class="debug-video-player__video"
        :src="videoSrc"
        :controls="true"
        :show-center-play-btn="true"
        :show-play-btn="true"
        :show-fullscreen-btn="true"
        :show-progress="true"
        :enable-progress-gesture="true"
        object-fit="contain"
        @play="onPlay"
        @pause="onPause"
        @ended="onEnded"
        @timeupdate="onTimeUpdate"
        @loadedmetadata="onLoadedMetadata"
        @loadstart="onLoadStart"
        @canplay="onCanPlay"
        @loadeddata="onLoadedData"
        @canplaythrough="onCanPlayThrough"
        @error="onError"
      ></video>
      
      <!-- 加载指示器 -->
      <view class="debug-video-player__loading" v-show="isLoading">
        <view class="debug-video-player__loading-spinner"></view>
        <text class="debug-video-player__loading-text">加载中...</text>
      </view>
    </view>
    
    <!-- 控制按钮 -->
    <view class="debug-video-player__controls">
      <view class="debug-video-player__btn" @click="forceHideLoading">
        <text class="debug-video-player__btn-text">强制隐藏加载</text>
      </view>
      <view class="debug-video-player__btn" @click="forceShowLoading">
        <text class="debug-video-player__btn-text">强制显示加载</text>
      </view>
      <view class="debug-video-player__btn" @click="playVideo">
        <text class="debug-video-player__btn-text">播放视频</text>
      </view>
    </view>
    
    <!-- 事件日志 -->
    <view class="debug-video-player__logs">
      <text class="debug-video-player__logs-title">事件日志</text>
      <view class="debug-video-player__logs-list">
        <text class="debug-video-player__log-item" v-for="(log, index) in logs" :key="index">
          {{ log }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const isLoading = ref(true)
const isPlaying = ref(false)
const videoSrc = ref('https://www.w3schools.com/html/mov_bbb.mp4')
const logs = ref([])

// 添加日志
const addLog = (event, detail = '') => {
  const timestamp = new Date().toLocaleTimeString()
  const log = `[${timestamp}] ${event}${detail ? ': ' + detail : ''}`
  logs.value.unshift(log)
  if (logs.value.length > 20) {
    logs.value.pop()
  }
}

// 事件处理
const onPlay = () => {
  isPlaying.value = true
  isLoading.value = false
  addLog('play')
}

const onPause = () => {
  isPlaying.value = false
  addLog('pause')
}

const onEnded = () => {
  isPlaying.value = false
  addLog('ended')
}

const onTimeUpdate = (e) => {
  const current = e.detail.currentTime || 0
  const total = e.detail.duration || 0
  addLog('timeupdate', `${Math.floor(current)}s / ${Math.floor(total)}s`)
}

const onLoadedMetadata = (e) => {
  const duration = e.detail.duration || 0
  addLog('loadedmetadata', `duration: ${duration}s`)
}

const onLoadStart = () => {
  isLoading.value = true
  addLog('loadstart')
}

const onCanPlay = () => {
  isLoading.value = false
  addLog('canplay')
}

const onLoadedData = () => {
  isLoading.value = false
  addLog('loadeddata')
}

const onCanPlayThrough = () => {
  isLoading.value = false
  addLog('canplaythrough')
}

const onError = (e) => {
  addLog('error', e.detail)
}

// 控制方法
const forceHideLoading = () => {
  isLoading.value = false
  addLog('强制隐藏加载')
}

const forceShowLoading = () => {
  isLoading.value = true
  addLog('强制显示加载')
}

const playVideo = () => {
  const videoContext = uni.createVideoContext('debugVideo')
  videoContext.play()
  addLog('手动播放')
}
</script>

<style lang="scss" scoped>
.debug-video-player {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 24rpx;

  &__status-bar {
    height: var(--status-bar-height);
    width: 100%;
  }

  &__debug-info {
    background: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;
  }

  &__debug-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }

  &__debug-item {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 8rpx;
    display: block;
  }

  &__video-container {
    position: relative;
    background: #000;
    border-radius: 12rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
  }

  &__video {
    width: 100%;
    height: 400rpx;
  }

  &__loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
  }

  &__loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid rgba(255, 255, 255, 0.3);
    border-top: 4rpx solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  &__loading-text {
    color: #fff;
    font-size: 24rpx;
  }

  &__controls {
    display: flex;
    gap: 16rpx;
    margin-bottom: 24rpx;
  }

  &__btn {
    flex: 1;
    background: #007aff;
    border-radius: 8rpx;
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__btn-text {
    color: #fff;
    font-size: 24rpx;
  }

  &__logs {
    background: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
  }

  &__logs-title {
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }

  &__logs-list {
    max-height: 300rpx;
    overflow-y: auto;
  }

  &__log-item {
    font-size: 22rpx;
    color: #666;
    padding: 4rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    display: block;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>