

# 架构设计
旺剪 App 是一款智能短视频剪辑与运营工具，采用前后端分离架构开发，前端基于 uni-app 适配安卓、IOS 多端，后端以 Python 项目 AIcut/server 为核心。主要功能丰富多元，涵盖视频创作全流程：支持相册选素材或现场拍摄，结合 AI 自动生成或用户自定义编辑文案，提供系统声音（含多种预设音色 ）与人声采集配音模式，可选用系统音乐或自定义上传本地音乐，还能精细调整画面特效、字幕样式、画面比例等个性化设置 。具备智能剪辑能力，AI 混剪可拆分重组素材并添加字幕动效，AI 穿版视频自动识别商品主体适配模板，模板视频提供丰富分类模板供快速套用 。运营辅助功能完善，矩阵分发支持多平台账号管理与任务创建，客资管理涵盖私信、评论互动维护，数据统计提供多维度数据辅助策略优化 。同时，支持创作历史管理与爆款模版复用 。

对于前端App将使用uni-app开发，开发时将注意：1. 组件化开发，从前端系统中尽量拆分出组件，可对这些组件进行自动化测试、提供组件的使用说明、运行示例（可运行打包出组件集合页可进入各个组件示例查看，生产发布打包成的旺剪APP不带组件集合页、示例、说明文档等）；2. 将使用uni-app官方推荐的自动化测试方式对APP进行测试，自动化测试脚本统一到一个地方，其中提供一些基础工具类封装；3.按uni-app官方推荐的方式支持国际化。

根据以上前端框架设计需求，已经让Claude确定了“旺剪App uni-app项目框架设计”，请根据这个框架设计升级当前代码，并且实现如图首页功能（先分析出可以抽离成组件作为组件开发并使用到首页）。

# 注意事项
热更（灰度发布）；数据埋点（业务统计、错误统计）；推送；隐私协议；



