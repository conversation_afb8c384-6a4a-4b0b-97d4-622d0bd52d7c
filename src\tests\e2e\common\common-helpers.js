// 通用测试辅助工具
const CommonHelpers = {
  
  // 延时等待
  async wait(ms = 1000) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },
  
  // 等待元素出现
  async waitForElement(selector, timeout = 10000) {
    console.log(`⏳ 等待元素出现: ${selector}`)
    const page = await program.currentPage()
    try {
      await page.waitForSelector(selector, { timeout })
      console.log(`✅ 元素已出现: ${selector}`)
      return true
    } catch (error) {
      console.log(`❌ 等待元素超时: ${selector}`)
      return false
    }
  },
  
  // 等待元素消失
  async waitForElementToDisappear(selector, timeout = 10000) {
    console.log(`⏳ 等待元素消失: ${selector}`)
    const page = await program.currentPage()
    try {
      await page.waitForSelector(selector, { hidden: true, timeout })
      console.log(`✅ 元素已消失: ${selector}`)
      return true
    } catch (error) {
      console.log(`❌ 等待元素消失超时: ${selector}`)
      return false
    }
  },
  
  // 检查元素是否存在
  async elementExists(selector) {
    const page = await program.currentPage()
    try {
      const element = await page.$(selector)
      return element !== null
    } catch (error) {
      return false
    }
  },
  
  // 获取元素文本
  async getElementText(selector) {
    const page = await program.currentPage()
    try {
      const element = await page.$(selector)
      if (element) {
        return await element.text()
      }
      return null
    } catch (error) {
      console.log(`❌ 获取元素文本失败: ${selector}`)
      return null
    }
  },
  
  // 获取元素属性
  async getElementAttribute(selector, attribute) {
    const page = await program.currentPage()
    try {
      const element = await page.$(selector)
      if (element) {
        return await element.getAttribute(attribute)
      }
      return null
    } catch (error) {
      console.log(`❌ 获取元素属性失败: ${selector}`)
      return null
    }
  },
  
  // 点击元素
  async clickElement(selector, options = {}) {
    console.log(`👆 点击元素: ${selector}`)
    const page = await program.currentPage()
    try {
      // 先等待元素出现
      await page.waitForSelector(selector, { timeout: 5000 })
      
      // 点击元素
      await page.tap(selector)
      
      // 等待点击后的延时
      if (options.waitAfter) {
        await this.wait(options.waitAfter)
      }
      
      console.log(`✅ 元素点击成功: ${selector}`)
      return true
    } catch (error) {
      console.log(`❌ 元素点击失败: ${selector}`, error)
      return false
    }
  },
  
  // 输入文本
  async inputText(selector, text) {
    console.log(`📝 输入文本: ${selector} = "${text}"`)
    const page = await program.currentPage()
    try {
      await page.waitForSelector(selector, { timeout: 5000 })
      await page.type(selector, text)
      console.log(`✅ 文本输入成功: ${selector}`)
      return true
    } catch (error) {
      console.log(`❌ 文本输入失败: ${selector}`, error)
      return false
    }
  },
  
  // 滚动到元素
  async scrollToElement(selector) {
    console.log(`📜 滚动到元素: ${selector}`)
    const page = await program.currentPage()
    try {
      await page.waitForSelector(selector, { timeout: 5000 })
      await page.evaluate((sel) => {
        const element = document.querySelector(sel)
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' })
        }
      }, selector)
      await this.wait(500)
      console.log(`✅ 滚动到元素成功: ${selector}`)
      return true
    } catch (error) {
      console.log(`❌ 滚动到元素失败: ${selector}`, error)
      return false
    }
  },
  
  // 截图
  async takeScreenshot(name, options = {}) {
    console.log(`📸 截图: ${name}`)
    const page = await program.currentPage()
    try {
      const timestamp = Date.now()
      const filename = `${name}-${timestamp}.png`
      const path = `./test-results/screenshots/${filename}`
      
      await page.screenshot({ 
        path,
        fullPage: options.fullPage || false,
        type: options.type || 'png'
      })
      
      console.log(`✅ 截图成功: ${filename}`)
      return filename
    } catch (error) {
      console.log(`❌ 截图失败: ${name}`, error)
      return null
    }
  },
  
  // 等待Toast消失
  async waitForToast(timeout = 3000) {
    console.log(`⏳ 等待Toast消失`)
    await this.wait(timeout)
    console.log(`✅ Toast等待完成`)
  },
  
  // 等待Modal出现
  async waitForModal(timeout = 5000) {
    console.log(`⏳ 等待Modal出现`)
    // 这里可以根据具体的Modal实现来调整
    await this.wait(timeout)
    console.log(`✅ Modal等待完成`)
  },
  
  // 模拟网络延迟
  async simulateNetworkDelay(delay = 1000) {
    console.log(`🌐 模拟网络延迟: ${delay}ms`)
    await this.wait(delay)
  },
  
  // 检查页面URL
  async checkCurrentUrl(expectedUrl) {
    console.log(`🔍 检查当前URL: ${expectedUrl}`)
    const page = await program.currentPage()
    try {
      const currentUrl = await page.url()
      const isMatch = currentUrl.includes(expectedUrl)
      
      if (isMatch) {
        console.log(`✅ URL检查通过: ${currentUrl}`)
      } else {
        console.log(`❌ URL检查失败: 期望包含 ${expectedUrl}, 实际 ${currentUrl}`)
      }
      
      return isMatch
    } catch (error) {
      console.log(`❌ URL检查失败:`, error)
      return false
    }
  },
  
  // 获取页面数据
  async getPageData(key) {
    console.log(`📊 获取页面数据: ${key}`)
    const page = await program.currentPage()
    try {
      const data = await page.callMethod('getData', key)
      console.log(`✅ 页面数据获取成功: ${key}`)
      return data
    } catch (error) {
      console.log(`❌ 页面数据获取失败: ${key}`, error)
      return null
    }
  },
  
  // 设置页面数据
  async setPageData(data) {
    console.log(`📊 设置页面数据:`, data)
    const page = await program.currentPage()
    try {
      await page.setData(data)
      console.log(`✅ 页面数据设置成功`)
      return true
    } catch (error) {
      console.log(`❌ 页面数据设置失败:`, error)
      return false
    }
  },
  
  // 调用页面方法
  async callPageMethod(method, ...args) {
    console.log(`📞 调用页面方法: ${method}`)
    const page = await program.currentPage()
    try {
      const result = await page.callMethod(method, ...args)
      console.log(`✅ 页面方法调用成功: ${method}`)
      return result
    } catch (error) {
      console.log(`❌ 页面方法调用失败: ${method}`, error)
      return null
    }
  },
  
  // 获取元素数量
  async getElementCount(selector) {
    const page = await program.currentPage()
    try {
      const elements = await page.$$(selector)
      return elements.length
    } catch (error) {
      console.log(`❌ 获取元素数量失败: ${selector}`, error)
      return 0
    }
  },
  
  // 断言元素存在
  async assertElementExists(selector, message = '') {
    const exists = await this.elementExists(selector)
    const assertMessage = message || `元素应该存在: ${selector}`
    expect(exists).toBe(true, assertMessage)
  },
  
  // 断言元素不存在
  async assertElementNotExists(selector, message = '') {
    const exists = await this.elementExists(selector)
    const assertMessage = message || `元素不应该存在: ${selector}`
    expect(exists).toBe(false, assertMessage)
  },
  
  // 断言元素文本
  async assertElementText(selector, expectedText, message = '') {
    const actualText = await this.getElementText(selector)
    const assertMessage = message || `元素文本应该是: ${expectedText}`
    expect(actualText).toBe(expectedText, assertMessage)
  },
  
  // 断言元素文本包含
  async assertElementTextContains(selector, expectedText, message = '') {
    const actualText = await this.getElementText(selector)
    const assertMessage = message || `元素文本应该包含: ${expectedText}`
    expect(actualText).toContain(expectedText, assertMessage)
  },
  
  // 断言元素数量
  async assertElementCount(selector, expectedCount, message = '') {
    const actualCount = await this.getElementCount(selector)
    const assertMessage = message || `元素数量应该是: ${expectedCount}`
    expect(actualCount).toBe(expectedCount, assertMessage)
  },
  
  // 重试操作
  async retry(operation, maxRetries = 3, delay = 1000) {
    console.log(`🔄 重试操作，最大重试次数: ${maxRetries}`)
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        console.log(`🔄 第 ${i + 1} 次尝试`)
        const result = await operation()
        console.log(`✅ 操作成功`)
        return result
      } catch (error) {
        console.log(`❌ 第 ${i + 1} 次尝试失败:`, error)
        
        if (i === maxRetries - 1) {
          console.log(`❌ 所有重试都失败了`)
          throw error
        }
        
        if (delay > 0) {
          await this.wait(delay)
        }
      }
    }
  },
  
  // 并行执行多个操作
  async parallel(operations) {
    console.log(`⚡ 并行执行 ${operations.length} 个操作`)
    try {
      const results = await Promise.all(operations)
      console.log(`✅ 所有并行操作完成`)
      return results
    } catch (error) {
      console.log(`❌ 并行操作失败:`, error)
      throw error
    }
  }
}

module.exports = CommonHelpers 