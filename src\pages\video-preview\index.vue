<template>
  <view class="video-preview-page">
    <!-- 页面头部 -->
    <CustomNavBar title="视频预览" :showBack="true" />
    
    <!-- 页面内容 -->
    <view class="video-preview-page__content">
      <view class="video-preview-page__placeholder">
        <text class="video-preview-page__placeholder-text">视频预览功能开发中...</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import CustomNavBar from '@/components/common/CustomNavBar/index.vue'

// 页面数据
const pageData = ref({
  title: '视频预览'
})

// 页面方法
const handleBack = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;

.video-preview-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  &__content {
    padding: 88rpx 40rpx 40rpx; // 44px顶部
    min-height: calc(100vh - 128rpx);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__placeholder {
    text-align: center;
    
    &-text {
      font-size: 32rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}
</style> 