<template>
  <view class="function-grid">
    <view v-if="title" class="function-grid__header">
      <text class="function-grid__title">{{ title }}</text>
      <view v-if="showMore" class="function-grid__more" @tap="handleMoreClick">
        <text class="function-grid__more-text">{{ moreText }}</text>
        <text class="function-grid__more-icon">›</text>
      </view>
    </view>
    
    <view class="function-grid__content" :class="`function-grid__content--${columns}`">
      <view 
        v-for="(item, index) in functionList" 
        :key="index"
        class="function-grid__item interactive"
        :class="{ 'function-grid__item--disabled': item.disabled }"
        @tap="handleItemClick(item, index)"
      >
        <view class="function-grid__item-content">
          <!-- 图标 -->
          <view class="function-grid__item-icon">
            <image 
              v-if="item.iconSrc" 
              :src="item.iconSrc" 
              mode="aspectFit" 
              class="function-grid__item-icon-image"
            />
            <text 
              v-else 
              class="function-grid__item-icon-text"
              :style="{ color: item.iconColor || '' }"
            >
              {{ item.icon || '⚡' }}
            </text>
          </view>
          
          <!-- 徽章 -->
          <view v-if="item.badge" class="function-grid__item-badge">
            <text class="function-grid__item-badge-text">{{ item.badge }}</text>
          </view>
          
          <!-- 标题 -->
          <text class="function-grid__item-title">{{ item.title }}</text>
          
          <!-- 描述 -->
          <text v-if="item.description" class="function-grid__item-desc">{{ item.description }}</text>
        </view>
      </view>
      
      <!-- 占位项（保持网格对齐） -->
      <view 
        v-for="placeholder in placeholderCount" 
        :key="`placeholder-${placeholder}`"
        class="function-grid__item function-grid__item--placeholder"
      ></view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  functionList: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Number,
    default: 3,
    validator: (value) => [2, 3, 4, 5].includes(value)
  },
  showMore: {
    type: Boolean,
    default: false
  },
  moreText: {
    type: String,
    default: '更多'
  },
  moreLink: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['item-click', 'more-click'])

// Computed
const placeholderCount = computed(() => {
  const remainder = props.functionList.length % props.columns
  return remainder === 0 ? 0 : props.columns - remainder
})

// Methods
const handleItemClick = (item, index) => {
  if (item.disabled) return
  
  emit('item-click', item, index)
  
  // 默认跳转行为
  if (item.link) {
    uni.navigateTo({
      url: item.link
    })
  } else if (item.action) {
    // 执行自定义动作
    item.action()
  }
}

const handleMoreClick = () => {
  emit('more-click')
  
  if (props.moreLink) {
    uni.navigateTo({
      url: props.moreLink
    })
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.function-grid {
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacing-lg;
    padding: 0 $spacing-lg;
  }
  
  &__title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }
  
  &__more {
    display: flex;
    align-items: center;
    padding: $spacing-xs $spacing-sm;
    border-radius: $radius-base;
    background-color: $bg-tertiary;
    transition: all $transition-fast;
    
    &:active {
      background-color: $bg-secondary;
    }
    
    &-text {
      font-size: $font-size-sm;
      color: $text-secondary;
      margin-right: $spacing-xs;
    }
    
    &-icon {
      font-size: $font-size-base;
      color: $text-secondary;
      font-weight: $font-weight-bold;
    }
  }
  
  &__content {
    display: grid;
    gap: $spacing-lg;
    padding: 0 $spacing-lg;
    
    &--2 {
      grid-template-columns: repeat(2, 1fr);
    }
    
    &--3 {
      grid-template-columns: repeat(3, 1fr);
    }
    
    &--4 {
      grid-template-columns: repeat(4, 1fr);
    }
    
    &--5 {
      grid-template-columns: repeat(5, 1fr);
    }
  }
  
  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 160rpx;
    background-color: $bg-primary;
    border-radius: $radius-lg;
    box-shadow: $shadow-sm;
    transition: all $transition-fast;
    
    &:active {
      transform: translateY(2rpx);
      box-shadow: $shadow-base;
    }
    
    &--disabled {
      opacity: 0.5;
      
      &:active {
        transform: none;
      }
    }
    
    &--placeholder {
      visibility: hidden;
      box-shadow: none;
      
      &:active {
        transform: none;
      }
    }
    
    &-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: $spacing-base;
      width: 100%;
      text-align: center;
    }
    
    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64rpx;
      height: 64rpx;
      margin-bottom: $spacing-sm;
      border-radius: $radius-base;
      background-color: $bg-secondary;
      
      &-image {
        width: 48rpx;
        height: 48rpx;
      }
      
      &-text {
        font-size: $font-size-2xl;
      }
    }
    
    &-badge {
      position: absolute;
      top: 0;
      right: 0;
      background-color: $accent-color;
      padding: 2rpx $spacing-xs;
      border-radius: $radius-full;
      transform: translate(25%, -25%);
      
      &-text {
        font-size: $font-size-xs;
        font-weight: $font-weight-medium;
        color: $text-inverse;
        line-height: 1;
      }
    }
    
    &-title {
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $text-primary;
      line-height: $line-height-tight;
      margin-bottom: $spacing-xs;
      @include text-ellipsis;
      width: 100%;
    }
    
    &-desc {
      font-size: $font-size-xs;
      color: $text-secondary;
      line-height: $line-height-normal;
      @include text-ellipsis-2;
      width: 100%;
    }
  }
}

// 响应式适配
@media (max-width: 600px) {
  .function-grid {
    &__content {
      &--4 {
        grid-template-columns: repeat(3, 1fr);
      }
      
      &--5 {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }
}

@media (max-width: 400px) {
  .function-grid {
    &__content {
      gap: $spacing-base;
      
      &--3,
      &--4,
      &--5 {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    &__item {
      min-height: 140rpx;
      
      &-content {
        padding: $spacing-sm;
      }
      
      &-icon {
        width: 48rpx;
        height: 48rpx;
        
        &-image {
          width: 36rpx;
          height: 36rpx;
        }
        
        &-text {
          font-size: $font-size-lg;
        }
      }
      
      &-title {
        font-size: $font-size-xs;
      }
      
      &-desc {
        display: none; // 小屏幕隐藏描述文字
      }
    }
  }
}
</style> 