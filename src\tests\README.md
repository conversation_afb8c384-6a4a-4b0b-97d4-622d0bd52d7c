# 旺剪 App 视频剪辑功能自动化测试

## 概述

本项目为旺剪 App 的视频剪辑功能提供了完整的自动化测试解决方案，基于 uni-app 官方推荐的自动化测试框架（uni-automator + Jest）实现。

## 测试覆盖范围

### 功能覆盖
- 页面加载和初始化
- 视频素材上传和管理
- AI文案生成和自定义设置
- 配音设置（系统声音和人声采集）
- 背景音乐设置（系统音乐和自定义音乐）
- 视频生成和任务创建
- 用户交互和体验

### 测试类型
- **单元测试 (Unit Tests)**: 组件和工具函数测试
- **集成测试 (Integration Tests)**: API和模块集成测试
- **端到端测试 (E2E Tests)**: 完整用户流程测试

### 测试方法
- **边界值测试**: 针对文件大小、时长、数量等边界值
- **场景法测试**: 针对不同使用场景和用户行为路径
- **等价类测试**: 针对不同文件类型、配置选项等

## 项目结构

```
src/tests/
├── docs/                          # 测试文档
│   └── video-editing-test-cases.md # 测试用例设计文档
├── config/                        # 测试配置
│   └── setup.js                   # 测试环境设置
├── helpers/                       # 测试辅助工具
│   ├── video-editing-helpers.js   # 视频剪辑功能测试工具
│   ├── common-helpers.js          # 通用测试辅助工具
│   └── test-data.js               # 测试数据
├── e2e/                          # 端到端测试
│   ├── video-editing-basic.test.js     # 基础功能测试
│   ├── video-editing-material.test.js  # 素材管理测试
│   └── video-editing-complete-flow.test.js # 完整流程测试
├── integration/                   # 集成测试
│   └── (待添加)
├── unit/                                           
│   ├── components                # 组件单元测试     
│   └── units                     # 公共类单元测试                

├── scripts/                      # 测试脚本
│   └── run-tests.js              # 测试运行脚本
└── README.md                     # 本文档
```

```
| 对比项        | 单元测试（Unit Test）        | 端对端测试（E2E Test）         |
|--------------|----------------------------|-----------------------------|
| 测试对象       | 单个函数/模块                | 整个系统/业务流程               |
| 依赖           | 隔离依赖（mock）             | 真实依赖                       |
| 执行速度       | 快                           | 慢                            |
| 发现问题类型   | 代码逻辑错误                  | 集成、配置、流程、环境问题      |

```
- **unit/**：存放所有单元测试文件(针对代码中最小的可测试单元如函数、方法、类、组件进行的自动化测试。)，结构可与 src 目录保持一致。
- **e2e/**：存放端对端测试文件(模拟真实用户操作，自动化测试整个应用的业务流程，从前端界面到后端服务的完整链路)，可按页面或业务模块细分子目录。
- **helpers/**：存放公共测试工具、方法、mock 工具、测试环境初始化脚本等。
- **mocks/**：集中管理 mock 数据、mock server（如有需要可新建）。
- **scripts/**：存放自定义测试 runner、CI 集成脚本等。
- **report/**：存放各类测试报告，CI 可自动收集。
- **docs/**、**README.md**：存放测试文档、配置和说明。

<!-- ## 环境要求

### 软件依赖
- Node.js >= 14.0.0
- HBuilderX (用于 app-plus 平台测试)
- Chrome/Chromium (用于 H5 平台测试)

### 包依赖
```json
{
  "devDependencies": {
    "@dcloudio/uni-automator": "latest",
    "jest": "^27.0.0",
    "jest-html-reporters": "^3.0.0",
    "expect": "^27.0.0"
  }
}
```

## 安装和配置

### 1. 安装依赖
```bash
npm install --save-dev @dcloudio/uni-automator jest jest-html-reporters
```

### 2. 配置 package.json
```json
{
  "scripts": {
    "test": "node src/tests/scripts/run-tests.js",
    "test:e2e": "node src/tests/scripts/run-tests.js --e2e",
    "test:unit": "node src/tests/scripts/run-tests.js --unit",
    "test:integration": "node src/tests/scripts/run-tests.js --integration",
    "test:coverage": "node src/tests/scripts/run-tests.js --coverage",
    "test:headed": "node src/tests/scripts/run-tests.js --e2e --headed --verbose"
  }
}
```

### 3. 配置测试环境
确保 HBuilderX 已安装并配置正确（用于 app-plus 测试）。

## 运行测试

### 基本命令

```bash
# 运行所有测试
npm test

# 运行端到端测试
npm run test:e2e

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 生成覆盖率报告
npm run test:coverage

# 在浏览器中显示测试过程
npm run test:headed
```

### 高级命令

```bash
# 运行特定的测试文件
node src/tests/scripts/run-tests.js --pattern "basic"

# 并行运行测试
node src/tests/scripts/run-tests.js --parallel

# 设置超时时间
node src/tests/scripts/run-tests.js --timeout 60000

# 显示详细日志
node src/tests/scripts/run-tests.js --verbose

# 组合选项
node src/tests/scripts/run-tests.js --e2e --headed --verbose --coverage
```

### 命令行选项

- `--unit`: 运行单元测试
- `--integration`: 运行集成测试
- `--e2e`: 运行端到端测试
- `--coverage`: 生成覆盖率报告
- `--verbose`: 显示详细日志
- `--headed`: 在浏览器中显示测试过程
- `--parallel`: 并行运行测试
- `--retries <n>`: 设置重试次数
- `--timeout <ms>`: 设置超时时间
- `--pattern <str>`: 匹配特定的测试文件
- `--help`: 显示帮助信息 -->

## 测试用例说明

### 1. 基础功能测试 (video-editing-basic.test.js)
- 页面加载和初始化
- 导航和路由
- UI交互基础功能
- 错误处理和边界条件

### 2. 素材管理测试 (video-editing-material.test.js)
- 视频素材上传功能
- 视频素材管理功能
- 素材边界值测试
- 素材交互功能
- 素材异常处理

### 3. 完整流程测试 (video-editing-complete-flow.test.js)
- 标准完整流程
- 边界值完整流程
- 异常处理完整流程
- 性能和稳定性测试
- 用户体验流程

## 测试数据

测试数据定义在 `helpers/test-data.js` 中，包括：

- **视频素材数据**: 不同大小、时长的测试视频
- **文案数据**: 正常文案、边界值文案、特殊字符文案
- **配音设置数据**: 不同声音类型和参数
- **背景音乐数据**: 系统音乐和自定义音乐
- **测试场景数据**: 完整流程、边界值、异常场景

## 测试辅助工具

### VideoEditingHelpers
专门针对视频剪辑功能的测试辅助工具，提供：
- 页面导航和状态管理
- 素材上传和管理操作
- 文案设置和验证
- 配音和音乐配置
- 生成操作和结果验证

### CommonHelpers
通用测试辅助工具，提供：
- 元素等待和操作
- 断言和验证方法
- 截图和日志功能
- 错误处理和重试机制

## 测试报告

测试完成后会生成以下报告：

### 1. HTML 测试报告
- 位置: `test-results/reports/test-report.html`
- 包含: 测试结果、执行时间、错误详情

### 2. 覆盖率报告
- 位置: `coverage/index.html`
- 包含: 代码覆盖率统计、未覆盖代码高亮

### 3. 截图和录像
- 截图: `test-results/screenshots/`
- 录像: `test-results/videos/` (如果启用)

## 最佳实践

### 1. 测试编写
- 使用描述性的测试名称
- 每个测试保持独立和原子性
- 合理使用 beforeEach 和 afterEach 清理状态
- 添加必要的等待时间避免竞态条件

### 2. 测试数据
- 使用 TestData 中定义的标准测试数据
- 避免硬编码测试数据
- 为边界值测试创建专门的数据

### 3. 错误处理
- 在测试失败时截图
- 记录详细的错误信息
- 使用重试机制处理不稳定的测试

### 4. 性能考虑
- 避免不必要的等待时间
- 合理使用并行测试
- 清理测试生成的文件

## 故障排除

### 常见问题

1. **测试超时**
   - 增加超时时间: `--timeout 60000`
   - 检查网络连接和服务器状态

2. **元素找不到**
   - 检查选择器是否正确
   - 增加等待时间
   - 确认页面已完全加载

3. **HBuilderX 相关错误**
   - 确认 HBuilderX 已正确安装
   - 检查路径配置
   - 确认有足够的权限

4. **依赖包问题**
   - 运行 `npm install` 重新安装依赖
   - 检查 Node.js 版本兼容性

### 调试技巧

1. **使用 headed 模式**
   ```bash
   npm run test:headed
   ```

2. **启用详细日志**
   ```bash
   node src/tests/scripts/run-tests.js --verbose
   ```

3. **运行单个测试**
   ```bash
   node src/tests/scripts/run-tests.js --pattern "basic"
   ```

4. **查看截图**
   测试失败时查看 `test-results/screenshots/` 目录下的截图

## 持续集成

### GitHub Actions 示例

```yaml
name: 自动化测试

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: 设置 Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行测试
      run: npm run test:e2e -- --headless
      
    - name: 上传测试报告
      uses: actions/upload-artifact@v2
      if: always()
      with:
        name: test-reports
        path: |
          test-results/
          coverage/
```

## 贡献指南

### 添加新测试

1. 在相应的测试目录中创建测试文件
2. 使用现有的测试辅助工具
3. 遵循命名规范: `*.test.js`
4. 添加适当的文档和注释

### 修改测试辅助工具

1. 保持向后兼容性
2. 添加适当的错误处理
3. 更新相关文档
4. 添加使用示例

## 联系方式

如有问题或建议，请联系开发团队或提交 Issue。 


