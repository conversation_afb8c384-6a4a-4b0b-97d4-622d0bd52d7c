# 视频播放器弹窗层级问题解决方案

## 问题描述

在视频播放器中，当点击导出按钮时，下载进度弹窗可能被video组件遮挡，这是因为uni-app的video组件在某些平台上具有特殊的层级处理。同时，当弹窗关闭后，视频播放器没有自动恢复播放，影响用户体验。

## 解决方案

### 1. 提高弹窗z-index值
- DownloadProgress组件的z-index设置为99999
- 确保弹窗显示在最顶层

### 2. 弹窗时隐藏video组件
- 通过`hasModal`属性控制video组件的显示/隐藏
- 当有弹窗时，隐藏video组件并显示占位区域
- 弹窗关闭后，恢复video组件显示

### 3. 自动恢复播放功能
- 当弹窗显示时，自动记录当前播放状态
- 弹窗关闭后，如果之前正在播放，则自动恢复播放
- 提供更好的用户体验

## 技术实现

### VideoPlayer组件修改

```javascript
// 新增状态变量
const wasPlayingBeforeModal = ref(false)

// 监听hasModal属性变化
watch(() => props.hasModal, (newHasModal, oldHasModal) => {
  if (oldHasModal && !newHasModal) {
    // 弹窗关闭，恢复播放状态
    if (wasPlayingBeforeModal.value) {
      nextTick(() => {
        setTimeout(() => {
          const context = getVideoContext()
          context.play()
        }, 100) // 延迟100ms确保视频组件已重新渲染
      })
    }
  } else if (!oldHasModal && newHasModal) {
    // 弹窗显示，记录当前播放状态并暂停视频
    wasPlayingBeforeModal.value = isPlaying.value
    if (isPlaying.value) {
      const context = getVideoContext()
      context.pause()
    }
  }
})
```

### 使用方式

```vue
<template>
  <view>
    <VideoPlayer
      v-if="showVideoPlayer"
      :video-src="videoUrl"
      :has-modal="showDownloadProgress"
      @export="handleExport"
    />
    
    <DownloadProgress
      :visible="showDownloadProgress"
      :progress="downloadProgress"
      :file-name="downloadFileName"
      @cancel="handleCancelDownload"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import VideoPlayer from '@/components/business/VideoPlayer/index.vue'
import DownloadProgress from '@/components/business/DownloadProgress/index.vue'

const showVideoPlayer = ref(false)
const showDownloadProgress = ref(false)
const downloadProgress = ref(0)
const downloadFileName = ref('')

const handleExport = () => {
  // 开始下载，显示进度弹窗
  showDownloadProgress.value = true
  downloadFileName.value = '视频文件.mp4'
  // 模拟下载进度
  const timer = setInterval(() => {
    if (downloadProgress.value < 100) {
      downloadProgress.value += 10
    } else {
      clearInterval(timer)
      setTimeout(() => {
        showDownloadProgress.value = false
        // 弹窗关闭后，视频会自动恢复播放（如果之前正在播放）
      }, 1000)
    }
  }, 500)
}

const handleCancelDownload = () => {
  showDownloadProgress.value = false
  downloadProgress.value = 0
  // 弹窗关闭后，视频会自动恢复播放（如果之前正在播放）
}
</script>
```

## 实现细节

### 弹窗显示时
- video组件被隐藏，显示占位区域
- 播放/暂停按钮在弹窗时也被隐藏
- 加载指示器在弹窗时也被隐藏
- 自动记录当前播放状态
- 如果正在播放，则主动暂停视频

### 弹窗关闭时
- video组件自动恢复显示
- 如果之前正在播放，则自动恢复播放
- 延迟100ms确保视频组件已重新渲染

## 测试验证

### 测试场景
1. **基础弹窗测试**：仅显示弹窗，不涉及视频播放
2. **视频播放+弹窗测试**：先播放视频，然后显示弹窗
3. **自动恢复播放测试**：验证弹窗关闭后视频是否自动恢复播放
4. **取消下载测试**：验证取消下载后视频是否恢复播放

### 测试步骤
1. 打开视频播放器
2. 开始播放视频
3. 点击导出按钮或触发弹窗
4. 观察视频是否暂停，弹窗是否正常显示
5. 等待弹窗关闭或手动取消
6. 验证视频是否自动恢复播放

## 注意事项

1. 确保在使用VideoPlayer时正确传递`hasModal`属性
2. 弹窗组件的z-index值要足够高
3. 在弹窗显示期间，video组件会被隐藏，这是正常行为
4. 建议在弹窗关闭后重置相关状态
5. **弹窗关闭后，如果视频之前正在播放，会自动恢复播放，无需手动处理**

## 更新日志

### v1.2.0
- **新增弹窗关闭后自动恢复播放功能**
- 优化弹窗状态管理
- 提升用户体验

### v1.1.0
- 新增`hasModal`属性支持
- 解决弹窗层级问题
- 优化弹窗时的用户体验

### v1.0.0
- 基础视频播放功能
- 播放控制
- 全屏支持
- 进度条控制