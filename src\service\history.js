import http from '@/utils/http'

/**
 * 创作历史相关服务
 */

const historyService = {
    /**
     * 获取创建历史
     * @param {Object} params 分页参数 { page, page_size, user_id, material_id, status }
     */
    getCreateHistoryList: (params = {}) => {
        return http.get('/api/v1/video-editing/tasks', params)
    },
    /**
     * 获取创建历史详情
     * @param {string} taskId 创作历史id
     */
    getCreateHistoryDetail: (taskId) => {
        return http.get(`/api/v1/video-editing/tasks/${taskId}`)
    },
    /**
     * 删除视频创作历史
     * @param {string} taskId 创作历史id
     */
    deleteCreateHistory: (taskId) => {
        return http.delete(`/api/v1/video-editing/tasks/${taskId}`)
    },
    /**
     * 重试视频创作历史
     * @param {string} taskId 创作历史id
     */
    retryCreateHistory: (taskId) => {
        return http.post(`/api/v1/video-editing/tasks/${taskId}/retry`)
    },
    /**
     * 获取原视频下载链接
     * @param {string} videoId 原视频vod_video_id
     */
    getOriginalVodDownloadUrl: (videoId) => {
        return http.get(`/api/v1/video-editing/vod/original/${videoId}/download`)
    }
}

export default historyService;