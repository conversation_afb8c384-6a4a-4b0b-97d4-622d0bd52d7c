<template>
	<view class="native-gallery__nav-bar" :style="{ paddingTop: statusBarHeight + 'px' }">
		<view class="native-gallery__nav-bar-content">
			<view class="native-gallery__nav-bar-left" @click="onBack">
				<text class="native-gallery__nav-bar-back-icon">×</text>
			</view>
			<!-- 标签栏 -->
			<view class="tab-container">
				<view 
					class="tab-item" 
					:class="{ 'active': activeTab === 'local' }" 
					@click="switchTab('local')"
				>
					<text class="tab-text" :class="{ 'active-text': activeTab === 'local' }">本地素材</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ 'active': activeTab === 'cloud' }" 
					@click="switchTab('cloud')"
				>
					<text class="tab-text" :class="{ 'active-text': activeTab === 'cloud' }">云素材</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ 'active': activeTab === 'library' }" 
					@click="switchTab('library')"
				>
					<text class="tab-text" :class="{ 'active-text': activeTab === 'library' }">素材库</text>
				</view>
			</view>
		</view>
	</view>
	<view class="gallery-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
		<!-- 本地素材选择器组件 -->
		<view v-if="activeTab === 'local'" class="content-container">
			<!-- 相册选择器组件 -->
			<qinsilk-gallery 
				ref="gallery" 
				tab="all" 
				class="gallery-selector"
				@on-select="onGallerySelect"
			></qinsilk-gallery>

			<!-- 新增：选择图片并上传按钮 -->
			<!--
			<view class="upload-btn-area">
				<button class="upload-btn" @click="chooseAndUploadImage">选择图片并上传</button>
			</view>
			-->
			
			<!-- 底部选择确认区域 - 性能点：使用 scroll-view 实现横向滚动，避免 list 嵌套 -->
			<view class="bottom-selection-area">
				<!-- 选中的媒体项列表 -->
				<scroll-view 
					class="selected-items-scroll" 
					:style="{ height: selectedItems.length > 0 ? '120px' : '0px' }"
					scroll-x="true" 
					show-scrollbar="false"
				>
					<view class="selected-items-container">
						<view 
							v-for="(item, index) in selectedItems" 
							:key="item.id" 
							class="selected-item"
						>
							<view class="selected-item-image-wrapper">
								<image :src="item.path" class="selected-item-image" mode="aspectFill" />
								<!-- 时长显示 -->
								<view class="selected-item-duration">
									<text class="duration-text">{{ item.duration }}</text>
								</view>
								<!-- 删除按钮 -->
								<view class="selected-item-remove" @click="removeSelectedItem(index)">
									<view class="remove-btn-bg">
										<text class="remove-icon">-</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
				
				<!-- 底部操作区域 -->
				<view class="bottom-actions">
					<view class="selection-tip" v-if="selectedItems.length === 0">
						<text class="selection-tip-text">请选择至少一个图片或者视频</text>
					</view>
					<view class="selection-tip" v-else>
						<text class="selection-tip-text">已选择 {{ selectedItems.length }} 个文件</text>
					</view>
					<view class="confirm-btn" @click="confirmSelection">
						<text class="confirm-btn-text">选好了</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 素材库内容 -->
		<MaterialLibrary 
			v-if="activeTab === 'library'" 
			@confirm="onMaterialLibraryConfirm"
		/>
		
		<!-- 云素材内容 -->
		<view v-if="activeTab === 'cloud'" class="content-container">
			<view class="empty-state">
				<text class="empty-text">云素材功能即将上线</text>
			</view>
		</view>
	</view>
</template>

<script>
import MaterialLibrary from '@/components/MaterialLibrary.nvue'

export default {
	name: 'NativeGallerySelector',
	components: {
		MaterialLibrary
	},
	data() {
		return {
			activeTab: 'local', // 当前激活的标签: 'local', 'cloud', 'library'
			statusBarHeight: 0, // 状态栏高度
			selectedItems: [] // 选中的媒体项数组
		}
	},
	
	onLoad(options) {
		console.log('Native Gallery Selector page loaded', options);
		
		// 获取系统信息，适配刘海屏
		this.getSystemInfo();
		
		// 如果有传入tab参数，则切换到指定标签
		if (options && options.tab) {
			this.activeTab = options.tab;
		}
	},
	
	onReady() {
		console.log('Native Gallery Selector page ready');
	},
	
	methods: {
		// 获取系统信息，适配刘海屏
		getSystemInfo() {
			uni.getSystemInfo({
				success: (res) => {
					console.log('系统信息:', res);
					this.statusBarHeight = res.statusBarHeight || 0;
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
					// 设置默认值
					this.statusBarHeight = 0;
				}
			});
		},
		
		// 切换标签页
		switchTab(tab) {
			this.activeTab = tab;
		},
		
		// 本地素材相关方法
		onGallerySelect(e) {
			console.log("相册选择事件：", e);
			
			if (e.detail) {
				// 原生端数据结构：detail 是 Map<String,Object>，需要获取 selected 字段
				const selected = e.detail.selected;
				
				if (selected && Array.isArray(selected) && selected.length > 0) {
					// 处理选中的媒体项数组
					this.selectedItems = selected.map((item, index) => ({
						id: index + 1, // 使用索引作为ID
						originalId: item.id, // 保存原生ID作为备用
						path: item.path || item.url,
						duration: item.duration || '00.30',
						size: item.size,
						name: item.name,
						mediaType: item.mediaType || 1
					}));
					
					console.log('处理后的选中项：', this.selectedItems);
					
					uni.showToast({
						title: `已选择 ${this.selectedItems.length} 个文件`,
						icon: 'success',
						duration: 1500
					});
				} else {
					this.selectedItems = [];
					uni.showToast({
						title: '请选择至少一个图片或视频',
						icon: 'none',
						duration: 2000
					});
				}
			} else {
				this.selectedItems = [];
			}
		},
		
		clearSelection() {
			this.selectedItems = [];
		},
		
		getSelectedItems() {
			return this.selectedItems;
		},
		
		resetGallery() {
			this.selectedItems = [];
		},
		
		removeSelectedItem(index) {
			const item = this.selectedItems[index];
			if (item && item.originalId) {
				// 如果有原生ID，调用原生方法删除
				uni.deleteMediaById({
					id: item.originalId,
					success: (res) => {
						console.log('原生删除成功:', res);
						// 从本地数组中移除
						this.selectedItems.splice(index, 1);
						uni.showToast({
							title: '删除成功',
							icon: 'success',
							duration: 1500
						});
					},
					fail: (err) => {
						console.error('原生删除失败:', err);
						uni.showToast({
							title: '删除失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			} else {
				// 没有原生ID，直接从本地数组移除
				this.selectedItems.splice(index, 1);
				console.log('移除选中项:', index);
			}
		},
		
		onBack() {
			uni.navigateBack();
		},
		
		confirmSelection() {
			if (this.selectedItems.length === 0) {
				uni.showToast({
					title: '请至少选择一个图片或视频',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			
			console.log('确认选择:', this.selectedItems);
			uni.showToast({
				title: '选择完成',
				icon: 'success',
				duration: 1500
			});
			
			uni.showModal({
				title: '提示',
				content: '待补充业务功能',
				showCancel: false
			});
		},
		
		// 新增：选择图片并上传
		async chooseAndUploadImage() {
			try {
				const res = await uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera']
				})
				if (res.tempFiles && res.tempFiles.length > 0) {
					const fileObj = res.tempFiles[0]
					const fileName = fileObj.name
					const title = fileName.replace(/\.[^/.]+$/, '')
					// 获取上传凭证
					const cred = await getVodUploadCredentials({
						title,
						file_name: fileName
					})
					
					// 根据status_code进行兼容处理
					if (cred && cred.status_code === 1) {
						const data = cred.data || cred // 兼容后端返回结构
						// 兼容PC端H5和移动端H5
						let file = fileObj.file || fileObj;
						if (!(file instanceof File)) {
							uni.showToast({ title: '请选择本地图片(H5环境)', icon: 'none' });
							return;
						}
						uni.showLoading({ title: '上传中...' })
						await uploadToAliyunVOD({
							file,
							uploadAuth: data.upload_auth,
							uploadAddress: data.upload_address,
							onProgress: (percent) => {
								uni.showLoading({ title: `上传中 ${percent}%` })
							}
						})
						uni.hideLoading()
						uni.showToast({ title: '上传成功', icon: 'success' })
					} else {
						// 失败情况：弹出消息并结束流程
						uni.showToast({ title: cred?.message || '获取上传凭证失败', icon: 'none' })
						return
					}
				} else {
					uni.showToast({ title: '未选择图片', icon: 'none' })
				}
			} catch (err) {
				console.log(err.message)
				uni.hideLoading()
				uni.showToast({ title: err.message || '上传失败', icon: 'none' })
			}
		},
		
		// 素材库相关方法
		onMaterialLibraryConfirm(selectedItems) {
			console.log('素材库选择完成:', selectedItems);
			
			// 处理选择完成事件
			setTimeout(() => {
				// 返回上一页
				uni.navigateBack({
					delta: 1
				});
			}, 1500);
		}
	}
}
</script>

<style scoped>
/* 顶部标签栏样式优化 */
.native-gallery__nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background-color: #1a1a1a;
	z-index: 100;
}

.native-gallery__nav-bar-content {
	height: 44px;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding: 0 16px;
}

.native-gallery__nav-bar-left {
	width: 32px;
	height: 32px;
	justify-content: center;
	align-items: center;
}

.native-gallery__nav-bar-back-icon {
	color: #ffffff;
	font-size: 24px;
	font-weight: bold;
	text-align: center;
	line-height: 32px;
}

.tab-container {
	flex: 1;
	flex-direction: row;
	justify-content: space-around;
	margin-left: 20px;
}

.tab-item {
	padding: 0 12px;
	height: 44px;
	justify-content: center;
	align-items: center;
	position: relative;
}

.tab-text {
	color: #8e8e8e;
	font-size: 15px;
}

.active-text {
	color: #ffffff;
	font-weight: bold;
}

.active {
	border-bottom-width: 2px;
	border-bottom-color: #ff0043;
}

.gallery-container {
	flex: 1;
	background-color: #fff;
}

.content-container {
	flex: 1;
	padding-bottom: 160px; /* 为底部选择区域留出空间 */
}

.gallery-selector {
	width: 750rpx;
	height: 500px;
}

.empty-state {
	flex: 1;
	justify-content: center;
	align-items: center;
	padding: 40px;
}

.empty-text {
	font-size: 16px;
	color: #999999;
	text-align: center;
}

/* 底部选择确认区域样式 - 性能优化：使用 flex 布局，避免绝对定位重绘 */
.bottom-selection-area {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #1a1a1a;
	border-top-left-radius: 16px;
	border-top-right-radius: 16px;
	padding: 16px 20px 20px 20px;
}

.selected-items-scroll {
	margin-bottom: 8px;
	height: 120px;
	flex-direction: row;
	align-items: flex-start;
	background-color: transparent;
}

.selected-items-container {
	flex-direction: row;
	align-items: flex-start;
	padding: 0 5px;
	padding-top: 10px;
}

.selected-item {
	margin-right: 12px;
	width: 80px;
	height: 100px;
	align-items: flex-start;
	background-color: transparent;
}

.selected-item-image-wrapper {
	width: 80px;
	height: 100px;
	border-radius: 12px;
	background-color: #f0f0f0;
	position: relative;
}

.selected-item-image {
	width: 80px;
	height: 100px;
	border-radius: 12px;
}

/* 时长显示 - 兼容点：使用 flex 布局避免绝对定位 */
.selected-item-duration {
	position: absolute;
	bottom: 6px;
	left: 6px;
	background-color: rgba(0, 0, 0, 0.7);
	padding: 2px 6px;
	border-radius: 4px;
}

.duration-text {
	color: #ffffff;
	font-size: 12px;
	font-weight: 500;
}

/* 删除按钮 - 性能点：使用 flex 布局，避免复杂阴影 */
.selected-item-remove {
	position: absolute;
	top: 4px;
	right: 4px;
	width: 24px;
	height: 24px;
	justify-content: center;
	align-items: center;
}

.remove-btn-bg {
	width: 24px;
	height: 24px;
	background-color: rgba(200,200,200,0.3);
	border-radius: 12px;
	justify-content: center;
	align-items: center;
}

.remove-icon {
	color: #ff0043;
	font-size: 28px;
	font-weight: bold;
	text-align: center;
	line-height: 24px;
}

.bottom-actions {
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	padding: 0 5px;
}

.selection-tip {
	flex: 1;
}

.selection-tip-text {
	color: #ffffff;
	font-size: 13px;
	opacity: 0.9;
}

.confirm-btn {
	background-color: #ff0043;
	border-radius: 8px;
	padding: 10px 20px;
	margin-left: 16px;
	justify-content: center;
	align-items: center;
}

.confirm-btn-text {
	color: #ffffff;
	font-size: 16px;
	font-weight: 600;
}

.upload-btn-area {
	margin: 32px 0 16px 0;
	display: flex;
	justify-content: center;
}
.upload-btn {
	background: linear-gradient(135deg, #ff2d55, #ff6b9d);
	color: #fff;
	font-size: 16px;
	padding: 12px 32px;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(255, 45, 85, 0.15);
	/* border: none; */
}
</style> 