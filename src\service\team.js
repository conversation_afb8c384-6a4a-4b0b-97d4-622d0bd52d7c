/**
 * 团队相关接口服务
 */

import http from '@/utils/http'

const teamService = {
  /**
   * 获取团队算力信息
   * @param {Object} params - 请求参数
   * @param {number} params.cid - 公司ID
   * @param {number} params.user_id - 用户ID
   * @returns {Promise}
   */
  getTeamComputePower(params = {}) {
    return http.get('/api/v1/compute-power/account/team', params)
  },

  /**
   * 获取个人算力信息
   * @returns {Promise}
   */
  getMyComputePower() {
    return http.get('/api/v1/compute-power/account/me')
  },

  /**
   * 获取算力日志
   * @param {Object} params - 请求参数
   * @param {number} params.page - 页码，默认1
   * @param {number} params.size - 每页数量，默认20
   * @returns {Promise}
   */
  getComputePowerLogs(params = { page: 1, size: 20 }) {
    return http.get('/api/v1/compute-power/logs', params)
  },

  /**
   * 分配团队算力
   * @param {Object} data - 分配数据
   * @param {number} data.to_user_id - 目标用户ID
   * @param {number} data.amount - 分配的算力数量
   * @param {string} data.remark - 备注信息
   * @returns {Promise}
   */
  allocateComputePower(data) {
    return http.post('/api/v1/compute-power/allocate', data)
  }
}

export default teamService
