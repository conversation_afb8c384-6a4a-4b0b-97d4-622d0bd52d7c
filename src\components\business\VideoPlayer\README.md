# VideoPlayer 组件

## 功能描述

VideoPlayer 是一个功能完整的视频播放器组件，支持播放控制、全屏、进度条、导出等功能。

## 主要特性

- 视频播放控制（播放/暂停/快进/快退）
- 全屏播放支持
- 进度条拖拽控制
- 画质切换
- 导出功能
- 自动播放支持
- 弹窗层级管理
- **弹窗关闭后自动恢复播放**

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| videoSrc | String | - | 视频源地址（必填） |
| poster | String | '' | 视频封面图片 |
| objectFit | String | 'contain' | 视频填充模式 |
| autoPlay | Boolean | false | 是否自动播放 |
| loop | Boolean | false | 是否循环播放 |
| muted | Boolean | false | 是否静音 |
| hasModal | Boolean | false | 是否有弹窗显示（用于层级管理） |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| close | - | 关闭播放器 |
| play | - | 开始播放 |
| pause | - | 暂停播放 |
| ended | - | 播放结束 |
| timeupdate | { current, total } | 播放进度更新 |
| progress | percent | 进度条变化 |
| fullscreen | isFullscreen | 全屏状态变化 |
| export | - | 导出按钮点击 |
| error | error | 播放错误 |

## 弹窗层级问题解决方案

### 问题描述
在视频播放器中，当点击导出按钮时，下载进度弹窗可能被video组件遮挡，这是因为uni-app的video组件在某些平台上具有特殊的层级处理。

### 解决方案

1. **提高弹窗z-index值**
   - DownloadProgress组件的z-index设置为99999
   - 确保弹窗显示在最顶层

2. **弹窗时隐藏video组件**
   - 通过`hasModal`属性控制video组件的显示/隐藏
   - 当有弹窗时，隐藏video组件并显示占位区域
   - 弹窗关闭后，恢复video组件显示

3. **自动恢复播放功能**
   - 当弹窗显示时，自动记录当前播放状态
   - 弹窗关闭后，如果之前正在播放，则自动恢复播放
   - 提供更好的用户体验

4. **使用方式**
   ```vue
   <VideoPlayer
     :video-src="videoUrl"
     :has-modal="showDownloadProgress"
     @export="handleExport"
   />
   
   <DownloadProgress
     :visible="showDownloadProgress"
     @cancel="handleCancelDownload"
   />
   ```

### 实现细节

- 当`hasModal`为true时，video组件被隐藏，显示占位区域
- 播放/暂停按钮在弹窗时也被隐藏
- 加载指示器在弹窗时也被隐藏
- 弹窗关闭后，video组件自动恢复显示
- **新增：弹窗关闭后自动恢复播放状态**

## 使用示例

```vue
<template>
  <view>
    <VideoPlayer
      v-if="showVideoPlayer"
      :video-src="videoUrl"
      :poster="videoPoster"
      :auto-play="true"
      :has-modal="showDownloadProgress"
      @close="onVideoPlayerClose"
      @export="onVideoExport"
    />
    
    <DownloadProgress
      :visible="showDownloadProgress"
      :progress="downloadProgress"
      :file-name="downloadFileName"
      @cancel="handleCancelDownload"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import VideoPlayer from '@/components/business/VideoPlayer/index.vue'
import DownloadProgress from '@/components/business/DownloadProgress/index.vue'

const showVideoPlayer = ref(false)
const showDownloadProgress = ref(false)
const downloadProgress = ref(0)
const downloadFileName = ref('')

const onVideoExport = () => {
  // 开始下载，显示进度弹窗
  showDownloadProgress.value = true
  downloadFileName.value = '视频文件.mp4'
  // 模拟下载进度
  const timer = setInterval(() => {
    if (downloadProgress.value < 100) {
      downloadProgress.value += 10
    } else {
      clearInterval(timer)
      setTimeout(() => {
        showDownloadProgress.value = false
        // 弹窗关闭后，视频会自动恢复播放（如果之前正在播放）
      }, 1000)
    }
  }, 500)
}

const handleCancelDownload = () => {
  showDownloadProgress.value = false
  downloadProgress.value = 0
  // 弹窗关闭后，视频会自动恢复播放（如果之前正在播放）
}
</script>
```

## 注意事项

1. 确保在使用VideoPlayer时正确传递`hasModal`属性
2. 弹窗组件的z-index值要足够高
3. 在弹窗显示期间，video组件会被隐藏，这是正常行为
4. 建议在弹窗关闭后重置相关状态
5. **弹窗关闭后，如果视频之前正在播放，会自动恢复播放，无需手动处理**

## 更新日志

### v1.2.0
- **新增弹窗关闭后自动恢复播放功能**
- 优化弹窗状态管理
- 提升用户体验

### v1.1.0
- 新增`hasModal`属性支持
- 解决弹窗层级问题
- 优化弹窗时的用户体验

### v1.0.0
- 基础视频播放功能
- 播放控制
- 全屏支持
- 进度条控制