# VideoPlayer 组件更新日志

## v1.2.0 (2024-01-XX)

### 🎉 新功能
- **弹窗关闭后自动恢复播放功能**
  - 当弹窗显示时，自动记录当前播放状态
  - 弹窗关闭后，如果之前正在播放，则自动恢复播放
  - 提供更好的用户体验，无需手动处理播放状态

### 🔧 技术实现
- 新增 `wasPlayingBeforeModal` 状态变量，记录弹窗显示前的播放状态
- 使用 `watch` 监听 `hasModal` 属性变化
- 弹窗显示时主动暂停视频播放
- 弹窗关闭时自动恢复播放（如果之前正在播放）

### 📝 代码变更
```javascript
// 新增状态变量
const wasPlayingBeforeModal = ref(false)

// 新增监听器
watch(() => props.hasModal, (newHasModal, oldHasModal) => {
  if (oldHasModal && !newHasModal) {
    // 弹窗关闭，恢复播放状态
    if (wasPlayingBeforeModal.value) {
      nextTick(() => {
        setTimeout(() => {
          const context = getVideoContext()
          context.play()
        }, 100)
      })
    }
  } else if (!oldHasModal && newHasModal) {
    // 弹窗显示，记录当前播放状态并暂停视频
    wasPlayingBeforeModal.value = isPlaying.value
    if (isPlaying.value) {
      const context = getVideoContext()
      context.pause()
    }
  }
})
```

### 🧪 测试
- 更新了 demo 页面，添加弹窗测试功能
- 提供了完整的测试用例，验证弹窗关闭后自动恢复播放
- 支持模拟下载进度和取消下载场景

### 📚 文档更新
- 更新了 README.md，添加新功能说明
- 提供了详细的使用示例
- 更新了注意事项和最佳实践

---

## v1.1.0 (2024-01-XX)

### 🎉 新功能
- 新增 `hasModal` 属性支持
- 解决弹窗层级问题
- 优化弹窗时的用户体验

### 🔧 技术实现
- 当 `hasModal` 为 true 时，隐藏 video 组件并显示占位区域
- 提高弹窗组件的 z-index 值，确保显示在最顶层
- 播放/暂停按钮和加载指示器在弹窗时也被隐藏

---

## v1.0.0 (2024-01-XX)

### 🎉 基础功能
- 基础视频播放功能
- 播放控制（播放/暂停/快进/快退）
- 全屏播放支持
- 进度条拖拽控制
- 画质切换
- 导出功能
- 自动播放支持