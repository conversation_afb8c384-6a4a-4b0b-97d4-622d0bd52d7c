<template>
  <view class="design-verification">
    <!-- 状态栏占位 -->
    <view class="design-verification__status-bar"></view>
    
    <!-- 设计稿对照测试 -->
    <view class="design-verification__content">
      <view class="design-verification__header">
        <text class="design-verification__title">设计稿像素级对照测试</text>
      </view>
      
      <!-- 设计稿描述 -->
      <view class="design-verification__description">
        <text class="design-verification__desc-title">设计稿内容：</text>
        <text class="design-verification__desc-text">• 两个模特展示服装的视频</text>
        <text class="design-verification__desc-text">• 左侧模特：长袖连衣裙，抽象图案</text>
        <text class="design-verification__desc-text">• 右侧模特：两件套，深灰色条纹</text>
        <text class="design-verification__desc-text">• 文字叠加："利润走量款 中等毛利"</text>
      </view>
      
      <!-- 界面元素对照 -->
      <view class="design-verification__elements">
        <text class="design-verification__section-title">界面元素对照</text>
        
        <view class="design-verification__element-list">
          <view class="design-verification__element-item">
            <view class="design-verification__element-icon">❌</view>
            <view class="design-verification__element-info">
              <text class="design-verification__element-title">关闭按钮 (X)</text>
              <text class="design-verification__element-desc">左上角白色X图标</text>
            </view>
            <view class="design-verification__element-status">待测试</view>
          </view>
          
          <view class="design-verification__element-item">
            <view class="design-verification__element-icon">❌</view>
            <view class="design-verification__element-info">
              <text class="design-verification__element-title">画质设置 (702P)</text>
              <text class="design-verification__element-desc">右上角深灰色按钮，带下拉箭头</text>
            </view>
            <view class="design-verification__element-status">待测试</view>
          </view>
          
          <view class="design-verification__element-item">
            <view class="design-verification__element-icon">❌</view>
            <view class="design-verification__element-info">
              <text class="design-verification__element-title">导出按钮</text>
              <text class="design-verification__element-desc">右上角红色按钮，白色"导出"文字</text>
            </view>
            <view class="design-verification__element-status">待测试</view>
          </view>
          
          <view class="design-verification__element-item">
            <view class="design-verification__element-icon">❌</view>
            <view class="design-verification__element-info">
              <text class="design-verification__element-title">时间显示</text>
              <text class="design-verification__element-desc">左下角白色文字 "00:03 / 00:21"</text>
            </view>
            <view class="design-verification__element-status">待测试</view>
          </view>
          
          <view class="design-verification__element-item">
            <view class="design-verification__element-icon">❌</view>
            <view class="design-verification__element-info">
              <text class="design-verification__element-title">播放/暂停按钮</text>
              <text class="design-verification__element-desc">白色双竖线暂停图标</text>
            </view>
            <view class="design-verification__element-status">待测试</view>
          </view>
          
          <view class="design-verification__element-item">
            <view class="design-verification__element-icon">❌</view>
            <view class="design-verification__element-info">
              <text class="design-verification__element-title">进度条</text>
              <text class="design-verification__element-desc">红色横线，白色圆形滑块</text>
            </view>
            <view class="design-verification__element-status">待测试</view>
          </view>
          
          <view class="design-verification__element-item">
            <view class="design-verification__element-icon">❌</view>
            <view class="design-verification__element-info">
              <text class="design-verification__element-title">控制按钮</text>
              <text class="design-verification__element-desc">右下角三个白色图标：后退、前进、全屏</text>
            </view>
            <view class="design-verification__element-status">待测试</view>
          </view>
        </view>
      </view>
      
      <!-- 测试按钮 -->
      <view class="design-verification__actions">
        <view class="design-verification__action-btn" @click="startVerification">
          <text class="design-verification__action-text">开始像素级对照测试</text>
        </view>
      </view>
    </view>
    
    <!-- 视频播放器 -->
    <VideoPlayer
      v-if="isVideoPlayerVisible"
      :video-src="videoSrc"
      :poster="posterSrc"
      :auto-play="false"
      @close="onClose"
      @play="onPlay"
      @pause="onPause"
      @ended="onEnded"
      @timeupdate="onTimeUpdate"
      @fullscreen="onFullscreen"
      @export="onExport"
      @error="onError"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import VideoPlayer from './index.vue'

// 响应式数据
const isVideoPlayerVisible = ref(false)
const videoSrc = ref('https://www.w3schools.com/html/mov_bbb.mp4')
const posterSrc = ref('https://www.w3schools.com/html/pic_mountain.jpg')

// 测试方法
const startVerification = () => {
  isVideoPlayerVisible.value = true
}

// 事件处理
const onClose = () => {
  isVideoPlayerVisible.value = false
  // 这里可以更新测试状态
}

const onPlay = () => {
  // 更新播放按钮测试状态
}

const onPause = () => {
  // 更新暂停按钮测试状态
}

const onEnded = () => {
  // 更新播放结束测试状态
}

const onTimeUpdate = ({ current, total }) => {
  // 更新时间显示测试状态
}

const onFullscreen = (isFullscreen) => {
  // 更新全屏按钮测试状态
}

const onExport = () => {
  // 更新导出按钮测试状态
  uni.showToast({
    title: '导出功能测试通过',
    icon: 'success'
  })
}

const onError = (error) => {
  console.error('视频播放错误:', error)
}
</script>

<style lang="scss" scoped>
.design-verification {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  &__status-bar {
    height: var(--status-bar-height);
    width: 100%;
  }

  &__content {
    flex: 1;
    padding: 24rpx;
  }

  &__header {
    margin-bottom: 32rpx;
    text-align: center;
  }

  &__title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }

  &__description {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;
  }

  &__desc-title {
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }

  &__desc-text {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
    display: block;
    margin-bottom: 8rpx;
  }

  &__elements {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 32rpx;
  }

  &__section-title {
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }

  &__element-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }

  &__element-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    padding: 16rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
  }

  &__element-icon {
    font-size: 24rpx;
    width: 40rpx;
    text-align: center;
  }

  &__element-info {
    flex: 1;
  }

  &__element-title {
    font-size: 24rpx;
    font-weight: 500;
    color: #333;
    display: block;
    margin-bottom: 4rpx;
  }

  &__element-desc {
    font-size: 22rpx;
    color: #666;
    display: block;
  }

  &__element-status {
    font-size: 22rpx;
    color: #999;
    padding: 4rpx 12rpx;
    background: #e9ecef;
    border-radius: 4rpx;
  }

  &__actions {
    display: flex;
    justify-content: center;
  }

  &__action-btn {
    background: #007aff;
    border-radius: 12rpx;
    padding: 24rpx 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__action-text {
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
  }
}
</style>