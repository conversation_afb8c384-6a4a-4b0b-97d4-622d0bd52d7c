# FunctionGrid 功能网格组件

## 组件描述

FunctionGrid 是一个灵活的功能网格布局组件，用于展示功能卡片列表。支持多种列数配置、自定义样式和交互事件。

## 功能特性

- 🎯 **灵活布局** - 支持 2-5 列网格布局
- 🎨 **自定义样式** - 支持图标、徽章、标题、描述等元素
- 📱 **响应式设计** - 自动适配不同屏幕尺寸
- 🔄 **交互事件** - 支持点击事件和更多按钮
- 🎭 **状态管理** - 支持禁用状态和占位符对齐

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '' | 网格标题 |
| functionList | Array | [] | 功能列表数据 |
| columns | Number | 3 | 网格列数，支持 2-5 列 |
| showMore | Boolean | false | 是否显示更多按钮 |
| moreText | String | '更多' | 更多按钮文本 |
| moreLink | String | '' | 更多按钮跳转链接 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| item-click | item: Object, index: Number | 功能项点击时触发 |
| more-click | - | 更多按钮点击时触发 |

## 数据结构

### functionList 数据格式

```javascript
[
  {
    title: '功能标题',           // 必填：功能标题
    description: '功能描述',     // 可选：功能描述
    icon: '⚡',                 // 可选：图标文本
    iconSrc: '/path/to/icon.png', // 可选：图标图片路径
    iconColor: '#ff0000',       // 可选：图标颜色
    badge: 'NEW',               // 可选：徽章文本
    disabled: false,            // 可选：是否禁用
    link: '/pages/detail/index', // 可选：跳转链接
    action: () => {}            // 可选：自定义动作函数
  }
]
```

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <FunctionGrid 
      title="常用功能"
      :function-list="functionList"
      :columns="3"
      @item-click="handleItemClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import FunctionGrid from '@/components/business/FunctionGrid/index.vue'

const functionList = ref([
  {
    title: 'AI 文案',
    description: '智能生成文案',
    icon: '✍️',
    link: '/pages/ai-copywriting/index'
  },
  {
    title: '视频制作',
    description: '一键生成视频',
    icon: '🎬',
    link: '/pages/video-create/index'
  },
  {
    title: '模板中心',
    description: '海量模板选择',
    icon: '📋',
    badge: 'HOT',
    link: '/pages/templates/index'
  }
])

const handleItemClick = (item, index) => {
  console.log('点击功能:', item.title, index)
}
</script>
```

### 带更多按钮的用法

```vue
<template>
  <view>
    <FunctionGrid 
      title="推荐功能"
      :function-list="functionList"
      :columns="4"
      :show-more="true"
      more-text="查看更多"
      more-link="/pages/all-functions/index"
      @item-click="handleItemClick"
      @more-click="handleMoreClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import FunctionGrid from '@/components/business/FunctionGrid/index.vue'

const functionList = ref([
  {
    title: '数据分析',
    description: '查看数据统计',
    iconSrc: '/static/icons/analytics.png',
    link: '/pages/analytics/index'
  },
  {
    title: '用户管理',
    description: '管理用户信息',
    iconSrc: '/static/icons/users.png',
    disabled: true
  }
])

const handleItemClick = (item, index) => {
  if (item.disabled) {
    uni.showToast({ title: '功能暂未开放', icon: 'none' })
    return
  }
  console.log('点击功能:', item.title)
}

const handleMoreClick = () => {
  console.log('点击更多按钮')
}
</script>
```

### 自定义样式用法

```vue
<template>
  <view>
    <FunctionGrid 
      title="特色功能"
      :function-list="customFunctionList"
      :columns="2"
      @item-click="handleItemClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import FunctionGrid from '@/components/business/FunctionGrid/index.vue'

const customFunctionList = ref([
  {
    title: '智能配音',
    description: 'AI 语音合成',
    icon: '🎤',
    iconColor: '#ff6b6b',
    badge: 'AI',
    action: () => {
      // 自定义动作
      uni.showModal({
        title: '提示',
        content: '是否开始智能配音？',
        success: (res) => {
          if (res.confirm) {
            // 执行配音逻辑
          }
        }
      })
    }
  },
  {
    title: '背景音乐',
    description: '精选 BGM 库',
    icon: '🎵',
    iconColor: '#4ecdc4',
    link: '/pages/bgm-selector/index'
  }
])

const handleItemClick = (item, index) => {
  if (item.action) {
    item.action()
  }
}
</script>
```

## 样式定制

组件使用 SCSS 编写，主要样式特点：

- 网格间距：`$spacing-lg`
- 标题字体：`$font-size-lg`
- 卡片最小高度：`160rpx`
- 图标尺寸：自适应
- 交互效果：点击缩放和阴影变化

## 注意事项

1. `columns` 参数只支持 2-5 列，超出范围会使用默认值
2. 当功能项数量不能被列数整除时，会自动添加占位符保持对齐
3. 图标支持文本和图片两种形式，优先使用 `iconSrc`
4. 禁用状态的功能项不会触发点击事件
5. 组件会自动处理页面跳转和自定义动作

## 最佳实践

1. **图标选择**：建议使用统一的图标风格，保持视觉一致性
2. **描述文案**：保持简洁明了，建议不超过 10 个字符
3. **徽章使用**：用于突出重要功能或新功能
4. **列数选择**：根据功能数量和屏幕尺寸合理选择列数
5. **交互反馈**：为重要操作添加适当的用户反馈 