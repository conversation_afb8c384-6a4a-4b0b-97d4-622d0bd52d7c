<template>
  <view class="download-progress" v-if="visible">
    <view class="download-progress__mask" @click="handleMaskClick"></view>
    <view class="download-progress__container">
                        <!-- 标题栏 -->
                  <view class="download-progress__header">
                    <text class="download-progress__title">下载中</text>
                  </view>
      
      <!-- 文件名 -->
      <view class="download-progress__filename">
        <text class="download-progress__filename-text">{{ fileName }}</text>
      </view>
      
      <!-- 进度条区域 -->
      <view class="download-progress__progress-area">
        <view class="download-progress__progress-bar">
          <view 
            class="download-progress__progress-fill" 
            :style="{ width: `${progress}%` }"
          ></view>
        </view>
        <text class="download-progress__progress-text">{{ progress }}%</text>
      </view>
      
      <!-- 状态文本 -->
      <view class="download-progress__status">
        <text class="download-progress__status-text">{{ statusText }}</text>
      </view>
      
      <!-- 取消按钮 -->
      <view class="download-progress__cancel-btn" @click="handleCancel">
        <text class="download-progress__cancel-text">取消下载</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  progress: {
    type: Number,
    default: 0
  },
  fileName: {
    type: String,
    default: '视频文件'
  },
  status: {
    type: String,
    default: 'downloading' // downloading, completed, failed, cancelled
  }
})

const emits = defineEmits(['cancel', 'close'])

const statusText = computed(() => {
  switch (props.status) {
    case 'downloading':
      return '正在下载...'
    case 'completed':
      return '下载完成'
    case 'failed':
      return '下载失败'
    case 'cancelled':
      return '下载已取消'
    default:
      return '正在下载...'
  }
})

const handleCancel = () => {
  emits('cancel')
}

const handleMaskClick = () => {
  // 点击遮罩不关闭，防止误操作
  // emits('close')
}
</script>

<style lang="scss" scoped>
.download-progress {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 99999; // 提高z-index值，确保显示在最顶层
  display: flex;
  align-items: center;
  justify-content: center;
  
  &__mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
  }
  
  &__container {
    position: relative;
    z-index: 2;
    width: 600rpx;
    background: #fff;
    border-radius: 24rpx;
    padding: 48rpx 40rpx 40rpx 40rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
                &__header {
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                margin-bottom: 32rpx;
              }
  
                &__title {
                color: #262626;
                font-size: 36rpx;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                line-height: 48rpx;
              }
  
  &__filename {
    width: 100%;
    margin-bottom: 40rpx;
  }
  
  &__filename-text {
    color: #595959;
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    line-height: 40rpx;
    text-align: center;
    word-break: break-all;
  }
  
  &__progress-area {
    width: 100%;
    margin-bottom: 24rpx;
  }
  
  &__progress-bar {
    width: 100%;
    height: 12rpx;
    background: #f5f5f5;
    border-radius: 6rpx;
    overflow: hidden;
    margin-bottom: 16rpx;
  }
  
  &__progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff0043 0%, #ff6b6b 100%);
    border-radius: 6rpx;
    transition: width 0.3s ease;
  }
  
  &__progress-text {
    color: #ff0043;
    font-size: 32rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    line-height: 44rpx;
    text-align: center;
  }
  
  &__status {
    width: 100%;
    margin-bottom: 48rpx;
  }
  
  &__status-text {
    color: #8c8c8c;
    font-size: 26rpx;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    line-height: 36rpx;
    text-align: center;
  }
  
  &__cancel-btn {
    width: 100%;
    height: 88rpx;
    background: #f7f7f7;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__cancel-text {
    color: #595959;
    font-size: 32rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    line-height: 44rpx;
  }
}
</style> 