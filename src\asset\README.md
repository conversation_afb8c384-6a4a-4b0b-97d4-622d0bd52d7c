# 资源目录说明

## 目录结构

```
src/asset/
├── icon/           # 图标文件
│   ├── tab/        # 底部导航图标
│   ├── common/     # 通用图标
│   └── business/   # 业务相关图标
├── img/            # 图片文件
│   ├── banner/     # 横幅图片
│   ├── background/ # 背景图片
│   └── placeholder/ # 占位图片
└── README.md       # 使用说明
```

## 使用规范

### 图标文件 (icon/)
- **格式**: 推荐使用 SVG 格式，也可使用 PNG
- **尺寸**: 
  - 小图标: 24x24px
  - 中等图标: 32x32px  
  - 大图标: 48x48px
- **命名**: 使用小写字母和连字符，如 `home-active.svg`
- **颜色**: 使用单色或双色，避免过多颜色

### 图片文件 (img/)
- **格式**: 
  - 照片类: JPG
  - 插画类: PNG
  - 简单图形: SVG
- **尺寸**: 根据实际使用场景确定，建议提供多种尺寸
- **命名**: 描述性命名，如 `banner-home-main.jpg`
- **压缩**: 使用适当的压缩比例，平衡质量和文件大小

## 文件命名规范

### 图标命名
- `icon-{功能}-{状态}.{格式}`
- 例如: `icon-home-active.svg`, `icon-user-normal.png`

### 图片命名
- `{类型}-{位置}-{描述}.{格式}`
- 例如: `banner-home-main.jpg`, `bg-login-pattern.png`

## 使用方式

### 在Vue组件中使用

```vue
<template>
  <view class="container">
    <!-- 使用图标 -->
    <image 
      class="icon" 
      src="@/asset/icon/home-active.svg" 
      mode="aspectFit"
    />
    
    <!-- 使用图片 -->
    <image 
      class="banner" 
      src="@/asset/img/banner-home-main.jpg" 
      mode="aspectFill"
    />
  </view>
</template>
```

### 在CSS中使用

```scss
.icon-home {
  background-image: url('@/asset/icon/home-normal.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

.banner-bg {
  background-image: url('@/asset/img/bg-login-pattern.png');
  background-size: cover;
  background-position: center;
}
```

## 优化建议

1. **图标优化**
   - 使用 SVG 格式可以保证在不同分辨率下的清晰度
   - 合理使用图标字体，减少HTTP请求

2. **图片优化**
   - 使用 WebP 格式（在支持的平台上）
   - 实现图片懒加载
   - 提供不同尺寸的图片适配不同设备

3. **缓存策略**
   - 静态资源使用长期缓存
   - 文件名包含版本号或hash值

4. **CDN加速**
   - 将资源上传到CDN，提高加载速度
   - 使用多个CDN节点，提高可用性

## 注意事项

1. **版权问题**: 确保所有使用的图片和图标都有合法的使用权限
2. **文件大小**: 控制单个文件大小，避免影响应用性能
3. **兼容性**: 确保资源在目标平台上正常显示
4. **更新维护**: 定期清理不再使用的资源文件 