<template>
  <view class="analyse-page">
    <!-- 顶部渐变背景 -->
    <view class="analyse-gradient-bg"></view>
    <!-- 右上角管理 -->
    <text class="analyse-manage">管理</text>
    <!-- 内容区 -->
    <view class="analyse-center">
      <view class="analyse-progress">
        <svg width="174" height="156" viewBox="0 0 174 156" style="position: absolute; top: 0; left: 0; z-index: 2">
          <!-- 红色底边框 -->
          <rect x="1" y="1" width="172" height="154" rx="14" ry="14" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2" />
          <!-- 白色进度描边 -->
          <rect x="1" y="1" width="172" height="154" rx="14" ry="14" fill="none" :stroke-dasharray="progressLength" :stroke-dashoffset="progressOffset" stroke="#fff" stroke-width="2" style="transition: stroke-dashoffset 0.3s" />
        </svg>
        <image class="analyse-progress-icon" src="@/asset/img/create/analyse_progress.png" mode="aspectFill" />
      </view>
      <text class="analyse-percent">分析视频{{ percent }}%</text>
      <text class="analyse-desc">{{ statusText }}</text>
      <view class="analyse-footer">
        <view class="analyse-btn-group">
          <view class="analyse-continue-btn" @click="onContinueCreation">
            <text class="analyse-btn-text">继续创作</text>
          </view>
          <view class="analyse-view-result-btn" @click="onViewResult">
            <text class="analyse-btn-text">查看结果</text>
          </view>
        </view>
      </view>
    </view>

    <!-- VideoPlayer 组件 -->
    <VideoPlayer v-if="showVideoPlayer" :video-src="videoUrl" :poster="videoPoster" :auto-play="true" :loop="false" :muted="false" :has-modal="showDownloadProgress" @close="onVideoPlayerClose" @play="onVideoPlay" @pause="onVideoPause" @ended="onVideoEnded" @timeupdate="onVideoTimeUpdate" @fullscreen="onVideoFullscreen" @export="onVideoExport" @error="onVideoError" />

    <!-- 下载进度组件 -->
    <DownloadProgress :visible="showDownloadProgress" :progress="downloadProgress" :file-name="downloadFileName" :status="downloadStatus" @cancel="handleCancelDownload" />
  </view>
</template>

<script>
import materialService from '@/service/material'
import VideoPlayer from '@/components/business/VideoPlayer/index.vue'
import DownloadProgress from '@/components/business/DownloadProgress/index.vue'
import defaultPoster from '@/asset/img/history/default_poster.png'
import { downloadAndSaveToAlbum } from '@/utils/tools'
import videoService from '@/service/video.js'

export default {
  name: 'AnalysePage',
  components: {
    VideoPlayer,
    DownloadProgress
  },
  data() {
    return {
      percent: 0,
      fakePercent: 0, // 伪进度
      timer: null, // 定时器句柄
      taskId: null,
      vodVideoId: null, // 保存vod_video_id
      statusText: '分析视频中...',
      _lastProgress: 0, // 上一次进度
      _waitDotCount: 0, // 等待动画点数
      _waitUnchangedCount: 0, // 进度未变轮询次数
      _waitingTips: ['素材越多，AI更容易选出好分镜', '正在努力分析，请耐心等待', '分析视频中...', '智能识别精彩片段中...'],
      _waitingTipIndex: 0,
      // 视频播放相关数据
      showVideoPlayer: false,
      videoUrl: '',
      videoPoster: '',
      videoId: null,
      // 下载进度相关变量
      showDownloadProgress: false,
      downloadProgress: 0,
      downloadFileName: '',
      downloadStatus: 'downloading',
      currentDownloadTask: null
    }
  },
  computed: {
    progressLength() {
      // 2*(w+h-2r) + 2*PI*r
      const w = 172,
        h = 154,
        r = 14
      return 2 * (w + h - 2 * r) + 2 * Math.PI * r
    },
    progressOffset() {
      return this.progressLength * (1 - this.percent / 100)
    }
  },
  onLoad(query) {
    this.taskId = query.id
    this.fakePercent = 0
    this._lastProgress = 0
    this._waitDotCount = 0
    this._waitUnchangedCount = 0
    if (this.taskId) {
      this.fetchProgress()
      if (this.timer) clearInterval(this.timer) // 防止多次 setInterval
      this.timer = setInterval(() => {
        this.fetchProgress()
      }, 2000)
    }
  },
  onUnload() {
    this.stopProgressPolling()
    // 清理下载任务
    if (this.currentDownloadTask) {
      this.currentDownloadTask.abort()
      this.currentDownloadTask = null
    }
  },

  onHide() {
    // 页面隐藏时也停止轮询，避免后台继续请求
    this.stopProgressPolling()
  },
  methods: {
    // 停止进度轮询
    stopProgressPolling() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },

    async fetchProgress() {
      if (!this.taskId) return
      try {
        const res = await materialService.getVideoEditingTaskProgress(this.taskId)
        if (res.status_code !== 1) {
          this.statusText = res.message || '任务不存在'
          this.stopProgressPolling()
          return
        }
        const status = Number(res?.data?.status)
        const progress = res?.data?.progress ?? 0
        const errorMsg = res?.data?.error_message

        const statusMap = {
          0: '待处理',
          1: '脚本生成中',
          2: '脚本生成完成',
          3: '视频剪辑中',
          4: '分析完成！',
          5: '分析失败'
        }

        // 多区间伪进度逻辑
        const progressNodes = [0, 20, 60, 70, 80, 90, 100]
        let lower = 0,
          upper = 100
        for (let i = 0; i < progressNodes.length - 1; i++) {
          if (progress >= progressNodes[i] && progress < progressNodes[i + 1]) {
            lower = progressNodes[i]
            upper = progressNodes[i + 1]
            break
          }
        }
        if (progress === 100) {
          lower = 100
          upper = 100
        }
        if (progress < 100 && status !== 4 && status !== 5) {
          // 伪进度最大只能到下一档-1，且最大为99
          let maxFake = Math.min(upper - 1, 99)
          if (this.fakePercent < maxFake) {
            this.fakePercent++
          }
          // 伪进度不能倒退
          if (this.fakePercent < progress) {
            this.fakePercent = progress
          }
          this.percent = this.fakePercent
        } else {
          this.percent = 100
          this.fakePercent = 100
        }

        // 等待动画逻辑
        if (progress === this._lastProgress && status !== 4 && status !== 5) {
          this._waitUnchangedCount++
          // 每两次轮询切换一次文案
          if (this._waitUnchangedCount % 2 === 0) {
            this._waitingTipIndex = (this._waitingTipIndex + 1) % this._waitingTips.length
          }
          this.statusText = this._waitingTips[this._waitingTipIndex]
        } else {
          this._waitUnchangedCount = 0
          this._waitDotCount = 0
          this._waitingTipIndex = 0
          this.statusText = statusMap[status]
        }
        this._lastProgress = progress

        if (status === 4) {
          this.statusText = statusMap[status]
          this.fakePercent = 100
          this.percent = 100
          this.stopProgressPolling()
          // 视频生成完成，显示预览组件
          this.openVideoPreview(res?.data)
        } else if (status === 5) {
          this.statusText = errorMsg || statusMap[status]
          this.fakePercent = progress
          this.percent = progress
          this.stopProgressPolling()
        }
      } catch (err) {
        console.log('获取进度失败', err)
        this.statusText = '获取进度失败'
        this.stopProgressPolling()
      }
    },
    onCancel() {
      this.stopProgressPolling()
      if (typeof uni !== 'undefined' && uni.navigateBack) {
        uni.navigateBack()
      } else if (window && window.history) {
        window.history.back()
      }
    },

    // 显示视频预览
    openVideoPreview(video) {
      if (!video?.output_path_auth) {
        uni.showToast({ title: '暂无视频', icon: 'none' })
        return
      }
      const { output_path_auth, vod_video_id, cover_image_path_auth } = video
      this.videoUrl = output_path_auth
      this.videoId = vod_video_id
      this.videoPoster = cover_image_path_auth || defaultPoster
      this.showVideoPlayer = true
    },
    // 关闭视频播放器
    onVideoPlayerClose() {
      this.showVideoPlayer = false
      this.videoUrl = ''
      this.videoPoster = ''
    },

    // VideoPlayer 事件处理函数
    onVideoPlay() {
      console.log('视频开始播放')
    },

    onVideoPause() {
      console.log('视频暂停播放')
    },

    onVideoEnded() {
      console.log('视频播放结束')
    },

    onVideoTimeUpdate({ current, total }) {
      console.log(`播放进度: ${current}/${total}`)
    },

    onVideoFullscreen(isFullscreen) {
      console.log(`全屏状态: ${isFullscreen}`)
    },

    onVideoExport() {
      // 使用当前视频ID进行导出操作
      if (this.videoId) {
        this.handleDownload({ vod_video_id: this.videoId })
      }
    },

    onVideoError(error) {
      console.error('视频播放错误:', error)
      uni.showToast({ title: '视频播放失败', icon: 'none' })
    },

    // 下载处理
    handleDownload() {
      if (!this.videoId) {
        uni.showToast({ title: '无可下载文件', icon: 'none' })
        return
      }
      // 设置下载信息
      this.downloadFileName = '视频文件'
      this.downloadProgress = 0
      this.downloadStatus = 'downloading'
      this.showDownloadProgress = true
      // 获取下载链接
      videoService
        .getVideoDownloadUrl(this.videoId)
        .then(res => {
          if (res.status_code === 1 && res.data && res.data.download_url) {
            console.log(res.data.download_url, 'download_url')

            // 使用公共方法下载并保存到相册
            this.currentDownloadTask = downloadAndSaveToAlbum({
              downloadUrl: res.data.download_url,
              fileName: this.downloadFileName,
              onProgress: progress => {
                this.downloadProgress = progress
              },
              onSuccess: () => {
                this.downloadStatus = 'completed'
                this.downloadProgress = 100
                setTimeout(() => {
                  this.showDownloadProgress = false
                }, 1000)
              },
              onError: error => {
                console.error('下载失败:', error)
                this.downloadStatus = 'failed'
                setTimeout(() => {
                  this.showDownloadProgress = false
                }, 2000)
              }
            })
          } else {
            this.showDownloadProgress = false
            uni.showToast({ title: res.message || '获取下载链接失败', icon: 'none' })
          }
        })
        .catch(err => {
          console.error('获取下载链接失败:', err)
          this.showDownloadProgress = false
          uni.showToast({ title: '网络错误', icon: 'none' })
        })
    },

    // 取消下载处理
    handleCancelDownload() {
      if (this.currentDownloadTask) {
        this.currentDownloadTask.abort()
        this.currentDownloadTask = null
      }

      this.downloadStatus = 'cancelled'
      setTimeout(() => {
        this.showDownloadProgress = false
        uni.showToast({ title: '下载已取消', icon: 'none' })
      }, 1000)
    },

    // 继续创作 - 重新启动生成页面，清空数据
    onContinueCreation() {
      // 停止轮询
      this.stopProgressPolling()
      // 重新启动到创作页面，清空所有数据
      uni.reLaunch({
        url: '/pages/create/index'
      })
    },

    // 查看结果 - 关闭当前页和创作页，进入创作历史模块
    onViewResult() {
      // 停止轮询
      this.stopProgressPolling()
      // 直接跳转到历史页面，关闭当前页面栈
      uni.reLaunch({
        url: '/pages/history/index'
      })
    }
  }
}
</script>

<style scoped>
.analyse-page {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  background: #111113;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.analyse-gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 150px;
  background: radial-gradient(0% 58% at 43% 39%, #ff1a46 0%, rgba(255, 26, 70, 0) 100%);
  z-index: 0;
  pointer-events: none;
}
.analyse-manage {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  color: rgba(255, 255, 255, 0.12);
  font-size: 28rpx;
  z-index: 2;
}
.analyse-center {
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  margin-top: 26vh;
}
.analyse-progress {
  width: 174rpx;
  height: 156rpx;
  border-radius: 14px;
  position: relative;
  background: rgba(255, 255, 255, 0.01);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  overflow: visible;
}
.analyse-progress svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.analyse-progress-icon {
  width: 110rpx;
  height: 80rpx;
  z-index: 1;
}
.analyse-percent {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  margin-top: 24rpx;
}
.analyse-desc {
  color: #bfbfbf;
  font-size: 28rpx;
  text-align: center;
  margin-top: 16rpx;
}
.analyse-footer {
  width: 100vw;
  display: flex;
  justify-content: center;
  margin-top: 64rpx;
}
.analyse-btn-group {
  display: flex;
  gap: 20rpx; /* 按钮之间的间距 */
}
.analyse-continue-btn,
.analyse-view-result-btn {
  background-color: #383839;
  border-radius: 12px;
  padding: 14rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
}
.analyse-view-result-btn {
  background-color: #ff1a46; /* 查看结果按钮使用红色背景 */
}
.analyse-btn-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
</style>
