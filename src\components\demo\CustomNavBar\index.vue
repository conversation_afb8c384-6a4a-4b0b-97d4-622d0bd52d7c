<template>
  <view class="demo-custom-navbar">
    <CustomNavBar title="基础用法" />
    <view class="demo-block">基础用法</view>

    <CustomNavBar title="带返回" :showBack="true" />
    <view class="demo-block">带返回按钮</view>

    <CustomNavBar title="自定义颜色" background="#222" color="#fff" />
    <view class="demo-block">自定义背景和字体颜色</view>

    <CustomNavBar title="带右侧按钮" rightText="操作" @rightClick="onRightClick" />
    <view class="demo-block">带右侧按钮</view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/common/CustomNavBar/index.vue'

function onRightClick() {
  uni.showToast({
    title: '点击了右侧按钮',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.demo-custom-navbar {
  background: #f8f8f8;
  min-height: 100vh;
  .demo-block {
    margin: 32rpx 0 64rpx 0;
    padding: 32rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
    font-size: 28rpx;
    color: #333;
  }
}
</style>
