<template>
  <view class="design-test-page">
    <!-- 状态栏占位 -->
    <view class="design-test-page__status-bar"></view>
    
    <!-- 测试内容 -->
    <view class="design-test-page__content">
      <view class="design-test-page__header">
        <text class="design-test-page__title">设计稿视频播放器测试</text>
        <text class="design-test-page__subtitle">基于设计稿像素级复刻</text>
      </view>
      
      <!-- 设计稿预览 -->
      <view class="design-test-page__preview">
        <view class="design-test-page__preview-title">设计稿预览</view>
        <view class="design-test-page__preview-image">
          <text class="design-test-page__preview-text">视频播放器设计稿</text>
          <text class="design-test-page__preview-desc">包含两个模特展示服装的视频</text>
        </view>
      </view>
      
      <!-- 功能测试区域 -->
      <view class="design-test-page__test-section">
        <text class="design-test-page__section-title">功能测试</text>
        
        <view class="design-test-page__test-buttons">
          <view class="design-test-page__test-btn" @click="showVideoPlayer">
            <text class="design-test-page__test-btn-text">启动视频播放器</text>
          </view>
          
          <view class="design-test-page__test-btn design-test-page__test-btn--secondary" @click="showVideoPlayerWithPoster">
            <text class="design-test-page__test-btn-text">带封面播放</text>
          </view>
          
          <view class="design-test-page__test-btn design-test-page__test-btn--secondary" @click="showVideoPlayerAutoPlay">
            <text class="design-test-page__test-btn-text">自动播放</text>
          </view>
        </view>
      </view>
      
      <!-- 设计稿功能对照 -->
      <view class="design-test-page__features">
        <text class="design-test-page__section-title">设计稿功能对照</text>
        
        <view class="design-test-page__feature-list">
          <view class="design-test-page__feature-item">
            <text class="design-test-page__feature-icon">✅</text>
            <text class="design-test-page__feature-text">顶部关闭按钮 (X)</text>
          </view>
          
          <view class="design-test-page__feature-item">
            <text class="design-test-page__feature-icon">✅</text>
            <text class="design-test-page__feature-text">画质设置 (702P)</text>
          </view>
          
          <view class="design-test-page__feature-item">
            <text class="design-test-page__feature-icon">✅</text>
            <text class="design-test-page__feature-text">导出按钮 (红色)</text>
          </view>
          
          <view class="design-test-page__feature-item">
            <text class="design-test-page__feature-icon">✅</text>
            <text class="design-test-page__feature-text">时间显示 (00:03 / 00:21)</text>
          </view>
          
          <view class="design-test-page__feature-item">
            <text class="design-test-page__feature-icon">✅</text>
            <text class="design-test-page__feature-text">播放/暂停按钮</text>
          </view>
          
          <view class="design-test-page__feature-item">
            <text class="design-test-page__feature-icon">✅</text>
            <text class="design-test-page__feature-text">进度条 (红色)</text>
          </view>
          
          <view class="design-test-page__feature-item">
            <text class="design-test-page__feature-icon">✅</text>
            <text class="design-test-page__feature-text">后退/前进按钮</text>
          </view>
          
          <view class="design-test-page__feature-item">
            <text class="design-test-page__feature-icon">✅</text>
            <text class="design-test-page__feature-text">全屏按钮</text>
          </view>
        </view>
      </view>
      
      <!-- 测试日志 -->
      <view class="design-test-page__logs">
        <text class="design-test-page__section-title">测试日志</text>
        <view class="design-test-page__log-list">
          <text class="design-test-page__log-item" v-for="(log, index) in testLogs" :key="index">
            {{ log }}
          </text>
        </view>
      </view>
    </view>
    
    <!-- 视频播放器组件 -->
    <VideoPlayer
      v-if="isVideoPlayerVisible"
      :video-src="currentVideoSrc"
      :poster="currentPoster"
      :auto-play="autoPlay"
      :loop="false"
      :muted="false"
      :has-modal="false"
      @close="hideVideoPlayer"
      @play="onPlay"
      @pause="onPause"
      @ended="onEnded"
      @timeupdate="onTimeUpdate"
      @fullscreen="onFullscreen"
      @export="onExport"
      @error="onError"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import VideoPlayer from '@/components/business/VideoPlayer/index.vue'

// 响应式数据
const isVideoPlayerVisible = ref(false)
const currentVideoSrc = ref('')
const currentPoster = ref('')
const autoPlay = ref(false)
const testLogs = ref([])

// 示例视频源（模拟设计稿中的服装展示视频）
const videoSources = {
  default: 'https://www.w3schools.com/html/mov_bbb.mp4', // 临时使用，实际应该是服装展示视频
  withPoster: 'https://www.w3schools.com/html/mov_bbb.mp4',
  poster: 'https://www.w3schools.com/html/pic_mountain.jpg' // 临时使用，实际应该是服装封面
}

// 添加测试日志
const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  testLogs.value.unshift(`[${timestamp}] ${message}`)
  if (testLogs.value.length > 20) {
    testLogs.value.pop()
  }
}

// 测试方法
const showVideoPlayer = () => {
  currentVideoSrc.value = videoSources.default
  currentPoster.value = ''
  autoPlay.value = false
  isVideoPlayerVisible.value = true
  addLog('启动基础视频播放器测试')
}

const showVideoPlayerWithPoster = () => {
  currentVideoSrc.value = videoSources.withPoster
  currentPoster.value = videoSources.poster
  autoPlay.value = false
  isVideoPlayerVisible.value = true
  addLog('启动带封面视频播放器测试')
}

const showVideoPlayerAutoPlay = () => {
  currentVideoSrc.value = videoSources.default
  currentPoster.value = ''
  autoPlay.value = true
  isVideoPlayerVisible.value = true
  addLog('启动自动播放视频播放器测试')
}

const hideVideoPlayer = () => {
  isVideoPlayerVisible.value = false
  addLog('关闭视频播放器')
}

// 事件处理
const onPlay = () => {
  addLog('✅ 视频开始播放')
}

const onPause = () => {
  addLog('⏸️ 视频暂停播放')
}

const onEnded = () => {
  addLog('🏁 视频播放结束')
}

const onTimeUpdate = ({ current, total }) => {
  // 避免频繁更新日志
  if (Math.floor(current) % 5 === 0) {
    addLog(`📊 播放进度: ${Math.floor(current)}s / ${Math.floor(total)}s`)
  }
}

const onFullscreen = (isFullscreen) => {
  addLog(`🖥️ 全屏状态: ${isFullscreen ? '开启' : '关闭'}`)
}

const onExport = () => {
  addLog('📤 点击导出按钮')
  uni.showToast({
    title: '导出功能开发中',
    icon: 'none'
  })
}

const onError = (error) => {
  addLog(`❌ 视频播放错误: ${error}`)
}
</script>

<style lang="scss" scoped>
.design-test-page {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  &__status-bar {
    height: var(--status-bar-height);
    width: 100%;
  }

  &__content {
    flex: 1;
    padding: 24rpx;
  }

  &__header {
    margin-bottom: 48rpx;
    text-align: center;
  }

  &__title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 8rpx;
  }

  &__subtitle {
    font-size: 24rpx;
    color: #666;
    display: block;
  }

  &__preview {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
  }

  &__preview-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 24rpx;
  }

  &__preview-image {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12rpx;
    padding: 60rpx 32rpx;
    text-align: center;
  }

  &__preview-text {
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    display: block;
    margin-bottom: 12rpx;
  }

  &__preview-desc {
    color: rgba(255, 255, 255, 0.8);
    font-size: 24rpx;
    display: block;
  }

  &__test-section {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
  }

  &__section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 24rpx;
    display: block;
  }

  &__test-buttons {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }

  &__test-btn {
    background: #007aff;
    border-radius: 12rpx;
    padding: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      background: #0056cc;
    }

    &--secondary {
      background: #34c759;
    }
  }

  &__test-btn-text {
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
  }

  &__features {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
  }

  &__feature-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }

  &__feature-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  &__feature-icon {
    font-size: 24rpx;
  }

  &__feature-text {
    font-size: 26rpx;
    color: #333;
  }

  &__logs {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
  }

  &__log-list {
    max-height: 400rpx;
    overflow-y: auto;
  }

  &__log-item {
    font-size: 24rpx;
    color: #666;
    padding: 8rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    display: block;
    line-height: 1.4;

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>