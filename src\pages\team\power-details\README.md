# 算力明细页面

## 页面说明

算力明细页面用于展示团队算力的详细使用情况，包括剩余算力、可用算力、消耗明细和分配明细。

## 功能特性

- **算力概览**：显示团队剩余算力和可用算力
- **标签页切换**：支持消耗明细和分配明细的切换查看
- **空状态展示**：当无数据时显示友好的空状态界面
- **累计消耗弹窗**：点击右上角可查看累计消耗详情

## 页面结构

### 顶部导航栏
- 左侧：返回按钮
- 中间：页面标题"算力明细"
- 右侧：累计消耗按钮

### 算力概览卡片
- 左侧：团队剩余算力（可点击查看更多）
- 右侧：团队可用算力

### 标签页导航
- 消耗明细（默认激活）
- 分配明细

### 内容展示区域
- 根据选中的标签显示对应的明细列表
- 无数据时显示空状态界面

### 累计消耗弹窗
- 显示团队累计消耗算力信息
- 提供"我知道了"按钮关闭弹窗

## 交互功能

1. **返回功能**：点击左上角返回按钮返回上一页
2. **标签切换**：点击标签页切换查看不同明细
3. **算力详情**：点击团队剩余算力查看详情
4. **累计消耗**：点击右上角查看累计消耗弹窗
5. **弹窗关闭**：点击遮罩或按钮关闭弹窗

## 数据接口

页面需要以下数据接口：
- 获取团队算力概览数据
- 获取消耗明细列表
- 获取分配明细列表
- 获取累计消耗数据

## 样式规范

- 使用rpx单位确保跨设备兼容性
- 遵循BEM命名规范
- 支持安全区域适配
- 响应式布局设计

## 注意事项

- 页面使用自定义导航栏，需要处理状态栏高度
- 空状态图标使用CSS绘制，无需额外图片资源
- 弹窗使用固定定位，确保层级正确
- 列表数据为空时显示空状态界面