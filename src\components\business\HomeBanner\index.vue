<template>
  <view class="home-banner">
    <swiper 
      class="home-banner__swiper"
      :indicator-dots="showIndicators"
      :autoplay="autoplay"
      :interval="interval"
      :duration="duration"
      :circular="circular"
      indicator-color="rgba(255, 255, 255, 0.3)"
      indicator-active-color="rgba(255, 255, 255, 0.8)"
      @change="handleSwiperChange"
    >
      <swiper-item 
        v-for="(item, index) in computedBannerList" 
        :key="index"
        class="home-banner__swiper-item"
        @click="handleBannerClick(item, index)"
      >
        <!-- 轮播图片 -->
        <image 
          :src="item.image" 
          class="home-banner__image"
          mode="aspectFill"
        />
      </swiper-item>
    </swiper>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, computed, ref } from 'vue'

// Props
const props = defineProps({
  // 轮播图片列表
  bannerList: {
    type: Array,
    default: () => []
  },
  // 默认图片（当bannerList为空时使用）
  defaultImage: {
    type: String,
    default: ''
  },
  // 默认跳转链接
  defaultLink: {
    type: String,
    default: ''
  },
  // 是否显示指示器
  showIndicators: {
    type: Boolean,
    default: true
  },
  // 是否自动轮播
  autoplay: {
    type: Boolean,
    default: true
  },
  // 轮播间隔时间（毫秒）
  interval: {
    type: Number,
    default: 3000
  },
  // 滑动动画时长（毫秒）
  duration: {
    type: Number,
    default: 500
  },
  // 是否循环轮播
  circular: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['click', 'change'])

// 当前轮播索引
const currentIndex = ref(0)

// Computed
const computedBannerList = computed(() => {
  if (props.bannerList.length > 0) {
    return props.bannerList
  }
  
  // 默认轮播项
  if (props.defaultImage) {
    return [
      {
        image: props.defaultImage,
        link: props.defaultLink
      }
    ]
  }
  
  // 如果没有配置任何图片，返回空数组
  return []
})

// Methods
const handleSwiperChange = (e) => {
  currentIndex.value = e.detail.current
  emit('change', {
    current: e.detail.current,
    item: computedBannerList.value[e.detail.current]
  })
}

const handleBannerClick = (item, index) => {
  // 只有设置了链接才触发点击事件
  if (item.link) {
    emit('click', { item, index, link: item.link })
    
    uni.navigateTo({
      url: item.link
    })
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;

.home-banner {
  margin-bottom: 60rpx;
  position: relative;
  
  &__swiper {
    height: 400rpx;
    border-radius: 24rpx;
    overflow: hidden;
  }
  
  &__swiper-item {
    position: relative;
    overflow: hidden;
    border-radius: 24rpx;
  }
  
  &__image {
    width: 100%;
    height: 100%;
    border-radius: 24rpx;
  }
}

// 响应式适配
@media (max-width: 400px) {
  .home-banner {
    &__swiper {
      height: 320rpx;
      border-radius: 20rpx;
    }
    
    &__swiper-item {
      border-radius: 20rpx;
    }
    
    &__image {
      border-radius: 20rpx;
    }
  }
}
</style> 