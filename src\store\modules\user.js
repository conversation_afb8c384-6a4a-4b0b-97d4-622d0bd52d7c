import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import materialService from '@/service/material'
import { encryptPassword } from '@/utils/md5'
import http, { setCid, clearCid, setUserId, clearUserId, setToken, clearToken } from '@/utils/http'

// 检查是否为开发环境
const isDevelopment = process.env.NODE_ENV === 'development'

export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref(null)
  const token = ref(null)
  const refreshToken = ref(null)
  
  // 用户权限
  const permissions = ref([])
  const roles = ref([])
  
  // 用户设置
  const settings = ref({
    theme: 'light',
    language: 'zh-CN',
    notifications: {
      push: true,
      email: true,
      sms: false
    },
    privacy: {
      allowDataCollection: true,
      allowMarketing: false
    },
    autoSave: true,
    videoQuality: 'hd',
    defaultAspectRatio: '16:9'
  })
  
  // 用户统计
  const stats = ref({
    totalVideos: 0,
    totalViews: 0,
    totalLikes: 0,
    totalShares: 0,
    storageUsed: 0,
    storageLimit: 1024 * 1024 * 1024, // 1GB
    createCount: 0,
    createLimit: 100
  })
  
  // 计算属性
  const isLogin = computed(() => !!token.value)
  const isVip = computed(() => userInfo.value?.vip === true)
  const username = computed(() => userInfo.value?.username || '')
  const avatar = computed(() => userInfo.value?.avatar || '')
  const storageUsagePercent = computed(() => {
    if (!stats.value.storageLimit) return 0
    return (stats.value.storageUsed / stats.value.storageLimit) * 100
  })
  const createUsagePercent = computed(() => {
    if (!stats.value.createLimit) return 0
    return (stats.value.createCount / stats.value.createLimit) * 100
  })
  
  // 新增：字体列表
  const userFonts = ref([])

  // 新增：语音角色列表
  const userVoiceRoles = ref([])

  // 新增：音乐列表
  const userMusicList = ref([])

  // 登录
  const login = async (credentials) => {
    try {
      const { account, password, isEncrypted = false } = credentials
      // 密码加密处理
      const encryptedPassword = isEncrypted ? password : encryptPassword(password)
      const loginData = {
        username: account,
        password: encryptedPassword
      }
      const response = await http.post('/api/v1/auth/login', loginData)
      if (response && response.status_code === 1) {
        const userData = response.data
        // 保存用户信息
        const userInfoData = {
          id: userData.user_id,
          username: userData.username,
          nickname: userData.nickname,
          avatar: '',
          phone: account,
          vip: false,
          user_type: userData.user_type
        }
        userInfo.value = userInfoData
        uni.setStorageSync('userInfo', userInfoData)
        if (userData.token) {
          token.value = userData.token
          setToken(userData.token)
        }
        if (userData.refresh_token) {
          refreshToken.value = userData.refresh_token
          uni.setStorageSync('refresh_token', userData.refresh_token)
        }
        if (userData.cid) {
          setCid(userData.cid)
        }
        if (userData.user_id) {
          setUserId(userData.user_id)
        }
        permissions.value = userData.permissions || []
        roles.value = []
        await getUserStats()
        await initAfterLogin()
        // 登录成功后保存账号和加密密码
        uni.setStorageSync('login_account', account)
        uni.setStorageSync('login_password', encryptedPassword)
        return { success: true }
      } else {
        return { success: false, message: response?.message || '登录失败' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: error.message || '网络错误，请稍后重试' }
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      // 生产环境调用登出API（如果有登出接口的话）
      // if (token.value) {
      //   await http.post('/api/v1/auth/logout')
      // }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地数据（账号密码除外）
      token.value = null
      refreshToken.value = null
      userInfo.value = null
      permissions.value = []
      roles.value = []
      stats.value = {
        totalVideos: 0,
        totalViews: 0,
        totalLikes: 0,
        totalShares: 0,
        storageUsed: 0,
        storageLimit: 1024 * 1024 * 1024,
        createCount: 0,
        createLimit: 100
      }
      clearToken()
      uni.removeStorageSync('refresh_token')
      uni.removeStorageSync('userInfo')
      clearCid()
      clearUserId()
      userFonts.value = []
      userVoiceRoles.value = []
      userMusicList.value = []
      // 保留 login_account 和 login_password
    }
  }
  
  // 更新用户信息
  const updateUserInfo = async (info) => {
    try {
      // 调用真实API
      const response = await fetch('/api/user/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        },
        body: JSON.stringify(info)
      })
      
      const data = await response.json()
      
      if (data.success) {
        userInfo.value = { ...userInfo.value, ...data.user }
        return { success: true }
      } else {
        return { success: false, message: data.message }
      }
    } catch (error) {
      console.error('Update user info error:', error)
      return { success: false, message: '更新失败' }
    }
  }
  
  // 更新用户设置
  const updateSettings = async (newSettings) => {
    try {
      // 同步到服务器
      settings.value = { ...settings.value, ...newSettings }
      
      if (token.value) {
        await fetch('/api/user/settings', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token.value}`
          },
          body: JSON.stringify(settings.value)
        })
      }
      
      return { success: true }
    } catch (error) {
      console.error('Update settings error:', error)
      return { success: false, message: '设置更新失败' }
    }
  }
  
  // 获取用户统计
  const getUserStats = async () => {
    try {
      if (!token.value) return
      
      // 调用真实API
      const response = await fetch('/api/user/stats', {
        headers: {
          'Authorization': `Bearer ${token.value}`
        }
      })
      
      const data = await response.json()
      
      if (data.success) {
        stats.value = { ...stats.value, ...data.stats }
      }
    } catch (error) {
      console.error('Get user stats error:', error)
    }
  }
  
  // 检查权限
  const hasPermission = (permission) => {
    return permissions.value.includes(permission)
  }
  
  // 检查角色
  const hasRole = (role) => {
    return roles.value.includes(role)
  }
  
  // 增加创作次数
  const incrementCreateCount = () => {
    stats.value.createCount += 1
  }
  
  // 增加存储使用量
  const incrementStorageUsed = (size) => {
    stats.value.storageUsed += size
  }
  
  // 刷新Token
  const refreshTokens = async () => {
    try {
      if (!refreshToken.value) return false
      
      // 调用真实API
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ refreshToken: refreshToken.value })
      })
      
      const data = await response.json()
      
      if (data.success) {
        token.value = data.token
        refreshToken.value = data.refreshToken
        return true
      } else {
        // 刷新失败，需要重新登录
        await logout()
        return false
      }
    } catch (error) {
      console.error('Refresh token error:', error)
      await logout()
      return false
    }
  }
  
  // 从本地存储恢复用户状态
  const restoreUserState = () => {
    try {
      // 从本地存储恢复token
      const storedToken = uni.getStorageSync('token')
      if (storedToken) {
        token.value = storedToken
      }
      
      // 从本地存储恢复refreshToken
      const storedRefreshToken = uni.getStorageSync('refresh_token')
      if (storedRefreshToken) {
        refreshToken.value = storedRefreshToken
      }
      
      // 从本地存储恢复用户信息
      const storedUserInfo = uni.getStorageSync('userInfo')
      if (storedUserInfo) {
        userInfo.value = storedUserInfo
      }
      
      console.log('用户状态恢复完成:', {
        hasToken: !!token.value,
        hasRefreshToken: !!refreshToken.value,
        hasUserInfo: !!userInfo.value
      })
    } catch (error) {
      console.error('恢复用户状态失败:', error)
    }
  }
  
  // 登录后/冷启动后初始化（可扩展多个接口）
  const initAfterLogin = async () => {
  }

  return {
    // 状态
    userInfo,
    token,
    refreshToken,
    permissions,
    roles,
    settings,
    stats,
    userFonts,
    userVoiceRoles,
    userMusicList,
    
    // 计算属性
    isLogin,
    isVip,
    username,
    avatar,
    storageUsagePercent,
    createUsagePercent,
    
    // 方法
    login,
    logout,
    updateUserInfo,
    updateSettings,
    getUserStats,
    hasPermission,
    hasRole,
    incrementCreateCount,
    incrementStorageUsed,
    refreshTokens,
    initAfterLogin, // 新增
    restoreUserState // 新增
  }
}) 