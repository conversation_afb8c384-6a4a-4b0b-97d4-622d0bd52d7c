/**
 * 系统相关接口服务
 */

import http from '@/utils/http'

const systemService = {
  // 获取系统配置
  getSystemConfig() {
    return http.get('/api/system/config')
  },
  
  // 获取应用版本信息
  getAppVersion() {
    return http.get('/api/system/version')
  },
  
  // 检查应用更新
  checkAppUpdate() {
    return http.get('/api/system/check-update')
  },
  
  // 获取系统公告
  getSystemNotices(params) {
    return http.get('/api/system/notices', params)
  },
  
  // 获取公告详情
  getNoticeDetail(id) {
    return http.get(`/api/system/notice/${id}`)
  },
  
  // 标记公告为已读
  markNoticeAsRead(id) {
    return http.post(`/api/system/notice/${id}/read`)
  },
  
  // 获取帮助文档
  getHelpDocs(params) {
    return http.get('/api/system/help', params)
  },
  
  // 获取帮助文档详情
  getHelpDocDetail(id) {
    return http.get(`/api/system/help/${id}`)
  },
  
  // 搜索帮助文档
  searchHelpDocs(params) {
    return http.get('/api/system/help/search', params)
  },
  
  // 获取常见问题
  getFAQ(params) {
    return http.get('/api/system/faq', params)
  },
  
  // 获取FAQ详情
  getFAQDetail(id) {
    return http.get(`/api/system/faq/${id}`)
  },
  
  // 提交意见反馈
  submitFeedback(data) {
    return http.post('/api/system/feedback', data)
  },
  
  // 获取用户反馈列表
  getUserFeedback(params) {
    return http.get('/api/system/user-feedback', params)
  },
  
  // 获取反馈详情
  getFeedbackDetail(id) {
    return http.get(`/api/system/feedback/${id}`)
  },
  
  // 上传反馈附件
  uploadFeedbackAttachment(filePath, formData = {}) {
    return http.upload('/api/system/feedback/attachment', filePath, formData)
  },
  
  // 获取客服联系方式
  getCustomerService() {
    return http.get('/api/system/customer-service')
  },
  
  // 发起客服会话
  startCustomerChat(data) {
    return http.post('/api/system/customer-chat', data)
  },
  
  // 获取隐私政策
  getPrivacyPolicy() {
    return http.get('/api/system/privacy-policy')
  },
  
  // 获取用户协议
  getUserAgreement() {
    return http.get('/api/system/user-agreement')
  },
  
  // 获取服务条款
  getTermsOfService() {
    return http.get('/api/system/terms-of-service')
  },
  
  // 获取关于我们
  getAboutUs() {
    return http.get('/api/system/about-us')
  },
  
  // 获取联系我们
  getContactUs() {
    return http.get('/api/system/contact-us')
  },
  
  // 上报错误日志
  reportError(data) {
    return http.post('/api/system/error-report', data)
  },
  
  // 上报崩溃日志
  reportCrash(data) {
    return http.post('/api/system/crash-report', data)
  },
  
  // 上报性能数据
  reportPerformance(data) {
    return http.post('/api/system/performance-report', data)
  },
  
  // 获取系统状态
  getSystemStatus() {
    return http.get('/api/system/status')
  },
  
  // 获取服务器时间
  getServerTime() {
    return http.get('/api/system/time')
  },
  
  // 获取地区列表
  getRegionList() {
    return http.get('/api/system/regions')
  },
  
  // 获取城市列表
  getCityList(regionId) {
    return http.get(`/api/system/cities/${regionId}`)
  },
  
  // 获取语言列表
  getLanguageList() {
    return http.get('/api/system/languages')
  },
  
  // 获取货币列表
  getCurrencyList() {
    return http.get('/api/system/currencies')
  },
  
  // 获取时区列表
  getTimezoneList() {
    return http.get('/api/system/timezones')
  },
  
  // 获取系统统计数据
  getSystemStats() {
    return http.get('/api/system/stats')
  },
  
  // 获取用户协议版本
  getAgreementVersion() {
    return http.get('/api/system/agreement-version')
  },
  
  // 同意用户协议
  agreeToTerms(data) {
    return http.post('/api/system/agree-terms', data)
  },
  
  // 获取系统维护信息
  getMaintenanceInfo() {
    return http.get('/api/system/maintenance')
  },
  
  // 获取系统限制信息
  getSystemLimits() {
    return http.get('/api/system/limits')
  },
  
  // 获取功能开关配置
  getFeatureFlags() {
    return http.get('/api/system/feature-flags')
  },
  
  // 获取A/B测试配置
  getABTestConfig() {
    return http.get('/api/system/ab-test')
  },
  
  // 上报A/B测试结果
  reportABTestResult(data) {
    return http.post('/api/system/ab-test-result', data)
  },
  
  // 获取推送配置
  getPushConfig() {
    return http.get('/api/system/push-config')
  },
  
  // 注册推送token
  registerPushToken(data) {
    return http.post('/api/system/push-token', data)
  },
  
  // 获取分享配置
  getShareConfig() {
    return http.get('/api/system/share-config')
  },
  
  // 获取支付配置
  getPaymentConfig() {
    return http.get('/api/system/payment-config')
  },
  
  // 获取第三方登录配置
  getThirdPartyLoginConfig() {
    return http.get('/api/system/third-party-login')
  },
  
  // 获取CDN配置
  getCDNConfig() {
    return http.get('/api/system/cdn-config')
  },
  
  // 获取上传配置
  getUploadConfig() {
    return http.get('/api/system/upload-config')
  },
  
  // 获取下载配置
  getDownloadConfig() {
    return http.get('/api/system/download-config')
  },
  
  // 获取缓存配置
  getCacheConfig() {
    return http.get('/api/system/cache-config')
  },
  
  // 清除缓存
  clearCache(type) {
    return http.post('/api/system/clear-cache', { type })
  },
  
  // 获取系统日志
  getSystemLogs(params) {
    return http.get('/api/system/logs', params)
  },
  
  // 获取操作日志
  getOperationLogs(params) {
    return http.get('/api/system/operation-logs', params)
  },
  
  // 获取安全配置
  getSecurityConfig() {
    return http.get('/api/system/security-config')
  },
  
  // 获取内容审核配置
  getContentModerationConfig() {
    return http.get('/api/system/content-moderation')
  },
  
  // 内容审核
  moderateContent(data) {
    return http.post('/api/system/moderate-content', data)
  },
  
  // 获取敏感词列表
  getSensitiveWords() {
    return http.get('/api/system/sensitive-words')
  },
  
  // 检查敏感词
  checkSensitiveWords(data) {
    return http.post('/api/system/check-sensitive-words', data)
  }
}

export default systemService 