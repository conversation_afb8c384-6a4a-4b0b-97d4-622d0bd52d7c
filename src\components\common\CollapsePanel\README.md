# CollapsePanel 折叠面板组件

## 组件描述

CollapsePanel 是一个可折叠的面板组件，支持展开/收起动画、图标显示和多种状态控制。适用于FAQ、设置项、帮助文档等需要折叠展示的场景。

## 功能特性

- 🎯 **折叠展开** - 支持点击头部展开/收起内容
- 🎨 **动画效果** - 支持平滑的展开/收起动画
- 📱 **图标支持** - 支持在标题前显示图标
- 🔄 **状态控制** - 支持禁用状态和默认展开状态
- 🎭 **事件回调** - 提供展开/收起状态变化事件

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | - | 面板标题（必填） |
| icon | String | '' | 标题前显示的图标 |
| expanded | Boolean | false | 是否默认展开 |
| disabled | Boolean | false | 是否禁用 |
| animation | Boolean | true | 是否启用动画效果 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | expanded: Boolean | 展开状态变化时触发 |
| expand | - | 展开时触发 |
| collapse | - | 收起时触发 |

## Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| default | 面板内容 |

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <CollapsePanel title="基础用法">
      <text>这是折叠面板的内容区域，可以放置任何内容。</text>
    </CollapsePanel>
  </view>
</template>

<script setup>
import CollapsePanel from '@/components/common/CollapsePanel/index.vue'
</script>
```

### 带图标的面板

```vue
<template>
  <view class="panel-demo">
    <CollapsePanel 
      title="常见问题"
      icon="❓"
      @change="handleChange"
    >
      <view class="faq-content">
        <text>1. 如何开始使用？</text>
        <text>点击首页的"开始创作"按钮即可开始使用。</text>
        <text>2. 支持哪些格式？</text>
        <text>支持 MP4、AVI、MOV 等常见视频格式。</text>
      </view>
    </CollapsePanel>
    
    <CollapsePanel 
      title="使用说明"
      icon="📖"
      @change="handleChange"
    >
      <view class="guide-content">
        <text>详细的使用说明和操作指南...</text>
      </view>
    </CollapsePanel>
  </view>
</template>

<script setup>
import CollapsePanel from '@/components/common/CollapsePanel/index.vue'

const handleChange = (expanded) => {
  console.log('面板状态变化:', expanded ? '展开' : '收起')
}
</script>

<style lang="scss" scoped>
.panel-demo {
  padding: 20rpx;
}

.faq-content,
.guide-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  
  text {
    line-height: 1.6;
  }
}
</style>
```

### 默认展开状态

```vue
<template>
  <view>
    <CollapsePanel 
      title="默认展开"
      :expanded="true"
      icon="📋"
      @expand="handleExpand"
      @collapse="handleCollapse"
    >
      <text>这个面板默认是展开状态的。</text>
    </CollapsePanel>
  </view>
</template>

<script setup>
import CollapsePanel from '@/components/common/CollapsePanel/index.vue'

const handleExpand = () => {
  console.log('面板展开')
}

const handleCollapse = () => {
  console.log('面板收起')
}
</script>
```

### 禁用状态

```vue
<template>
  <view>
    <CollapsePanel 
      title="禁用状态"
      :disabled="true"
      icon="🔒"
    >
      <text>这个面板被禁用了，无法点击展开。</text>
    </CollapsePanel>
  </view>
</template>

<script setup>
import CollapsePanel from '@/components/common/CollapsePanel/index.vue'
</script>
```

### 无动画模式

```vue
<template>
  <view>
    <CollapsePanel 
      title="无动画模式"
      :animation="false"
      icon="⚡"
      @change="handleChange"
    >
      <text>这个面板没有展开/收起动画效果。</text>
    </CollapsePanel>
  </view>
</template>

<script setup>
import CollapsePanel from '@/components/common/CollapsePanel/index.vue'

const handleChange = (expanded) => {
  console.log('状态变化:', expanded)
}
</script>
```

### 复杂内容

```vue
<template>
  <view>
    <CollapsePanel 
      title="复杂内容示例"
      icon="🎨"
      @change="handleChange"
    >
      <view class="complex-content">
        <view class="content-section">
          <text class="section-title">功能特点</text>
          <view class="feature-list">
            <text>• 支持多种视频格式</text>
            <text>• 智能剪辑功能</text>
            <text>• 一键分享到社交平台</text>
          </view>
        </view>
        
        <view class="content-section">
          <text class="section-title">使用步骤</text>
          <view class="step-list">
            <text>1. 选择视频文件</text>
            <text>2. 设置剪辑参数</text>
            <text>3. 点击开始处理</text>
            <text>4. 下载成品视频</text>
          </view>
        </view>
      </view>
    </CollapsePanel>
  </view>
</template>

<script setup>
import CollapsePanel from '@/components/common/CollapsePanel/index.vue'

const handleChange = (expanded) => {
  console.log('复杂面板状态:', expanded)
}
</script>

<style lang="scss" scoped>
.complex-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.section-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.feature-list,
.step-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding-left: 20rpx;
  
  text {
    line-height: 1.5;
    color: #666;
  }
}
</style>
```

### 动态控制

```vue
<template>
  <view>
    <view class="control-buttons">
      <PrimaryButton 
        text="全部展开"
        @click="expandAll"
      />
      <PrimaryButton 
        text="全部收起"
        @click="collapseAll"
      />
    </view>
    
    <CollapsePanel 
      v-for="(item, index) in panelList"
      :key="index"
      :title="item.title"
      :icon="item.icon"
      :expanded="item.expanded"
      @change="(expanded) => handlePanelChange(index, expanded)"
    >
      <text>{{ item.content }}</text>
    </CollapsePanel>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import CollapsePanel from '@/components/common/CollapsePanel/index.vue'
import PrimaryButton from '@/components/common/PrimaryButton/index.vue'

const panelList = ref([
  {
    title: '面板一',
    icon: '📝',
    content: '这是第一个面板的内容',
    expanded: false
  },
  {
    title: '面板二',
    icon: '🎯',
    content: '这是第二个面板的内容',
    expanded: false
  },
  {
    title: '面板三',
    icon: '🚀',
    content: '这是第三个面板的内容',
    expanded: false
  }
])

const handlePanelChange = (index, expanded) => {
  panelList.value[index].expanded = expanded
}

const expandAll = () => {
  panelList.value.forEach(panel => {
    panel.expanded = true
  })
}

const collapseAll = () => {
  panelList.value.forEach(panel => {
    panel.expanded = false
  })
}
</script>

<style lang="scss" scoped>
.control-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
}
</style>
```

## 样式定制

组件使用 SCSS 编写，主要样式特点：

- 边框：`1rpx solid $border-light`
- 圆角：`$radius-base`
- 过渡动画：`$transition-fast`
- 激活状态：蓝色背景，白色文字

## 交互效果

- **点击效果**：点击头部时有背景色变化
- **展开动画**：平滑的高度变化动画
- **箭头旋转**：展开时箭头向下，收起时向右
- **状态反馈**：激活状态有明显的视觉反馈

## 注意事项

1. `title` 是必填参数，不能为空
2. 禁用状态下无法点击展开/收起
3. 动画效果可以通过 `animation` 属性关闭
4. 组件会自动处理展开状态的同步
5. 内容区域支持任何类型的子元素

## 最佳实践

1. **标题简洁**：保持标题简洁明了，建议不超过 10 个字符
2. **图标使用**：使用合适的图标增强视觉效果
3. **内容组织**：合理组织面板内容，避免过于复杂
4. **状态管理**：合理使用默认展开状态
5. **用户体验**：考虑是否需要禁用状态

## 与其他组件配合

CollapsePanel 通常与以下组件配合使用：

- **PrimaryButton**: 控制按钮
- **Form**: 表单分组
- **List**: 列表项分组

提供更好的内容组织和用户体验。 