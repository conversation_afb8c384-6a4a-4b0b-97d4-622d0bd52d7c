<template>
  <view class="bottom-tab-bar">
    <view 
      v-for="(tab, index) in tabList" 
      :key="index"
      class="bottom-tab-bar__item"
      :class="{ 'bottom-tab-bar__item--active': currentIndex === index }"
      @click="handleTabClick(index)"
    >
      <view class="bottom-tab-bar__icon">
        <image 
          :src="currentIndex === index ? tab.activeIcon : tab.icon" 
          mode="aspectFit"
          class="bottom-tab-bar__icon-image"
        />
      </view>
      <text class="bottom-tab-bar__text">{{ tab.text }}</text>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, onMounted, onUnmounted } from 'vue'

// 导入图标
import tabHomeIcon from '@/static/icons/tab/tab_home.png'
import tabHomeActiveIcon from '@/static/icons/tab/tab_home_active.png'
import tabTemplateIcon from '@/static/icons/tab/tab_template.png'
import tabTemplateActiveIcon from '@/static/icons/tab/tab_template_active.png'
import tabTrendingIcon from '@/static/icons/tab/tab_trending.png'
import tabTrendingActiveIcon from '@/static/icons/tab/tab_trending_active.svg'
import tabHistoryIcon from '@/static/icons/tab/tab_history.png'
import tabHistoryActiveIcon from '@/static/icons/tab/tab_history_active.png'
import tabProfileIcon from '@/static/icons/tab/tab_profile.png'
import tabProfileActiveIcon from '@/static/icons/tab/tab_profile_active.svg'

// Props
const props = defineProps({
  // 当前激活的标签索引
  currentIndex: {
    type: Number,
    default: 0
  },
  // 标签列表
  tabList: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['change'])

// 默认标签列表
const defaultTabList = [
  { 
    text: '首页', 
    icon: tabHomeIcon, 
    activeIcon: tabHomeActiveIcon,
    path: 'pages/index/index' 
  },
  { 
    text: '穿版', 
    icon: tabTemplateIcon, 
    activeIcon: tabTemplateActiveIcon,
    path: 'pages/template/index' 
  },
  { 
    text: '爆款模板', 
    icon: tabTrendingIcon, 
    activeIcon: tabTrendingActiveIcon,
    path: 'pages/trending/index' 
  },
  { 
    text: '创作历史', 
    icon: tabHistoryIcon, 
    activeIcon: tabHistoryActiveIcon,
    path: 'pages/history/index' 
  },
  { 
    text: '我的', 
    icon: tabProfileIcon, 
    activeIcon: tabProfileActiveIcon,
    path: 'pages/profile/index' 
  }
  ]

// 获取标签列表
const tabList = props.tabList.length > 0 ? props.tabList : defaultTabList

// Methods
const handleTabClick = (index) => {
  if (index === props.currentIndex) return
  
  const tab = tabList[index]
  if (tab && tab.path) {
    // 直接导航到指定页面
    uni.redirectTo({
      url: `/${tab.path}`,
      fail: (err) => {
        console.error('Navigation failed:', err)
      }
    })
  }
  
  // 触发change事件
  emit('change', index)
}

// 生命周期
onMounted(() => {
  // 组件挂载完成
})

onUnmounted(() => {
  // 组件卸载
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;

.bottom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 1);
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 112rpx; // 56px
  padding: 20rpx 46rpx 20rpx 54rpx; // 匹配设计稿padding
  z-index: 100;
  
  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    &--active {
      .bottom-tab-bar__text {
        color: rgba(255, 0, 67, 1); // 匹配设计稿的红色
      }
    }
  }
  
  &__icon {
    width: 44rpx; // 22px
    height: 44rpx; // 22px
    margin-bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &-image {
      width: 44rpx;
      height: 44rpx;
    }
  }
  
  &__text {
    font-size: 20rpx; // 10px，匹配设计稿
    color: rgba(140, 140, 140, 1); // 匹配设计稿的灰色
    line-height: 28rpx; // 14px
    text-align: center;
    white-space: nowrap;
  }
}
</style> 