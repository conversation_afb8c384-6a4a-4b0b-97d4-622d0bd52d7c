// 爆款模块API服务
import http from '@/utils/http'

/**
 * 获取爆款视频列表
 * @param {Object} params 请求参数
 * @param {string} params.category 分类ID
 * @param {string} params.keyword 搜索关键词
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 */
export const getTrendingVideos = (params = {}) => {
  return http.get('/api/trending/videos', {
    category: params.category || 'all',
    keyword: params.keyword || '',
    page: params.page || 1,
    pageSize: params.pageSize || 10
  })
}

/**
 * 获取分类列表
 */
export const getCategories = () => {
  return http.get('/api/trending/categories')
}

/**
 * 点赞视频
 * @param {string} videoId 视频ID
 */
export const likeVideo = (videoId) => {
  return http.post('/api/trending/like', { videoId })
}

/**
 * 取消点赞视频
 * @param {string} videoId 视频ID
 */
export const unlikeVideo = (videoId) => {
  return http.post('/api/trending/unlike', { videoId })
}

/**
 * 获取视频详情
 * @param {string} videoId 视频ID
 */
export const getVideoDetail = (videoId) => {
  return http.get(`/api/trending/video/${videoId}`)
}

/**
 * 关注用户
 * @param {string} userId 用户ID
 */
export const followUser = (userId) => {
  return http.post('/api/user/follow', { userId })
}

/**
 * 取消关注用户
 * @param {string} userId 用户ID
 */
export const unfollowUser = (userId) => {
  return http.post('/api/user/unfollow', { userId })
}

// 模拟数据（开发阶段使用）
export const mockTrendingVideos = (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: 1,
          cover: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/ff2dc603c7c44b9cac5aa54cf6cb8a3e_mergeImage.png',
          likes: '9999',
          tag: '利润走量款中等毛利',
          description: '当你老了你会如何回答以下…',
          category: 'women',
          author: {
            id: 'user1',
            name: '时尚达人',
            avatar: '/static/images/avatar1.png'
          },
          duration: '00:30',
          views: '12.5万'
        },
        {
          id: 2,
          cover: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/ec62f6ebd23a46588b9f66dc7838c5bb_mergeImage.png',
          likes: '8888',
          tag: '利润走量款中等毛利',
          description: '当你老了你会如何回答以下…',
          category: 'women',
          author: {
            id: 'user2',
            name: '潮流先锋',
            avatar: '/static/images/avatar2.png'
          },
          duration: '00:45',
          views: '8.9万'
        },
        {
          id: 3,
          cover: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/c753eb05babb4f958242471bcc42cadc_mergeImage.png',
          likes: '7777',
          tag: '利润走量款中等毛利',
          description: '在深圳龙华，你只要月薪超…',
          category: 'women',
          author: {
            id: 'user3',
            name: '穿搭博主',
            avatar: '/static/images/avatar3.png'
          },
          duration: '01:20',
          views: '15.2万'
        },
        {
          id: 4,
          cover: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/ab620554f5ff42f3817a778370bb333e_mergeImage.png',
          likes: '6666',
          tag: '利润走量款中等毛利',
          description: '在深圳龙华，你只要月薪超…',
          category: 'women',
          author: {
            id: 'user4',
            name: '时尚编辑',
            avatar: '/static/images/avatar4.png'
          },
          duration: '00:55',
          views: '6.8万'
        }
      ]
      
      // 根据分类过滤
      let filteredData = mockData
      if (params.category && params.category !== 'all') {
        filteredData = mockData.filter(item => item.category === params.category)
      }
      
      // 根据关键词搜索
      if (params.keyword) {
        filteredData = filteredData.filter(item => 
          item.description.includes(params.keyword) || 
          item.tag.includes(params.keyword)
        )
      }
      
      resolve({
        code: 200,
        data: {
          list: filteredData,
          total: filteredData.length,
          hasMore: params.page < 3 // 模拟只有3页数据
        },
        message: '获取成功'
      })
    }, 500) // 模拟网络延迟
  })
}

export const mockCategories = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: [
          { id: 'all', name: '全部', count: 156 },
          { id: 'women', name: '女装', count: 89 },
          { id: 'children', name: '童装', count: 34 },
          { id: 'men', name: '男装', count: 23 },
          { id: 'persona', name: '人设', count: 12 },
          { id: 'store', name: '店设', count: 8 }
        ],
        message: '获取成功'
      })
    }, 300)
  })
} 