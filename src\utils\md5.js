/**
 * MD5加密工具类
 * 用于密码加密处理
 */

// MD5常量
const K = [
  0xd76aa478, 0xe8c7b756, 0x242070db, 0xc1bdceee,
  0xf57c0faf, 0x4787c62a, 0xa8304613, 0xfd469501,
  0x698098d8, 0x8b44f7af, 0xffff5bb1, 0x895cd7be,
  0x6b901122, 0xfd987193, 0xa679438e, 0x49b40821,
  0xf61e2562, 0xc040b340, 0x265e5a51, 0xe9b6c7aa,
  0xd62f105d, 0x02441453, 0xd8a1e681, 0xe7d3fbc8,
  0x21e1cde6, 0xc33707d6, 0xf4d50d87, 0x455a14ed,
  0xa9e3e905, 0xfcefa3f8, 0x676f02d9, 0x8d2a4c8a,
  0xfffa3942, 0x8771f681, 0x6d9d6122, 0xfde5380c,
  0xa4beea44, 0x4bdecfa9, 0xf6bb4b60, 0xbebfbc70,
  0x289b7ec6, 0xeaa127fa, 0xd4ef3085, 0x04881d05,
  0xd9d4d039, 0xe6db99e5, 0x1fa27cf8, 0xc4ac5665,
  0xf4292244, 0x432aff97, 0xab9423a7, 0xfc93a039,
  0x655b59c3, 0x8f0ccc92, 0xffeff47d, 0x85845dd1,
  0x6fa87e4f, 0xfe2ce6e0, 0xa3014314, 0x4e0811a1,
  0xf7537e82, 0xbd3af235, 0x2ad7d2bb, 0xeb86d391
]

const S = [
  7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22,
  5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20,
  4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23,
  6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21
]

// 左移函数
function leftRotate(x, c) {
  return (x << c) | (x >>> (32 - c))
}

// 转换为小端字节序
function toLittleEndian(str) {
  const result = []
  for (let i = 0; i < str.length; i += 4) {
    result.push(
      str.charCodeAt(i) |
      (str.charCodeAt(i + 1) << 8) |
      (str.charCodeAt(i + 2) << 16) |
      (str.charCodeAt(i + 3) << 24)
    )
  }
  return result
}

// 转换为十六进制字符串
function toHexString(num) {
  let hex = ''
  for (let i = 0; i < 4; i++) {
    const byte = (num >>> (i * 8)) & 0xff
    hex += byte.toString(16).padStart(2, '0')
  }
  return hex
}

/**
 * MD5加密函数
 * @param {string} message - 要加密的字符串
 * @returns {string} - MD5加密后的字符串（小写）
 */
export function md5(message) {
  // 初始化变量
  let a = 0x67452301
  let b = 0xefcdab89
  let c = 0x98badcfe
  let d = 0x10325476

  // 消息长度
  const msgLength = message.length * 8

  // 填充消息
  let paddedMessage = message + '\x80'
  while ((paddedMessage.length + 8) % 64 !== 0) {
    paddedMessage += '\x00'
  }
  paddedMessage += String.fromCharCode(
    msgLength & 0xff,
    (msgLength >>> 8) & 0xff,
    (msgLength >>> 16) & 0xff,
    (msgLength >>> 24) & 0xff,
    0, 0, 0, 0
  )

  // 处理每个512位块
  for (let i = 0; i < paddedMessage.length; i += 64) {
    const chunk = paddedMessage.substr(i, 64)
    const M = toLittleEndian(chunk)

    const AA = a
    const BB = b
    const CC = c
    const DD = d

    // 主循环
    for (let j = 0; j < 64; j++) {
      let f, g

      if (j < 16) {
        f = (b & c) | (~b & d)
        g = j
      } else if (j < 32) {
        f = (d & b) | (~d & c)
        g = (5 * j + 1) % 16
      } else if (j < 48) {
        f = b ^ c ^ d
        g = (3 * j + 5) % 16
      } else {
        f = c ^ (b | ~d)
        g = (7 * j) % 16
      }

      const temp = d
      d = c
      c = b
      b = b + leftRotate((a + f + K[j] + M[g]), S[j])
      a = temp
    }

    a = (a + AA) >>> 0
    b = (b + BB) >>> 0
    c = (c + CC) >>> 0
    d = (d + DD) >>> 0
  }

  // 返回结果
  return toHexString(a) + toHexString(b) + toHexString(c) + toHexString(d)
}

/**
 * 密码加密函数（MD5 + 转大写）
 * @param {string} password - 原始密码
 * @returns {string} - 加密后的密码（大写）
 */
export function encryptPassword(password) {
  if (!password) return ''
  return md5(password.toString()).toUpperCase()
}

// 测试函数
export function testMd5() {
  const testCases = [
    { input: 'admin', expected: '21232F297A57A5A743894A0E4A801FC3' },
    { input: 'test', expected: '098F6BCD4621D373CADE4E832627B4F6' },
    { input: '', expected: 'D41D8CD98F00B204E9800998ECF8427E' }
  ]

  console.log('MD5测试结果:')
  testCases.forEach(({ input, expected }) => {
    const result = encryptPassword(input)
    const passed = result === expected
    console.log(`${input} -> ${result} ${passed ? '✓' : '✗'}`)
  })
} 