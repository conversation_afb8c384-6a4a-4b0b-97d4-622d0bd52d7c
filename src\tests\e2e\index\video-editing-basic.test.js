// 视频剪辑功能基础测试
const VideoEditingHelpers = require('../../helpers/video-editing-helpers')
const CommonHelpers = require('../common/common-helpers')
const TestData = require('../../helpers/test-data')

describe('视频剪辑功能 - 基础测试', () => {
  let page
  
  beforeEach(async () => {
    // 每个测试前重置页面状态
    await VideoEditingHelpers.navigateToVideoEditingPage()
    await VideoEditingHelpers.waitForPageLoad()
    page = await program.currentPage()
  })
  
  afterEach(async () => {
    // 测试后截图
    await VideoEditingHelpers.takeScreenshot('test-end')
  })
  
  describe('页面加载和初始化', () => {
    
    test('页面正常加载并显示必要元素', async () => {
      console.log('🧪 测试：页面正常加载')
      
      // 检查页面标题
      await VideoEditingHelpers.checkPageTitle('视频智能剪辑')
      
      // 检查主要模块是否显示
      await CommonHelpers.assertElementExists(
        VideoEditingHelpers.selectors.selectVideoButton,
        '选择视频按钮应该存在'
      )
      
      await CommonHelpers.assertElementExists(
        VideoEditingHelpers.selectors.scriptSettingButton,
        '文案设置按钮应该存在'
      )
      
      await CommonHelpers.assertElementExists(
        VideoEditingHelpers.selectors.systemVoiceMode,
        '系统配音模式应该存在'
      )
      
      await CommonHelpers.assertElementExists(
        VideoEditingHelpers.selectors.systemMusicMode,
        '系统音乐模式应该存在'
      )
      
      await CommonHelpers.assertElementExists(
        VideoEditingHelpers.selectors.generateButton,
        '生成按钮应该存在'
      )
      
      console.log('✅ 页面加载测试通过')
    })
    
    test('页面返回功能正常', async () => {
      console.log('🧪 测试：页面返回功能')
      
      // 记录当前页面路径
      const currentUrl = await page.url()
      console.log('当前页面URL:', currentUrl)
      
      // 点击返回按钮
      await VideoEditingHelpers.clickBackButton()
      
      // 等待页面跳转
      await CommonHelpers.wait(2000)
      
      // 检查是否返回到上一页
      const newUrl = await page.url()
      console.log('返回后页面URL:', newUrl)
      
      // 应该不在创作页面了
      expect(newUrl).not.toContain('/pages/create/index')
      
      console.log('✅ 页面返回功能测试通过')
    })
    
    test('初始状态正确', async () => {
      console.log('🧪 测试：初始状态检查')
      
      // 检查初始没有上传的视频素材
      await VideoEditingHelpers.checkUploadedMaterialCount(0)
      
      // 检查初始文案设置
      await CommonHelpers.assertElementTextContains(
        VideoEditingHelpers.selectors.customScriptItem,
        '已设置2条文案',
        '初始应该有2条空文案'
      )
      
      // 检查默认配音模式为系统声音
      const systemVoiceMode = await page.$(VideoEditingHelpers.selectors.systemVoiceMode)
      const hasActiveClass = await systemVoiceMode.getAttribute('class')
      expect(hasActiveClass).toContain('mode-option--active')
      
      // 检查默认音乐模式为系统声音
      const systemMusicMode = await page.$(VideoEditingHelpers.selectors.systemMusicMode)
      const musicModeClass = await systemMusicMode.getAttribute('class')
      expect(musicModeClass).toContain('mode-option--active')
      
      console.log('✅ 初始状态检查通过')
    })
    
    test('页面元素响应性', async () => {
      console.log('🧪 测试：页面元素响应性')
      
      // 测试各个按钮的点击响应
      const buttons = [
        VideoEditingHelpers.selectors.selectVideoButton,
        VideoEditingHelpers.selectors.scriptSettingButton,
        VideoEditingHelpers.selectors.systemVoiceMode,
        VideoEditingHelpers.selectors.systemMusicMode
      ]
      
      for (const buttonSelector of buttons) {
        console.log(`测试按钮响应性: ${buttonSelector}`)
        
        // 点击按钮
        const success = await CommonHelpers.clickElement(buttonSelector, { waitAfter: 500 })
        expect(success).toBe(true)
        
        // 等待UI更新
        await CommonHelpers.wait(500)
      }
      
      console.log('✅ 页面元素响应性测试通过')
    })
    
    test('页面滚动功能', async () => {
      console.log('🧪 测试：页面滚动功能')
      
      // 滚动到底部的生成按钮
      await CommonHelpers.scrollToElement(VideoEditingHelpers.selectors.generateButton)
      
      // 检查生成按钮是否可见
      const generateButtonVisible = await CommonHelpers.elementExists(
        VideoEditingHelpers.selectors.generateButton
      )
      expect(generateButtonVisible).toBe(true)
      
      // 滚动回顶部
      await CommonHelpers.scrollToElement(VideoEditingHelpers.selectors.pageTitle)
      
      // 检查页面标题是否可见
      const titleVisible = await CommonHelpers.elementExists(
        VideoEditingHelpers.selectors.pageTitle
      )
      expect(titleVisible).toBe(true)
      
      console.log('✅ 页面滚动功能测试通过')
    })
    
  })
  
  describe('导航和路由', () => {
    
    test('从首页正确导航到创作页面', async () => {
      console.log('🧪 测试：从首页导航到创作页面')
      
      // 先导航到首页
      await program.navigateTo('/pages/index/index')
      await CommonHelpers.wait(1000)
      
      // 查找并点击"开始创作"按钮（这里需要根据实际首页结构调整）
      // 由于我们没有首页的具体代码，这里模拟点击
      await program.navigateTo('/pages/create/index')
      await CommonHelpers.wait(2000)
      
      // 检查是否正确到达创作页面
      await CommonHelpers.checkCurrentUrl('/pages/create/index')
      await VideoEditingHelpers.checkPageTitle('视频智能剪辑')
      
      console.log('✅ 导航测试通过')
    })
    
    test('页面状态在导航后保持', async () => {
      console.log('🧪 测试：页面状态保持')
      
      // 上传一个视频素材
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      
      // 设置文案
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      
      // 检查状态
      await VideoEditingHelpers.checkUploadedMaterialCount(1)
      await VideoEditingHelpers.checkScriptCount(2)
      
      // 离开页面后返回
      await program.navigateTo('/pages/index/index')
      await CommonHelpers.wait(1000)
      await program.navigateTo('/pages/create/index')
      await CommonHelpers.wait(2000)
      
      // 检查状态是否保持（这取决于具体的状态管理实现）
      // 注意：实际情况可能需要根据应用的状态管理策略调整
      
      console.log('✅ 页面状态保持测试通过')
    })
    
  })
  
  describe('UI交互基础功能', () => {
    
    test('模式切换功能', async () => {
      console.log('🧪 测试：模式切换功能')
      
      // 测试配音模式切换
      await VideoEditingHelpers.selectVoiceMode('system')
      await CommonHelpers.wait(500)
      
      await VideoEditingHelpers.selectVoiceMode('record')
      await CommonHelpers.wait(500)
      
      await VideoEditingHelpers.selectVoiceMode('system')
      await CommonHelpers.wait(500)
      
      // 测试音乐模式切换
      await VideoEditingHelpers.selectMusicMode('system')
      await CommonHelpers.wait(500)
      
      await VideoEditingHelpers.selectMusicMode('custom')
      await CommonHelpers.wait(500)
      
      await VideoEditingHelpers.selectMusicMode('system')
      await CommonHelpers.wait(500)
      
      console.log('✅ 模式切换功能测试通过')
    })
    
    test('声音类型选择功能', async () => {
      console.log('🧪 测试：声音类型选择功能')
      
      // 确保在系统配音模式
      await VideoEditingHelpers.selectVoiceMode('system')
      
      // 测试选择不同的声音类型
      for (let i = 0; i < 4; i++) {
        await VideoEditingHelpers.selectVoiceType(i)
        await CommonHelpers.wait(1000) // 等待声音预览
        
        // 检查是否有激活的声音类型
        await VideoEditingHelpers.checkActiveVoiceType()
      }
      
      console.log('✅ 声音类型选择功能测试通过')
    })
    
    test('自定义音乐选择功能', async () => {
      console.log('🧪 测试：自定义音乐选择功能')
      
      // 切换到自定义音乐模式
      await VideoEditingHelpers.selectMusicMode('custom')
      await CommonHelpers.wait(500)
      
      // 检查音乐列表是否显示
      const musicItems = await page.$$(VideoEditingHelpers.selectors.musicItem)
      expect(musicItems.length).toBeGreaterThan(0)
      
      // 测试选择不同的音乐
      for (let i = 0; i < Math.min(3, musicItems.length); i++) {
        await VideoEditingHelpers.selectCustomMusic(i)
        await CommonHelpers.wait(500)
        
        // 测试播放/停止功能
        await VideoEditingHelpers.toggleMusicPlay(i)
        await CommonHelpers.wait(1000)
        
        // 停止播放
        await VideoEditingHelpers.toggleMusicPlay(i)
        await CommonHelpers.wait(500)
      }
      
      console.log('✅ 自定义音乐选择功能测试通过')
    })
    
  })
  
  describe('错误处理和边界条件', () => {
    
    test('生成条件验证 - 无视频素材', async () => {
      console.log('🧪 测试：生成条件验证 - 无视频素材')
      
      // 确保没有视频素材
      await VideoEditingHelpers.checkUploadedMaterialCount(0)
      
      // 设置文案
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      
      // 点击生成按钮
      await VideoEditingHelpers.clickGenerateButton()
      
      // 检查是否显示错误提示
      await VideoEditingHelpers.checkGenerationValidation('请先上传视频素材')
      
      console.log('✅ 无视频素材验证测试通过')
    })
    
    test('生成条件验证 - 无文案内容', async () => {
      console.log('🧪 测试：生成条件验证 - 无文案内容')
      
      // 上传视频素材
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      
      // 设置空文案
      await VideoEditingHelpers.setCustomScript(TestData.scripts.empty)
      
      // 点击生成按钮
      await VideoEditingHelpers.clickGenerateButton()
      
      // 检查是否显示错误提示
      await VideoEditingHelpers.checkGenerationValidation('请先设置文案内容')
      
      console.log('✅ 无文案内容验证测试通过')
    })
    
    test('生成条件验证 - 空文案内容', async () => {
      console.log('🧪 测试：生成条件验证 - 空文案内容')
      
      // 上传视频素材
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      
      // 设置空格文案
      await VideoEditingHelpers.setCustomScript(TestData.scripts.spaces)
      
      // 点击生成按钮
      await VideoEditingHelpers.clickGenerateButton()
      
      // 检查是否显示错误提示
      await VideoEditingHelpers.checkGenerationValidation('请输入有效的文案内容')
      
      console.log('✅ 空文案内容验证测试通过')
    })
    
    test('重复点击生成按钮', async () => {
      console.log('🧪 测试：重复点击生成按钮')
      
      // 设置完整的生成条件
      await VideoEditingHelpers.uploadVideoMaterial(TestData.videoMaterials.normal)
      await VideoEditingHelpers.setCustomScript(TestData.scripts.normal)
      
      // 连续点击生成按钮
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.clickGenerateButton()
      await VideoEditingHelpers.clickGenerateButton()
      
      // 等待处理完成
      await CommonHelpers.wait(3000)
      
      // 检查是否正确处理了重复点击
      // 这里需要根据具体的实现来调整断言
      
      console.log('✅ 重复点击生成按钮测试通过')
    })
    
  })
  
}) 