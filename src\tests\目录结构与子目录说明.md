# tests 目录及子目录结构说明

本文件详细说明 src/tests 目录及其下一级、二级子目录的组织和用途，便于团队成员理解和维护。

## 目录结构

```text
src/tests/
├── docs/        # 测试文档，如用例设计、测试规范等
│   └── ...      # 详细测试用例、设计文档等
├── e2e/         # 端对端（E2E）测试，模拟用户操作和完整业务流程
│   ├── index/       # 首页相关 E2E 测试
│   ├── history/     # 历史记录相关 E2E 测试
│   ├── create/      # 创作页相关 E2E 测试
│   └── ...          # 其它页面或模块 E2E 测试
├── helpers/     # 公共测试工具、辅助方法、mock 工具等
│   ├── video-editing-helpers.js   # 视频剪辑功能测试工具
│   ├── common-helpers.js          # 通用测试辅助工具
│   └── test-data.js               # 测试数据
├── report/      # 测试报告输出目录，自动化测试结果存放于此
│   └── wangcut-app/   # 按项目或平台分类的测试报告
├── scripts/     # 测试相关脚本，如自定义 runner、CI 集成脚本等
│   └── run-tests.js   # 测试运行脚本
├── unit/        # 单元测试，针对函数、组件、工具类等最小单元
│   ├── components/    # 组件相关单元测试
│   ├── utils/         # 工具函数相关单元测试
│   └── ...            # 其它模块单元测试
└── README.md    # 测试说明文档，介绍测试体系和用法
```

## 各目录及子目录说明

- **docs/**
  - 存放测试相关文档，如测试用例、测试计划、规范说明等。
  - 二级目录/文件：详细的用例设计、测试流程说明等。

- **e2e/**
  - 端对端测试目录，建议按页面或业务模块细分子目录。
  - **index/**：首页相关 E2E 测试用例。
  - **history/**：历史记录相关 E2E 测试用例。
  - **create/**：创作页相关 E2E 测试用例。
  - 其它页面或业务模块可新建对应子目录。

- **helpers/**
  - 公共测试工具、mock 数据、辅助函数等，供各类测试复用。
  - 主要为 JS 工具文件，也可按功能细分子目录。

- **report/**
  - 自动化测试报告输出目录，CI/CD 可自动收集。
  - **wangcut-app/**：按项目或平台分类的测试报告。

- **scripts/**
  - 测试相关脚本，如批量运行、定制报告、CI 集成等。
  - 主要为 JS 脚本文件。

- **unit/**
  - 单元测试目录，建议与 src 结构保持一致，测试最小功能单元。
  - **components/**：组件相关单元测试。
  - **utils/**：工具函数相关单元测试。
  - 其它模块可新建对应子目录。

- **README.md**
  - 测试体系说明、运行方法、最佳实践等。

## 测试方法

### 单元测试（Unit Test）

- **定义**：针对代码中最小的可测试单元（如函数、方法、类、组件）进行的自动化测试。
- **目的**：验证每个单元的功能是否符合预期，确保输入输出正确。
- **特点**：
  - 测试范围小、粒度细。
  - 通常不依赖外部系统（如数据库、网络），会用 mock/stub 隔离依赖。
  - 执行速度快，便于频繁运行。
- **示例**：测试一个加法函数 `add(a, b)` 是否能正确返回两数之和。

### 端对端测试（End-to-End Test, E2E）

- **定义**：模拟真实用户操作，自动化测试整个应用的业务流程，从前端界面到后端服务的完整链路。
- **目的**：验证系统各部分协同工作是否正常，确保关键业务流程无误。
- **特点**：
  - 测试范围大，覆盖整个系统。
  - 依赖真实环境（如数据库、接口、UI），不做 mock。
  - 执行速度较慢，但能发现集成、配置、环境等问题。
- **示例**：自动化浏览器打开首页，登录账号，上传视频，生成剪辑，验证最终页面和数据。

### 集成测试（Integration Test）

- **定义**：测试多个模块或组件之间的协作，验证它们能否正确集成。
- **目的**：确保模块间接口正确、数据传递无误、协作逻辑正常。
- **特点**：
  - 测试范围介于单元测试和 E2E 测试之间。
  - 可能 mock 部分外部依赖，但保留模块间真实交互。
  - 执行速度中等，能发现模块集成问题。

### 测试方法对比

| 对比项         | 单元测试（Unit Test）         | 端对端测试（E2E Test）         | 集成测试（Integration Test）   |
|----------------|------------------------------|-------------------------------|-------------------------------|
| 测试对象       | 单个函数/模块                | 整个系统/业务流程             | 多个模块/组件协作             |
| 依赖           | 隔离依赖（mock）              | 真实依赖                      | 部分真实依赖                  |
| 执行速度       | 快                           | 慢                            | 中等                          |
| 发现问题类型   | 代码逻辑错误                  | 集成、配置、流程、环境问题    | 模块间接口、协作问题          |

## HBuilderX 自动化测试

### 环境配置

- **HBuilderX 版本**：确保使用支持自动化测试的版本。
- **uni-app 项目**：项目必须基于 uni-app 框架。
- **依赖安装**：确保 `@dcloudio/uni-automator` 已正确安装。

### 运行方式

#### 1. HBuilderX 内置测试
- 在 HBuilderX 中右键测试文件，选择"运行测试"。
- 支持单文件测试、批量测试。
- 自动处理环境变量和依赖。

#### 2. 命令行测试
```bash
# 运行所有测试
npm run test:e2e:h5

# 运行特定平台测试
npm run test:e2e:android
npm run test:e2e:ios

# 运行单元测试
npm run test:unit
```

### 测试文件要点

#### 1. 文件命名规范
- 单元测试：`*.test.js` 或 `*.spec.js`
- 端对端测试：`*.e2e.js` 或 `*.test.js`
- 建议按功能模块命名，如 `video-editing-basic.test.js`

#### 2. 测试结构
```javascript
// 测试用例
test('测试用例描述', async () => {
  // 测试步骤
  await program.navigateTo('/pages/index/index');
  
  // 断言验证
  expect(result).toBe(expected);
});
```

#### 3. 常用 API
- `program.navigateTo()`：页面跳转
- `program.currentPage()`：获取当前页面
- `page.$()`：查找元素
- `element.tap()`：点击操作
- `element.text()`：获取文本内容

## Jest 配置文件要点

### 1. jest.config.js（主配置）

```javascript
module.exports = {
  globalTeardown: '@dcloudio/uni-automator/dist/teardown.js',
  testEnvironment: '@dcloudio/uni-automator/dist/environment.js',
  testEnvironmentOptions: {
    compile: true,
    h5: {
      options: {
        headless: false // 显示测试窗口
      }
    }
  },
  testTimeout: 10000,
  reporters: [
    'default',
    ['jest-html-reporter', {
      publicPath: './',
      filename: 'test-report.html'
    }]
  ],
  testMatch: [
    "<rootDir>/src/tests/**/*.test.js"
  ],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
};
```

### 2. jest.unit.config.js（单元测试配置）

```javascript
module.exports = {
  testMatch: [
    "<rootDir>/src/tests/unit/**/*.test.js",
    "<rootDir>/src/tests/unit/**/*.spec.js"
  ],
  testEnvironment: "jest-environment-jsdom",
  moduleFileExtensions: ['js', 'json', 'vue'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
};
```

### 3. jest.e2e.config.js（端对端测试配置）

```javascript
module.exports = {
  globalTeardown: '@dcloudio/uni-automator/dist/teardown.js',
  testEnvironment: '@dcloudio/uni-automator/dist/environment.js',
  testEnvironmentOptions: {
    compile: true,
    h5: {
      options: {
        headless: false
      }
    }
  },
  testTimeout: 10000,
  testMatch: [
    "<rootDir>/src/tests/e2e/**/*.test.js",
    "<rootDir>/src/tests/e2e/**/*.e2e.js"
  ],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
};
```

### 配置要点说明

#### 1. 环境变量设置
- **UNI_PLATFORM**：必须设置，如 `h5`、`app-plus`、`mp-weixin` 等。
- **UNI_OS_NAME**：App 平台需要，如 `android`、`ios`。

#### 2. 测试环境选择
- **单元测试**：使用 `jest-environment-jsdom` 或 `jest-environment-node`。
- **端对端测试**：使用 `@dcloudio/uni-automator/dist/environment.js`。

#### 3. 超时设置
- **testTimeout**：建议设置为 10000-30000ms，避免测试过早超时。

#### 4. 报告配置
- **reporters**：支持多种报告格式，如 HTML、JSON、JUnit 等。
- **输出路径**：建议统一配置报告输出目录。

#### 5. 路径映射
- **moduleNameMapper**：配置 `@/` 别名，便于导入模块。
- **testMatch**：指定测试文件匹配规则。

## 最佳实践

### 1. 测试编写
- 使用描述性的测试名称，清晰表达测试意图。
- 每个测试保持独立和原子性，避免测试间相互依赖。
- 合理使用 beforeEach 和 afterEach 清理状态。
- 添加必要的等待时间避免竞态条件。

### 2. 测试数据
- 使用 helpers/test-data.js 中定义的标准测试数据。
- 避免硬编码测试数据，提高可维护性。
- 为边界值测试创建专门的数据。

### 3. 错误处理
- 在测试失败时截图，便于问题定位。
- 记录详细的错误信息，包括上下文数据。
- 使用重试机制处理不稳定的测试。

### 4. 性能考虑
- 避免不必要的等待时间，提高测试效率。
- 合理使用并行测试，但注意资源竞争。
- 及时清理测试生成的文件，避免磁盘空间浪费。

### 5. 配置管理
- 分离单元测试和端对端测试配置。
- 使用环境变量区分不同平台。
- 统一报告输出格式和路径。

> 建议团队成员在添加新测试或工具时，遵循本结构规范，便于协作和维护。 