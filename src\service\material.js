/**
 * 素材相关接口服务
 */

import http from '@/utils/http'

const materialService = {
  // 获取素材列表
  getMaterialList(params) {
    return http.get('/api/material/list', params)
  },
  
  // 获取素材详情
  getMaterialDetail(id) {
    return http.get(`/api/material/detail/${id}`)
  },
  
  // 获取素材库视频列表
  getMaterialLibrary(params = {}) {
    return http.get('/api/v1/materials/', params)
  },
  
  // 上传素材
  uploadMaterial(filePath, formData = {}) {
    return http.upload('/api/material/upload', filePath, formData)
  },
  
  // 删除素材
  deleteMaterial(id) {
    return http.delete(`/api/material/${id}`)
  },
  
  // 批量删除素材
  batchDeleteMaterials(ids) {
    return http.post('/api/material/batch-delete', { ids })
  },
  
  // 获取素材分类
  getMaterialCategories() {
    return http.get('/api/material/categories')
  },
  
  // 按分类获取素材
  getMaterialsByCategory(categoryId, params) {
    return http.get(`/api/material/category/${categoryId}`, params)
  },
  
  // 搜索素材
  searchMaterials(params) {
    return http.get('/api/material/search', params)
  },
  
  // 获取用户素材库
  getUserMaterials(params) {
    return http.get('/api/material/user', params)
  },
  
  // 创建素材文件夹
  createMaterialFolder(data) {
    return http.post('/api/material/folder', data)
  },
  
  // 更新素材文件夹
  updateMaterialFolder(id, data) {
    return http.put(`/api/material/folder/${id}`, data)
  },
  
  // 删除素材文件夹
  deleteMaterialFolder(id) {
    return http.delete(`/api/material/folder/${id}`)
  },
  
  // 移动素材到文件夹
  moveMaterialToFolder(materialId, folderId) {
    return http.post(`/api/material/${materialId}/move`, { folderId })
  },
  
  // 获取音频素材
  getAudioMaterials(params) {
    return http.get('/api/material/audio', params)
  },
  
  // 获取图片素材
  getImageMaterials(params) {
    return http.get('/api/material/image', params)
  },
  
  // 获取视频素材
  getVideoMaterials(params) {
    return http.get('/api/material/video', params)
  },
  
  // 获取字体素材
  getFontMaterials(params) {
    return http.get('/api/material/font', params)
  },
  
  // 获取贴纸素材
  getStickerMaterials(params) {
    return http.get('/api/material/sticker', params)
  },
  
  // 获取转场效果素材
  getTransitionMaterials(params) {
    return http.get('/api/material/transition', params)
  },
  
  // 获取滤镜素材
  getFilterMaterials(params) {
    return http.get('/api/material/filter', params)
  },
  
  // 获取特效素材
  getEffectMaterials(params) {
    return http.get('/api/material/effect', params)
  },
  
  // 收藏素材
  favoriteMaterial(id) {
    return http.post(`/api/material/${id}/favorite`)
  },
  
  // 取消收藏素材
  unfavoriteMaterial(id) {
    return http.post(`/api/material/${id}/unfavorite`)
  },
  
  // 获取收藏的素材
  getFavoriteMaterials(params) {
    return http.get('/api/material/favorites', params)
  },
  
  // 下载素材
  downloadMaterial(id) {
    return http.download(`/api/material/${id}/download`)
  },
  
  // 批量下载素材
  batchDownloadMaterials(ids) {
    return http.post('/api/material/batch-download', { ids })
  },
  
  // 获取素材使用统计
  getMaterialStats(id) {
    return http.get(`/api/material/${id}/stats`)
  },
  
  // 记录素材使用
  recordMaterialUse(id, data) {
    return http.post(`/api/material/${id}/use`, data)
  },
  
  // 获取热门素材
  getPopularMaterials(params) {
    return http.get('/api/material/popular', params)
  },
  
  // 获取最新素材
  getLatestMaterials(params) {
    return http.get('/api/material/latest', params)
  },
  
  // 获取推荐素材
  getRecommendedMaterials(params) {
    return http.get('/api/material/recommended', params)
  },
  
  // 举报素材
  reportMaterial(id, data) {
    return http.post(`/api/material/${id}/report`, data)
  },
  
  // 获取素材评论
  getMaterialComments(id, params) {
    return http.get(`/api/material/${id}/comments`, params)
  },
  
  // 添加素材评论
  addMaterialComment(id, data) {
    return http.post(`/api/material/${id}/comments`, data)
  },
  
  // 删除素材评论
  deleteMaterialComment(materialId, commentId) {
    return http.delete(`/api/material/${materialId}/comments/${commentId}`)
  },
  
  // 素材标签管理
  getMaterialTags() {
    return http.get('/api/material/tags')
  },
  
  // 按标签获取素材
  getMaterialsByTag(tagId, params) {
    return http.get(`/api/material/tag/${tagId}`, params)
  },
  
  // 为素材添加标签
  addMaterialTag(materialId, tagId) {
    return http.post(`/api/material/${materialId}/tags`, { tagId })
  },
  
  // 移除素材标签
  removeMaterialTag(materialId, tagId) {
    return http.delete(`/api/material/${materialId}/tags/${tagId}`)
  },
  
  // 获取素材版权信息
  getMaterialCopyright(id) {
    return http.get(`/api/material/${id}/copyright`)
  },
  
  // 检查素材版权
  checkMaterialCopyright(id) {
    return http.post(`/api/material/${id}/copyright-check`)
  },
  
  // 获取素材许可信息
  getMaterialLicense(id) {
    return http.get(`/api/material/${id}/license`)
  },
  
  // 素材格式转换
  convertMaterialFormat(id, data) {
    return http.post(`/api/material/${id}/convert`, data)
  },
  
  // 获取转换任务状态
  getConvertStatus(taskId) {
    return http.get(`/api/material/convert/${taskId}`)
  },
  
  // 素材预览
  previewMaterial(id) {
    return http.get(`/api/material/${id}/preview`)
  },
  
  // 获取素材缩略图
  getMaterialThumbnail(id, size = 'medium') {
    return http.get(`/api/material/${id}/thumbnail`, { size })
  },

  // 获取字体列表
  getFontList() {
    return http.get('/api/v1/system-config/fonts')
  },

  // 获取语音角色列表
  getVoiceRoleList() {
    return http.get('/api/v1/system-config/voice-roles')
  },

  /**
   * 创建视频编辑任务
   * @param {Object} data - 任务参数
   * @returns {Promise}
   */
  createVideoEditingTask(data) {
    return http.post('/api/v1/video-editing/tasks', data)
  },

  /**
   * 查询视频编辑任务进度
   * @param {number|string} id - 任务ID
   * @returns {Promise}
   */
  getVideoEditingTaskProgress(id) {
    return http.get(`/api/v1/video-editing/tasks/${id}/progress`)
  },

  /**
   * 获取系统音乐列表
   * @returns {Promise}
   */
  getMusicList() {
    return http.get('/api/v1/system-config/music')
  },

  /**
   * 生成文案/仿写/改写
   * @param {Object} params
   * @param {number} params.function_type 功能类型：1-文案生成，2-仿写，3-改写
   * @param {string} params.prompt_type 文案类型
   * @param {string} params.custom_requirements 自定义要求
   * @param {string} params.origin_text 原文案（仿写/改写时传）
   * @param {string} params.compute_account_type 算力账户类型（1:个人2:团队）
   * @returns {Promise}
   */
  generateDoc(params) {
    return http.post('/api/v1/ai/generate', params)
  }
}

export default materialService 