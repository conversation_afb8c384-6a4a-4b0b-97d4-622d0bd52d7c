<template>
  <view class="demo-dialog">
    <button @click="showDialog = true">打开弹窗</button>
    <Dialog
      :visible="showDialog"
      title="增加文案"
      content="在深圳龙华，你只要月薪超过1万，你一定要来这家服装店。但来这里，你根本不用担心价格，件件都是极致性价比，更不用担心质量，每一件都是老板娘精挑细选。今天，只要你在这条视频的结尾评论美丽二字到店，老板娘都给你准备惊喜一份。"
      :features="features"
      :extraFeatures="extraFeatures"
      :actions="actions"
      @close="showDialog = false"
      @confirm="onConfirm"
      @cancel="onCancel"
      @featureClick="onFeatureClick"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Dialog from '@/components/business/Dialog/index.vue';

const showDialog = ref(false);
const features = [
  { icon: '/src/asset/dialog/dialog-ai.png', text: 'AI生成' }
];
const extraFeatures = [
  { icon: '/src/asset/dialog/dialog-douyin-link.png', text: '抖音链接提取' },
  { icon: '/src/asset/dialog/dialog-ai-rewrite.png', text: 'AI改写' }
];
const actions = [
  { text: '取消', type: 'default' },
  { text: '确定', type: 'primary' }
];
const onConfirm = () => {
  uni.showToast({ title: '点击确定', icon: 'success' });
  showDialog.value = false;
};
const onCancel = () => {
  uni.showToast({ title: '点击取消', icon: 'none' });
  showDialog.value = false;
};
const onFeatureClick = (feature: any) => {
  uni.showToast({ title: `点击${feature.text}`, icon: 'none' });
};
</script>

<style lang="scss" scoped>
.demo-dialog {
  padding: 40rpx;
  min-height: 100vh;
  background: #18181a;
  button {
    margin-bottom: 40rpx;
    background: #ff0043;
    color: #fff;
    border-radius: 16rpx;
    font-size: 32rpx;
    padding: 20rpx 60rpx;
  }
}
</style>
