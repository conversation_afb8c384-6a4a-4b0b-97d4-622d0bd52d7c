# PrimaryButton 主要按钮组件

## 组件描述

PrimaryButton 是一个功能丰富的主要按钮组件，支持多种类型、尺寸、状态和自定义样式。适用于各种交互场景，提供统一的按钮体验。

## 功能特性

- 🎯 **多种类型** - 支持 primary、secondary、success、warning、danger、info 等类型
- 🎨 **尺寸可选** - 支持 small、medium、large 三种尺寸
- 📱 **状态管理** - 支持禁用状态和加载状态
- 🔄 **自定义样式** - 支持背景色、文字色、边框色自定义
- 🎭 **图标支持** - 支持图标和文字组合显示

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| text | String | '' | 按钮文字 |
| icon | String | '' | 按钮图标（emoji） |
| type | String | 'primary' | 按钮类型：primary/secondary/success/warning/danger/info |
| size | String | 'medium' | 按钮尺寸：small/medium/large |
| disabled | Boolean | false | 是否禁用 |
| loading | Boolean | false | 是否显示加载状态 |
| backgroundColor | String | '' | 自定义背景色 |
| textColor | String | '' | 自定义文字颜色 |
| borderColor | String | '' | 自定义边框颜色 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | - | 按钮点击时触发 |

## Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| default | 按钮内容插槽 |

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <PrimaryButton 
      text="确认"
      @click="handleConfirm"
    />
  </view>
</template>

<script setup>
import PrimaryButton from '@/components/common/PrimaryButton/index.vue'

const handleConfirm = () => {
  console.log('按钮被点击')
}
</script>
```

### 不同类型按钮

```vue
<template>
  <view class="button-demo">
    <!-- 主要按钮 -->
    <PrimaryButton 
      text="主要按钮"
      type="primary"
      @click="handlePrimary"
    />
    
    <!-- 次要按钮 -->
    <PrimaryButton 
      text="次要按钮"
      type="secondary"
      @click="handleSecondary"
    />
    
    <!-- 成功按钮 -->
    <PrimaryButton 
      text="成功按钮"
      type="success"
      @click="handleSuccess"
    />
    
    <!-- 警告按钮 -->
    <PrimaryButton 
      text="警告按钮"
      type="warning"
      @click="handleWarning"
    />
    
    <!-- 危险按钮 -->
    <PrimaryButton 
      text="危险按钮"
      type="danger"
      @click="handleDanger"
    />
    
    <!-- 信息按钮 -->
    <PrimaryButton 
      text="信息按钮"
      type="info"
      @click="handleInfo"
    />
  </view>
</template>

<script setup>
import PrimaryButton from '@/components/common/PrimaryButton/index.vue'

const handlePrimary = () => console.log('主要按钮')
const handleSecondary = () => console.log('次要按钮')
const handleSuccess = () => console.log('成功按钮')
const handleWarning = () => console.log('警告按钮')
const handleDanger = () => console.log('危险按钮')
const handleInfo = () => console.log('信息按钮')
</script>

<style lang="scss" scoped>
.button-demo {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 40rpx;
}
</style>
```

### 不同尺寸按钮

```vue
<template>
  <view class="size-demo">
    <PrimaryButton 
      text="小按钮"
      size="small"
      @click="handleClick"
    />
    
    <PrimaryButton 
      text="中等按钮"
      size="medium"
      @click="handleClick"
    />
    
    <PrimaryButton 
      text="大按钮"
      size="large"
      @click="handleClick"
    />
  </view>
</template>

<script setup>
import PrimaryButton from '@/components/common/PrimaryButton/index.vue'

const handleClick = () => console.log('按钮点击')
</script>

<style lang="scss" scoped>
.size-demo {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 40rpx;
}
</style>
```

### 带图标按钮

```vue
<template>
  <view class="icon-demo">
    <PrimaryButton 
      text="保存"
      icon="💾"
      @click="handleSave"
    />
    
    <PrimaryButton 
      text="删除"
      icon="🗑️"
      type="danger"
      @click="handleDelete"
    />
    
    <PrimaryButton 
      text="分享"
      icon="📤"
      type="info"
      @click="handleShare"
    />
  </view>
</template>

<script setup>
import PrimaryButton from '@/components/common/PrimaryButton/index.vue'

const handleSave = () => console.log('保存')
const handleDelete = () => console.log('删除')
const handleShare = () => console.log('分享')
</script>

<style lang="scss" scoped>
.icon-demo {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 40rpx;
}
</style>
```

### 状态按钮

```vue
<template>
  <view class="state-demo">
    <!-- 正常状态 -->
    <PrimaryButton 
      text="正常按钮"
      @click="handleClick"
    />
    
    <!-- 禁用状态 -->
    <PrimaryButton 
      text="禁用按钮"
      :disabled="true"
      @click="handleClick"
    />
    
    <!-- 加载状态 -->
    <PrimaryButton 
      text="加载中"
      :loading="true"
      @click="handleClick"
    />
  </view>
</template>

<script setup>
import PrimaryButton from '@/components/common/PrimaryButton/index.vue'

const handleClick = () => console.log('按钮点击')
</script>

<style lang="scss" scoped>
.state-demo {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 40rpx;
}
</style>
```

### 自定义样式

```vue
<template>
  <view class="custom-demo">
    <PrimaryButton 
      text="自定义按钮"
      background-color="#ff6b6b"
      text-color="#ffffff"
      border-color="#ff6b6b"
      @click="handleClick"
    />
    
    <PrimaryButton 
      text="渐变按钮"
      background-color="linear-gradient(45deg, #667eea 0%, #764ba2 100%)"
      text-color="#ffffff"
      @click="handleClick"
    />
  </view>
</template>

<script setup>
import PrimaryButton from '@/components/common/PrimaryButton/index.vue'

const handleClick = () => console.log('自定义按钮点击')
</script>

<style lang="scss" scoped>
.custom-demo {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 40rpx;
}
</style>
```

### 使用插槽

```vue
<template>
  <view>
    <PrimaryButton @click="handleClick">
      <text>插槽内容</text>
      <text style="margin-left: 10rpx;">🎉</text>
    </PrimaryButton>
  </view>
</template>

<script setup>
import PrimaryButton from '@/components/common/PrimaryButton/index.vue'

const handleClick = () => console.log('插槽按钮点击')
</script>
```

## 样式变体

### 类型样式

- **primary**: 主要按钮，蓝色背景
- **secondary**: 次要按钮，透明背景，蓝色边框
- **success**: 成功按钮，绿色背景
- **warning**: 警告按钮，橙色背景
- **danger**: 危险按钮，红色背景
- **info**: 信息按钮，灰色背景

### 尺寸规格

- **small**: 小尺寸，适合紧凑布局
- **medium**: 中等尺寸，默认规格
- **large**: 大尺寸，适合重要操作

## 样式定制

组件使用 SCSS 编写，主要样式特点：

- 圆角：`$radius-base`
- 过渡动画：`$transition-fast`
- 交互效果：点击时向下移动 1rpx
- 加载动画：旋转动画

## 注意事项

1. 禁用状态和加载状态下不会触发点击事件
2. 加载状态下会显示加载图标，隐藏按钮图标
3. 自定义样式会覆盖默认的主题样式
4. 图标建议使用 emoji，确保跨平台兼容性
5. 按钮文字和图标会自动居中对齐

## 最佳实践

1. **类型选择**：根据操作性质选择合适的按钮类型
2. **尺寸选择**：根据布局需要选择合适的按钮尺寸
3. **状态管理**：合理使用禁用和加载状态
4. **图标使用**：使用统一的图标风格
5. **文案简洁**：按钮文字保持简洁明了

## 与其他组件配合

PrimaryButton 通常与以下组件配合使用：

- **Dialog**: 对话框确认按钮
- **Form**: 表单提交按钮
- **Modal**: 模态框操作按钮

提供完整的用户交互体验。 