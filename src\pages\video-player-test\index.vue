<template>
  <view class="video-test-page">
    <!-- 状态栏占位 -->
    <view class="video-test-page__status-bar"></view>
    
    <!-- 页面内容 -->
    <view class="video-test-page__content">
      <view class="video-test-page__header">
        <text class="video-test-page__title">视频播放器测试中心</text>
        <text class="video-test-page__subtitle">选择测试模式</text>
      </view>
      
      <!-- 测试模式选择 -->
      <view class="video-test-page__test-modes">
        <view class="video-test-page__mode-card" @click="goToBasicTest">
          <view class="video-test-page__mode-icon">🎬</view>
          <text class="video-test-page__mode-title">基础功能测试</text>
          <text class="video-test-page__mode-desc">测试播放、暂停、进度等基本功能</text>
        </view>
        
        <view class="video-test-page__mode-card" @click="goToDesignTest">
          <view class="video-test-page__mode-icon">🎨</view>
          <text class="video-test-page__mode-title">设计稿对照测试</text>
          <text class="video-test-page__mode-desc">像素级复刻验证，对照设计稿</text>
        </view>
        
        <view class="video-test-page__mode-card" @click="goToVerification">
          <view class="video-test-page__mode-icon">🔍</view>
          <text class="video-test-page__mode-title">元素验证测试</text>
          <text class="video-test-page__mode-desc">逐个验证界面元素和交互</text>
        </view>
      </view>
      
      <!-- 快速测试 -->
      <view class="video-test-page__quick-test">
        <text class="video-test-page__section-title">快速测试</text>
        <view class="video-test-page__quick-buttons">
          <view class="video-test-page__quick-btn" @click="showVideoPlayer">
            <text class="video-test-page__quick-btn-text">直接测试播放器</text>
          </view>
          <view class="video-test-page__quick-btn" @click="testTabVisibility">
            <text class="video-test-page__quick-btn-text">测试Tab隐藏功能</text>
          </view>
          <view class="video-test-page__quick-btn" @click="testControlsAndBack">
            <text class="video-test-page__quick-btn-text">测试控制栏和返回键</text>
          </view>
          <view class="video-test-page__quick-btn" @click="testAutoPlay">
            <text class="video-test-page__quick-btn-text">测试自动播放</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 视频播放器 -->
    <VideoPlayer
      v-if="isVideoPlayerVisible"
      :video-src="videoSrc"
      :poster="posterSrc"
      :auto-play="true"
      :has-modal="showDownloadProgress"
      @close="hideVideoPlayer"
      @play="onPlay"
      @pause="onPause"
      @ended="onEnded"
      @timeupdate="onTimeUpdate"
      @fullscreen="onFullscreen"
      @export="onExport"
    />

    <!-- 下载进度组件 -->
    <DownloadProgress
      :visible="showDownloadProgress"
      :progress="downloadProgress"
      :file-name="downloadFileName"
      :status="downloadStatus"
      @cancel="handleCancelDownload"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import VideoPlayer from '@/components/business/VideoPlayer/index.vue'
import DownloadProgress from '@/components/business/DownloadProgress/index.vue'
import { useAppStore } from '@/store/modules/app.js'

// 响应式数据
const isVideoPlayerVisible = ref(false)
const videoSrc = ref('https://www.w3schools.com/html/mov_bbb.mp4')
const posterSrc = ref('https://www.w3schools.com/html/pic_mountain.jpg')

// 下载进度相关变量
const showDownloadProgress = ref(false)
const downloadProgress = ref(0)
const downloadFileName = ref('')
const downloadStatus = ref('downloading')
const downloadTimer = ref(null)

// 方法
const showVideoPlayer = () => {
  isVideoPlayerVisible.value = true
}

const hideVideoPlayer = () => {
  console.log('VideoPlayer测试页面: 收到关闭事件，隐藏视频播放器')
  isVideoPlayerVisible.value = false
  
  // 确保tab显示
  const appStore = useAppStore()
  appStore.setTabBarVisible(true)
  
  console.log('VideoPlayer测试页面: 视频播放器已隐藏')
}

const onPlay = () => {
  console.log('视频开始播放')
}

const onPause = () => {
  console.log('视频暂停')
}

const onEnded = () => {
  console.log('视频播放结束')
}

const onTimeUpdate = ({ current, total }) => {
  console.log(`播放进度: ${current}/${total}`)
}

const onFullscreen = (isFullscreen) => {
  console.log(`全屏状态: ${isFullscreen}`)
}

const onExport = () => {
  // 触发下载进度弹窗测试
  startDownload('测试视频.mp4')
}

// 下载进度相关方法
const startDownload = (fileName) => {
  downloadFileName.value = fileName
  downloadStatus.value = 'downloading'
  downloadProgress.value = 0
  showDownloadProgress.value = true

  downloadTimer.value = setInterval(() => {
    if (downloadProgress.value < 100) {
      downloadProgress.value += 10
    } else {
      clearInterval(downloadTimer.value)
      downloadStatus.value = 'completed'
      uni.showToast({
        title: '下载完成',
        icon: 'success'
      })
      setTimeout(() => {
        showDownloadProgress.value = false
      }, 1000)
    }
  }, 500)
}

const handleCancelDownload = () => {
  clearInterval(downloadTimer.value)
  downloadStatus.value = 'cancelled'
  uni.showToast({
    title: '下载已取消',
    icon: 'none'
  })
  setTimeout(() => {
    showDownloadProgress.value = false
  }, 1000)
}

// 导航方法
const goToBasicTest = () => {
  // 可以跳转到基础测试页面
  showVideoPlayer()
}

const goToDesignTest = () => {
  uni.navigateTo({
    url: '/pages/video-player-test/design-test'
  })
}

const goToVerification = () => {
  // 可以跳转到验证测试页面
  showVideoPlayer()
}

const testTabVisibility = () => {
  // 测试tab隐藏功能
  const appStore = useAppStore()
  
  // 隐藏tab
  appStore.setTabBarVisible(false)
  
  uni.showToast({
    title: 'Tab已隐藏，3秒后恢复',
    icon: 'none',
    duration: 3000
  })
  
  // 3秒后恢复tab
  setTimeout(() => {
    appStore.setTabBarVisible(true)
    uni.showToast({
      title: 'Tab已恢复显示',
      icon: 'success'
    })
  }, 3000)
}

const testControlsAndBack = () => {
  // 测试控制栏显示和物理返回键
  showVideoPlayer()
  
  uni.showToast({
    title: '请测试：1.控制栏永远显示 2.物理返回键关闭 3.检查控制台日志',
    icon: 'none',
    duration: 5000
  })
  
  // 添加调试信息
  console.log('VideoPlayer测试: 开始测试控制栏和返回键功能')
}

const testAutoPlay = () => {
  // 测试自动播放功能
  showVideoPlayer()
  
  uni.showToast({
    title: '请测试：1.视频自动播放 2.控制栏显示 3.检查控制台日志',
    icon: 'none',
    duration: 5000
  })
  
  // 添加调试信息
  console.log('VideoPlayer测试: 开始测试自动播放功能')
}
</script>

<style lang="scss" scoped>
.video-test-page {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  &__status-bar {
    height: var(--status-bar-height);
    width: 100%;
  }

  &__content {
    flex: 1;
    padding: 24rpx;
  }

  &__header {
    margin-bottom: 48rpx;
  }

  &__title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 8rpx;
  }

  &__subtitle {
    font-size: 24rpx;
    color: #666;
  }

  &__test-modes {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    margin-bottom: 48rpx;
  }

  &__mode-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    display: flex;
    align-items: center;
    gap: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  &__mode-icon {
    font-size: 48rpx;
    width: 80rpx;
    height: 80rpx;
    background: #f0f0f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__mode-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 8rpx;
    display: block;
  }

  &__mode-desc {
    font-size: 24rpx;
    color: #666;
    display: block;
  }

  &__quick-test {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
  }

  &__section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 24rpx;
  }

  &__quick-buttons {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }

  &__quick-btn {
    background: #007aff;
    border-radius: 12rpx;
    padding: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__quick-btn-text {
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
  }


}
</style>