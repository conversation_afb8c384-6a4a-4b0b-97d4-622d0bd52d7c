<template>
  <view class="video-player-example">
    <view class="video-player-example__header">
      <text class="video-player-example__title">视频播放器使用示例</text>
    </view>
    
    <view class="video-player-example__content">
      <view class="video-player-example__card">
        <image class="video-player-example__thumbnail" :src="videoPoster" mode="aspectFill" />
        <view class="video-player-example__info">
          <text class="video-player-example__title">示例视频</text>
          <text class="video-player-example__desc">点击播放按钮观看视频</text>
        </view>
        <view class="video-player-example__play-btn" @click="showVideoPlayer">
          <text class="video-player-example__play-icon">▶</text>
        </view>
      </view>
    </view>
    
    <!-- 视频播放器 -->
    <VideoPlayer
      v-if="isVideoPlayerVisible"
      :video-src="videoSrc"
      :poster="videoPoster"
      :auto-play="true"
      @close="hideVideoPlayer"
      @play="onPlay"
      @pause="onPause"
      @ended="onEnded"
      @export="onExport"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import VideoPlayer from './index.vue'

// 响应式数据
const isVideoPlayerVisible = ref(false)
const videoSrc = ref('https://www.w3schools.com/html/mov_bbb.mp4')
const videoPoster = ref('https://www.w3schools.com/html/pic_mountain.jpg')

// 方法
const showVideoPlayer = () => {
  isVideoPlayerVisible.value = true
}

const hideVideoPlayer = () => {
  isVideoPlayerVisible.value = false
}

const onPlay = () => {
  console.log('视频开始播放')
}

const onPause = () => {
  console.log('视频暂停')
}

const onEnded = () => {
  console.log('视频播放结束')
}

const onExport = () => {
  uni.showToast({
    title: '导出功能开发中',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.video-player-example {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 24rpx;

  &__header {
    margin-bottom: 48rpx;
  }

  &__title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }

  &__card {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    display: flex;
    align-items: center;
    gap: 24rpx;
  }

  &__thumbnail {
    width: 120rpx;
    height: 80rpx;
    border-radius: 8rpx;
    background: #f0f0f0;
  }

  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
  }

  &__title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
  }

  &__desc {
    font-size: 24rpx;
    color: #666;
  }

  &__play-btn {
    width: 80rpx;
    height: 80rpx;
    background: #007aff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__play-icon {
    color: #fff;
    font-size: 32rpx;
    margin-left: 4rpx;
  }
}
</style>