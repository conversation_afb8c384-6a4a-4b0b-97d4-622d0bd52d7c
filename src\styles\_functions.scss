// Sass 向后兼容函数
// 提供旧版本Sass函数的兼容性支持

@use "sass:color";
@use "sass:math";

// 颜色函数兼容性
@function darken($color, $amount) {
  @return color.adjust($color, $lightness: -$amount);
}

@function lighten($color, $amount) {
  @return color.adjust($color, $lightness: $amount);
}

@function saturate($color, $amount) {
  @return color.adjust($color, $saturation: $amount);
}

@function desaturate($color, $amount) {
  @return color.adjust($color, $saturation: -$amount);
}

@function adjust-hue($color, $amount) {
  @return color.adjust($color, $hue: $amount);
}

@function fade-in($color, $amount) {
  @return color.adjust($color, $alpha: $amount);
}

@function fade-out($color, $amount) {
  @return color.adjust($color, $alpha: -$amount);
}

@function opacify($color, $amount) {
  @return color.adjust($color, $alpha: $amount);
}

@function transparentize($color, $amount) {
  @return color.adjust($color, $alpha: -$amount);
}

@function mix($color1, $color2, $weight: 50%) {
  @return color.mix($color1, $color2, $weight);
}

@function complement($color) {
  @return color.adjust($color, $hue: 180deg);
}

@function invert($color, $weight: 100%) {
  @return color.invert($color, $weight);
}

@function grayscale($color) {
  @return color.grayscale($color);
}

// 数学函数兼容性
@function percentage($number) {
  @return math.percentage($number);
}

@function round($number) {
  @return math.round($number);
}

@function ceil($number) {
  @return math.ceil($number);
}

@function floor($number) {
  @return math.floor($number);
}

@function abs($number) {
  @return math.abs($number);
}

@function min($numbers...) {
  @return math.min($numbers...);
}

@function max($numbers...) {
  @return math.max($numbers...);
}

@function random($limit: null) {
  @return math.random($limit);
}

// 字符串函数兼容性
@function str-length($string) {
  @return string.length($string);
}

@function str-insert($string, $insert, $index) {
  @return string.insert($string, $insert, $index);
}

@function str-index($string, $substring) {
  @return string.index($string, $substring);
}

@function str-slice($string, $start-at, $end-at: -1) {
  @return string.slice($string, $start-at, $end-at);
}

@function to-upper-case($string) {
  @return string.to-upper-case($string);
}

@function to-lower-case($string) {
  @return string.to-lower-case($string);
}

// 列表函数兼容性
@function length($list) {
  @return list.length($list);
}

@function nth($list, $n) {
  @return list.nth($list, $n);
}

@function set-nth($list, $n, $value) {
  @return list.set-nth($list, $n, $value);
}

@function join($list1, $list2, $separator: auto) {
  @return list.join($list1, $list2, $separator);
}

@function append($list, $val, $separator: auto) {
  @return list.append($list, $val, $separator);
}

@function zip($lists...) {
  @return list.zip($lists...);
}

@function index($list, $value) {
  @return list.index($list, $value);
}

@function list-separator($list) {
  @return list.separator($list);
}

@function is-bracketed($list) {
  @return list.is-bracketed($list);
}

// Map函数兼容性
@function map-get($map, $key) {
  @return map.get($map, $key);
}

@function map-merge($map1, $map2) {
  @return map.merge($map1, $map2);
}

@function map-remove($map, $keys...) {
  @return map.remove($map, $keys...);
}

@function map-keys($map) {
  @return map.keys($map);
}

@function map-values($map) {
  @return map.values($map);
}

@function map-has-key($map, $key) {
  @return map.has-key($map, $key);
}

@function map-deep-get($map, $keys...) {
  @return map.deep-get($map, $keys...);
}

@function map-deep-merge($map1, $map2) {
  @return map.deep-merge($map1, $map2);
}

// 选择器函数兼容性
@function selector-append($selectors...) {
  @return selector.append($selectors...);
}

@function selector-extend($selector, $extendee, $extender) {
  @return selector.extend($selector, $extendee, $extender);
}

@function selector-replace($selector, $original, $replacement) {
  @return selector.replace($selector, $original, $replacement);
}

@function selector-unify($selector1, $selector2) {
  @return selector.unify($selector1, $selector2);
}

@function simple-selectors($selector) {
  @return selector.simple-selectors($selector);
}

@function selector-parse($selector) {
  @return selector.parse($selector);
}

@function selector-nest($selectors...) {
  @return selector.nest($selectors...);
}