/**
 * 原生素材库工具类
 * 提供获取和清除选中素材的方法
 */

/**
 * 获取选中的原生素材
 * @returns {Array} 选中的素材数组
 */
export function getSelectedNativeMaterials() {
  try {
    const materials = uni.getStorageSync('selectedNativeMaterials');
    return materials || [];
  } catch (error) {
    console.error('获取选中的原生素材失败:', error);
    return [];
  }
}

/**
 * 清除选中的原生素材
 */
export function clearSelectedNativeMaterials() {
  try {
    uni.removeStorageSync('selectedNativeMaterials');
    console.log('已清除选中的原生素材');
  } catch (error) {
    console.error('清除选中的原生素材失败:', error);
  }
}

/**
 * 格式化时长显示
 * @param {number} seconds - 时长（毫秒）
 * @returns {string} 格式化后的时长字符串
 */
export function formatDuration(seconds) {
  if (!seconds || seconds === 0) return '';
  
  const totalSeconds = Math.floor(seconds / 1000); // 转换为秒
  const minutes = Math.floor(totalSeconds / 60);
  const remainingSeconds = totalSeconds % 60;
  
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * 从路径中提取文件名
 * @param {string} path - 文件路径
 * @returns {string} 文件名
 */
export function getFileNameFromPath(path) {
  if (!path) return '未命名文件';
  const parts = path.split('/');
  return parts[parts.length - 1] || '未命名文件';
}

/**
 * 转换原生数据格式为应用数据格式
 * @param {Array} nativeSelectedItems - 原生选中的媒体项数组
 * @returns {Array} 转换后的数据数组
 */
export function convertNativeData(nativeSelectedItems) {
  return nativeSelectedItems.map(item => {
    return {
      id: item.id,
      path: item.path,
      mediaType: item.mediaType, // 1: 图片, 3: 视频
      duration: item.duration,
      durationStr: item.durationStr || formatDuration(item.duration),
      isSelected: item.isSelected,
      selectedOrder: item.selectedOrder,
      name: getFileNameFromPath(item.path),
      size: item.size || 0
    };
  });
}