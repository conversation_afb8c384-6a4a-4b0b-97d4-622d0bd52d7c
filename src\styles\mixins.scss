// 混入文件
@use './variables.scss' as *;

// 文本省略混入
@mixin text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

@mixin text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 居中混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 响应式断点混入
@mixin mobile {
  @media (max-width: $breakpoint-sm - 1px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: $breakpoint-sm) and (max-width: $breakpoint-lg - 1px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

// 按钮样式混入
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-lg;
  border: none;
  border-radius: $radius-base;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition-fast;
  user-select: none;
  
  &:active {
    transform: translateY(1rpx);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:active {
      transform: none;
    }
  }
}

@mixin button-primary {
  @include button-base;
  background: $gradient-primary;
  color: $text-inverse;
  box-shadow: $shadow-base;
  
  &:hover {
    box-shadow: $shadow-lg;
  }
}

@mixin button-secondary {
  @include button-base;
  background: $gradient-secondary;
  color: $text-inverse;
  box-shadow: $shadow-base;
  
  &:hover {
    box-shadow: $shadow-lg;
  }
}

@mixin button-outline {
  @include button-base;
  background: transparent;
  color: $primary-color;
  border: 1rpx solid $primary-color;
  
  &:hover {
    background: $primary-color;
    color: $text-inverse;
  }
}

// 卡片样式混入
@mixin card-base {
  background: $bg-primary;
  border-radius: $radius-lg;
  box-shadow: $shadow-base;
  overflow: hidden;
  transition: all $transition-fast;
}

@mixin card-hover {
  &:hover {
    transform: translateY(-2rpx);
    box-shadow: $shadow-lg;
  }
}

// 输入框样式混入
@mixin input-base {
  width: 100%;
  padding: $spacing-base $spacing-lg;
  border: 1rpx solid $border-light;
  border-radius: $radius-base;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  background: $bg-primary;
  color: $text-primary;
  transition: all $transition-fast;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 2rpx rgba($primary-color, 0.2);
  }
  
  &:disabled {
    background: $bg-tertiary;
    color: $text-tertiary;
    cursor: not-allowed;
  }
  
  &::placeholder {
    color: $text-tertiary;
  }
}

// 滚动条样式混入
@mixin scrollbar($width: 8rpx, $track-color: transparent, $thumb-color: rgba($neutral-400, 0.5)) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;
    
    &:hover {
      background: rgba($neutral-400, 0.8);
    }
  }
}

// 安全区域适配混入
@mixin safe-area-padding($direction: 'all') {
  @if $direction == 'top' {
    padding-top: calc(#{$safe-area-inset-top} + 20rpx);
  } @else if $direction == 'bottom' {
    padding-bottom: calc(#{$safe-area-inset-bottom} + 20rpx);
  } @else if $direction == 'left' {
    padding-left: calc(env(safe-area-inset-left) + 20rpx);
  } @else if $direction == 'right' {
    padding-right: calc(env(safe-area-inset-right) + 20rpx);
  } @else {
    padding-top: calc(#{$safe-area-inset-top} + 20rpx);
    padding-bottom: calc(#{$safe-area-inset-bottom} + 20rpx);
    padding-left: calc(env(safe-area-inset-left) + 20rpx);
    padding-right: calc(env(safe-area-inset-right) + 20rpx);
  }
}

// 动画混入
@mixin fade-in($duration: $transition-normal) {
  animation: fadeIn $duration ease-in-out;
}

@mixin slide-up($duration: $transition-normal) {
  animation: slideUp $duration ease-out;
}

@mixin scale-in($duration: $transition-normal) {
  animation: scaleIn $duration ease-out;
} 