<template>
  <view class="create-page">
    <!-- 顶部安全距离 -->
    <view class="create-safe-area"></view>
    <!-- 顶部AI设置栏固定 -->
    <view class="create-header">
      <image class="create-header__icon" src="@/asset/img/create/ai_setting_icon.png" @click="onBack" />
      <view class="create-header__right">
        <text class="create-header__title">AI设置</text>
        <image class="create-header__icon-right" src="@/asset/img/create/ai_setting_icon_right.png" />
      </view>
    </view>
    <!-- 内容区可滚动 -->
    <scroll-view class="create-scroll" scroll-y="true">
      <view class="create-content">
        <!-- 步骤1 上传视频素材 -->
        <view class="step-row">
          <view class="step-num">1</view>
          <text class="step-title">上传视频素材</text>
          <view class="example-mode-btn" :class="{ active: isExampleMode }" @click="toggleExampleMode">
            <image class="example-mode__icon" src="@/asset/img/create/example_mode.png" />
            <text class="example-mode__text">示例模式</text>
          </view>
        </view>
        <view class="material-box" v-if="isExampleMode">
          <view class="material-box__header example-header">
            <text class="material-box__desc-input example-desc"><text class="material-box__required">*</text>请上传视频素材，视频场景越多，效果越好</text>
            <view class="material-select" @click="openMaterialLibrary">
              <image class="material-select__icon" src="@/asset/img/create/material_select.png" />
              <text class="material-select__text">素材库</text>
            </view>
          </view>
          <view class="example-group-list">
            <view v-for="(group, gIdx) in exampleGroups" :key="gIdx" class="example-group">
              <text class="example-group__title">{{ group.title }}</text>
              <text class="example-group__desc">{{ group.desc }}</text>
              <view class="example-video-row">
                <view v-for="(video, vIdx) in group.videos" :key="vIdx" class="example-video-card">
                  <image :src="video.cover" class="example-video-card__img" />
                  <view class="example-video-card__play">
                    <image src="@/asset/img/create/example_mode.png" class="example-video-card__play-icon" />
                  </view>
                  <view class="example-video-card__label">示例</view>
                </view>
                <view class="example-upload-btn" @click="onExampleUpload(gIdx)">
                  <image class="upload-btn__icon" src="@/asset/img/create/upload_video.png" />
                  <text class="upload-btn__text">上传视频</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-else class="material-box">
          <view class="material-box__header">
            <view class="material-box__input-group">
              <text class="material-box__required">*</text>
              <textarea class="material-box__desc-input" v-model="videoDesc" placeholder="请上传视频素材，视频场景越多，效果越好" autoHeight :rows="1" />
            </view>
            <view class="material-select" @click="openMaterialLibrary">
              <image class="material-select__icon" src="@/asset/img/create/material_select.png" />
              <text class="material-select__text">素材库</text>
            </view>
          </view>
          <!-- 新增：已选视频内容 -->
          <view class="selected-video-list" v-if="selectedVideos.length">
            <view class="selected-video-item" v-for="(video, idx) in selectedVideos" :key="video.id">
              <image :src="video.coverUrl" class="selected-video-thumb" />
              <view class="selected-video-duration">{{ formatDuration(video.duration) }}</view>
              <view class="selected-video-remove" @click="removeSelectedVideo(idx)">×</view>
            </view>
          </view>
          <view class="upload-btn upload-btn--left" @click="onExampleUpload">
            <image class="upload-btn__icon" src="@/asset/img/create/upload_video.png" />
            <text class="upload-btn__text">上传视频</text>
          </view>
        </view>
        <!-- 步骤2 确认文案 -->
        <view class="step-row">
          <view class="step-num">2</view>
          <text class="step-title">确认文案</text>
        </view>
        <view class="doc-box doc-box--design" :class="{ 'doc-box--no-content': !docContent || !docContent.trim() }">
          <view class="doc-box__header" @click="openVideoDocDialog">
            <view class="doc-box__header-left flex-col">
              <text class="doc-box__title">
                <text class="doc-box__required">*</text>
                视频文案设置
              </text>
              <text class="doc-box__desc" v-if="docContent && docContent.trim()">
                已根据您的店铺信息自动生成{{ videoDocList.filter(item => item.content && item.content.trim()).length }}条文案
              </text>
              <text class="doc-box__desc doc-box__desc--empty" v-else>
                请设置视频文案
              </text>
            </view>
            <image class="doc-box__arrow" src="@/asset/img/create/confirm_doc_arrow.png" />
          </view>
          <view class="doc-box__divider" v-if="docContent && docContent.trim()"></view>
          <view class="doc-box__content-input" v-if="docContent && docContent.trim()">{{ docContent }}</view>
        </view>
        <!-- 步骤3 配音设置 -->
        <view class="step-row">
          <view class="step-num">3</view>
          <text class="step-title">配音设置</text>
        </view>
        <view class="voice-setting-bg">
          <scroll-view class="voice-setting__options-scroll" scroll-x="true" show-scrollbar="false" v-if="!voiceSettingExpanded">
            <view class="voice-setting__options">
              <view v-for="(item, idx) in voiceOptions[0].slice(0, 3)" :key="item" :class="['voice-setting__option', { 'voice-setting__option--active': voiceOptionIndex === idx }]" @click="voiceOptionIndex = idx">
                <text>{{ item.show_name }}</text>
              </view>
            </view>
          </scroll-view>
          <view class="voice-setting__options-expanded" v-if="voiceSettingExpanded">
            <view class="voice-setting__options-grid">
              <view v-for="(item, idx) in voiceOptions[0]" :key="item" :class="['voice-setting__option', { 'voice-setting__option--active': voiceOptionIndex === idx }]" @click="voiceOptionIndex = idx">
                <text>{{ item.show_name }}</text>
              </view>
            </view>
          </view>
          <view class="voice-setting__toggle" @click="voiceSettingExpanded = !voiceSettingExpanded">
            <text class="voice-setting__toggle-text">{{ voiceSettingExpanded ? '收起' : '展开' }}</text>
            <image class="voice-setting__toggle-arrow" :style="{ transform: voiceSettingExpanded ? 'rotate(180deg)' : 'none' }" src="@/asset/img/create/more_setting_arrow.png" />
          </view>
        </view>
        <!-- 步骤4 背景音乐设置 -->
        <view class="step-row">
          <view class="step-num">4</view>
          <text class="step-title">背景音乐设置</text>
        </view>
        <view class="music-box" :class="{ 'music-box--no-options': !musicEnabled }">
          <view class="music-box__row justify-between">
            <text class="music-box__label">背景音乐</text>
            <view class="switch-wrap"><switch color="#ff0043" :checked="musicEnabled" @change="e => (musicEnabled = e.detail.value)" /></view>
          </view>
          <scroll-view class="music-options-scroll" scroll-x="true" show-scrollbar="false" v-if="musicEnabled && !musicSettingExpanded">
            <view class="music-options">
              <view v-for="(item, idx) in musicList" :key="idx" :class="['music-option', { 'music-option--active': selectedMusicIndex === idx }]" @click="selectedMusicIndex = idx">
                <text>{{ item.name || '请选择' }}</text>
              </view>
            </view>
          </scroll-view>
          <view class="music-options-expanded" v-if="musicEnabled && musicSettingExpanded">
            <view class="music-options-grid">
              <view v-for="(item, idx) in musicList" :key="idx" :class="['music-option', { 'music-option--active': selectedMusicIndex === idx }]" @click="selectedMusicIndex = idx">
                <text>{{ item.name || '请选择' }}</text>
              </view>
            </view>
          </view>
          <view class="music-setting__toggle" v-if="musicEnabled && musicList.length > 3" @click="musicSettingExpanded = !musicSettingExpanded">
            <text class="music-setting__toggle-text">{{ musicSettingExpanded ? '收起' : '展开' }}</text>
            <image class="music-setting__toggle-arrow" :style="{ transform: musicSettingExpanded ? 'rotate(180deg)' : 'none' }" src="@/asset/img/create/more_setting_arrow.png" />
          </view>
        </view>

        <transition name="fade-slide">
          <view v-if="moreSettingOpen">
            <!-- 5. 字幕设置 -->
            <view class="step-row"><view class="step-num">5</view><text class="step-title">字幕设置</text></view>
            <view class="doc-box doc-box--design" :class="{ 'doc-box--no-content': !showTopFont }">
              <view class="subtitle-setting-row">
                <view class="subtitle-select-group">
                  <text class="subtitle-label">字体</text>
                  <picker mode="selector" :range="fontFamilyList" :value="fontFamilyIndex" @change="onFontFamilyChange" range-key="name">
                    <view class="subtitle-select-font">
                      <text class="subtitle-select-text">{{ fontFamilyList[fontFamilyIndex]?.name || '' }}</text>
                      <view class="subtitle-select-arrow"></view>
                    </view>
                  </picker>
                  <text class="subtitle-label">字号</text>
                  <picker mode="selector" :range="fontSizeList" :value="fontSizeIndex" @change="onFontSizeChange">
                    <view class="subtitle-select-size">
                      <text class="subtitle-select-text">{{ fontSizeList[fontSizeIndex] }}</text>
                      <view class="subtitle-select-arrow"></view>
                    </view>
                  </picker>
                </view>
              </view>
              <view class="doc-box__header">
                <text class="doc-box__title">显示顶部字体</text>
                <view class="switch-wrap"><switch color="#ff0043" :checked="showTopFont" @change="e => (showTopFont = e.detail.value)" /></view>
              </view>
              <view class="doc-box__header" v-if="showTopFont">
                <text class="doc-box__title">顶部字幕文案</text>
                <input class="cover-input" v-model="top_subtitle" placeholder="请输入顶部字幕文案" />
              </view>
            </view>
            <!-- 6. 封面设置 -->
            <view class="step-row"><view class="step-num">6</view><text class="step-title">封面设置</text></view>
            <view class="doc-box doc-box--design" :class="{ 'doc-box--no-content': !coverAddToStart }">
              <view class="doc-box__header">
                <text class="doc-box__title">生成封面并加到开头</text>
                <view class="switch-wrap"><switch color="#ff0043" :checked="coverAddToStart" @change="e => (coverAddToStart = e.detail.value)" /></view>
              </view>
              <view class="cover-input-row" v-if="coverAddToStart">
                <text class="cover-input-label">第一行</text>
                <input class="cover-input" v-model="coverLine1" placeholder="请输入" />
              </view>
              <view class="cover-input-row" v-if="coverAddToStart">
                <text class="cover-input-label">第二行</text>
                <input class="cover-input" v-model="coverLine2" placeholder="请输入" />
              </view>
            </view>
            <!-- 8. 视频样式设置 -->
            <view class="step-row"><view class="step-num">7</view><text class="step-title">视频样式设置</text></view>
            <view class="doc-box doc-box--design">
              <view class="music-box__row music-box__row--btns">
                <view :class="['music-box__btn', { 'music-box__btn--active': videoStyle === 'landscape' }]" @click="videoStyle = 'landscape'">横版 (16:9)</view>
                <view :class="['music-box__btn', { 'music-box__btn--active': videoStyle === 'portrait' }]" @click="videoStyle = 'portrait'">竖版 (9:16)</view>
              </view>
            </view>
          </view>
        </transition>
        <!-- 更多个性化设置按钮始终显示在立即生成视频按钮上方 -->
        <view class="more-setting" @click="moreSettingOpen = !moreSettingOpen">
          <text class="more-setting__text">更多个性化设置</text>
          <image class="more-setting__arrow" :style="{ transform: moreSettingOpen ? 'rotate(180deg)' : 'none' }" src="@/asset/img/create/more_setting_arrow.png" />
        </view>
        <!-- 底部占位，为固定按钮留出空间 -->
        <view class="create-bottom-space"></view>
      </view>
    </scroll-view>
    <!-- 立即生成视频按钮固定在底部 -->
    <view class="generate-btn-fixed">
      <view :class="['generate-btn', { 'generate-btn--active': canGenerate }]" @click="onGenerateVideo">
        <text>立即生成视频</text>
      </view>
    </view>
    <VideoDocDialog v-model="videoDocList" v-if="showVideoDocDialog" @close="closeVideoDocDialog" @confirm="onVideoDocConfirm" @add="onVideoDocAdd" @regenerate="onVideoDocRegenerate" @copyAdd="onVideoDocCopyAdd" @update:modelValue="onVideoDocListChange" :videoDocLoadingIdx="videoDocLoadingIdx" />
  </view>
</template>
<script>
import VideoDocDialog from './VideoDocDialog.vue'
import materialService from '@/service/material'
import { useUserStore } from '@/store/modules/user'

export default {
  name: 'CreatePage',
  components: {
    VideoDocDialog
  },
  data() {
    return {
      videoDesc: '',
      docContent: '',
      voiceOptions: [[]], // 动态生成，只保留一个数组
      voiceOptionIndex: 0,
      musicList: [], // 新增：音乐列表
      selectedMusicIndex: 0, // 新增：当前选中音乐索引
      musicEnabled: true, // 背景音乐开关
      moreSettingOpen: false,
      fontFamilyList: [], // [{name, path, ...}]
      fontFamilyIndex: 0,
      fontFamilyPath: '', // 当前选中的字体path
      fontSizeList: ['24', '32', '40', '48', '56', '64', '72', '80', '88', '96'],
      fontSizeIndex: 0,
      showTopFont: true,
      top_subtitle: '', // 顶部字幕文案
      coverAddToStart: true,
      coverLine1: '',
      coverLine2: '',
      videoStyle: 'portrait',
      showDocDialog: false,
      docTabIndex: 0,
      docTypes: ['AI生成', '输入/提取'],
      docTypeIndex: 0,
      customRequest: '',
      showVideoDocDialog: false,
      videoDocList: [{ content: '' }],
      isExampleMode: false,
      selectedMaterial: null,
      exampleGroups: [
        {
          title: '场景1：老板出镜相关场景',
          desc: '拍摄顾客进店时，老板打招呼，点头微笑或招招手，展现了店内的老板很有亲和力',
          videos: [{ cover: '@/asset/img/create/example_mode.png', label: '示例', isExample: true }]
        },
        {
          title: '场景2：门店形象+相关场景',
          desc: '拍摄店内陈列4-10秒视频上传',
          videos: [{ cover: '@/asset/img/create/example_mode.png', label: '示例', isExample: true }]
        },
        {
          title: '场景3：收银场景',
          desc: '拍摄店内收银/收款场面4-10秒视频上传',
          videos: [{ cover: '@/asset/img/create/example_mode.png', label: '示例', isExample: true }]
        }
      ],
      selectedVideos: [],
      videoDocLoadingIdx: null, // 当前生成中的文案索引
      voiceSettingExpanded: false, // 新增：配音设置是否展开
      musicSettingExpanded: false, // 新增：背景音乐设置是否展开
    }
  },
  computed: {
    canGenerate() {
      return this.selectedVideos.length > 0 && this.docContent.trim().length > 0
    },
  },
  created() {
    // 移除全局store依赖，数据在onLoad中拉取
  },
  async onLoad() {
    // 进入页面时拉取字体、语音角色、音乐列表
    try {
      const [fontRes, voiceRes, musicRes] = await Promise.all([
        materialService.getFontList(),
        materialService.getVoiceRoleList(),
        materialService.getMusicList()
      ])
      // 字体
      if (fontRes && Array.isArray(fontRes.data)) {
        this.fontFamilyList = fontRes.data.map(m => ({ ...m, name: m.name === 'default' ? '默认' : m.name }))
        this.fontFamilyPath = fontRes.data[0]?.path || ''
      } else {
        this.fontFamilyList = []
        this.fontFamilyPath = ''
      }
      // 语音角色
      if (voiceRes && Array.isArray(voiceRes.data)) {
        this.voiceOptions[0] = voiceRes.data
      } else {
        this.voiceOptions[0] = []
      }
      // 音乐列表
      if (musicRes && musicRes.status_code === 1 && Array.isArray(musicRes.data)) {
        this.musicList = musicRes.data.map(m => ({ ...m, name: m.name === 'default' ? '默认' : m.name }))
      } else {
        this.musicList = []
      }
      
      // 页面加载完成后自动生成一条默认文案
      this.generateDefaultDoc()
    } catch (err) {
      this.fontFamilyList = []
      this.fontFamilyPath = ''
      this.voiceOptions[0] = []
      this.musicList = []
      
      // 即使加载失败也尝试生成默认文案
      this.generateDefaultDoc()
    }
  },
  onShow() {
    // 页面显示时检查本地存储
    const selected = uni.getStorageSync('selectedMaterials')
    if (selected && Array.isArray(selected) && selected.length) {
      this.selectedVideos = selected
      uni.removeStorageSync('selectedMaterials')
      
      // 如果用户选择了素材，重新生成文案
      if (this.selectedVideos.length > 0 && (!this.docContent || this.docContent.trim() === '')) {
        this.generateDefaultDoc()
      }
    }
  },
  methods: {
    onFontFamilyChange(e) {
      this.fontFamilyIndex = e.detail.value
      this.fontFamilyPath = this.fontFamilyList[this.fontFamilyIndex].path
    },
    onFontSizeChange(e) {
      this.fontSizeIndex = e.detail.value
    },
    
    // 生成默认文案
    generateDefaultDoc() {
      // 确保 videoDocList 有初始数据
      if (!this.videoDocList || this.videoDocList.length === 0) {
        this.videoDocList = [{ content: '' }]
      }
      
      // 生成默认文案
      this.handleDocGenerate({
        function_type: 1, // 1-生成
        origin_text: '',
        loadingIdx: 0,
        loadingTitle: '生成默认文案中...',
        prompt_type: 'default', // 使用默认文案类型
        custom_requirements: '请生成一条适合短视频的吸引人的文案',
        onSuccess: (content) => {
          this.$set(this.videoDocList, 0, { content })
          this.videoDocList = [...this.videoDocList]
          // 同步到主文案内容
          this.docContent = content
        }
      })
    },



    openVideoDocDialog() {
      this.showVideoDocDialog = true
    },
    closeVideoDocDialog() {
      this.showVideoDocDialog = false
    },
    onVideoDocConfirm(list) {
      this.videoDocList = list
      this.showVideoDocDialog = false
      // 可根据需要同步到主文案内容
      this.docContent = list.map((item, idx) => `${item.content}`).join('\n\n')
    },
    onVideoDocAdd(payload) {
      // payload: { docType, customRequest }
      const idx = this.videoDocList.length;
      this.videoDocList.push({ content: '' });
      this.handleDocGenerate({
        function_type: 1, // 1-生成
        origin_text: '',
        loadingIdx: idx,
        loadingTitle: '文案生成中...',
        prompt_type: payload.docType, // 传递文案类型
        custom_requirements: payload.customRequest, // 预留自定义要求
        onSuccess: (content) => {
          this.$set(this.videoDocList, idx, { content });
          this.videoDocList = [...this.videoDocList];
          this.docContent = this.videoDocList.map(item => item.content).join('\n\n');
        }
      });
    },
    /**
     * AI文案生成/仿写/改写
     * @param {Object} options
     * @param {number} options.function_type 1-生成 2-仿写 3-改写
     * @param {string} options.origin_text 原文案（仿写/改写时传）
     * @param {string} [options.prompt_type] 文案类型（传给接口的 prompt_type）
     * @param {string} [options.custom_requirements] 自定义要求（传给接口的 custom_requirements）
     * @param {number} options.loadingIdx 当前操作的索引
     * @param {string} [options.loadingTitle] loading提示
     * @param {function} options.onSuccess 成功回调，参数为生成内容
     */
    handleDocGenerate({ function_type, origin_text, prompt_type = '', custom_requirements = '', loadingIdx, loadingTitle = '生成中...', onSuccess }) {
      this.videoDocLoadingIdx = loadingIdx
      uni.showLoading({ title: loadingTitle })
      this.$set(this.videoDocList[loadingIdx], 'loading', true)
      materialService.generateDoc({
        function_type,
        prompt_type, // 新增：文案类型
        custom_requirements, // 预留：自定义要求
        origin_text,
        compute_account_type: 1 //  算力账户类型（1:个人2:团队）
      }).then(res => {
        if (res && res.status_code === 1 && res.data && res.data.result_text) {
          onSuccess && onSuccess(res.data.result_text)
        } else {
          uni.showToast({ title: res?.message || '生成失败', icon: 'none' })
        }
      }).catch((err) => {
        uni.showToast({ title: err?.message || '生成失败', icon: 'none' })
      }).finally(() => {
        this.$set(this.videoDocList[loadingIdx], 'loading', false)
        this.videoDocLoadingIdx = null
        uni.hideLoading()
      })
    },
    onVideoDocRegenerate(idx) {
      const origin_text = this.videoDocList[idx]?.content || ''
      this.handleDocGenerate({
        function_type: 1,
        origin_text,
        loadingIdx: idx,
        loadingTitle: '文案生成中...',
        onSuccess: (content) => {
          this.$set(this.videoDocList, idx, { content })
          this.videoDocList = [...this.videoDocList]
        }
      })
    },
    onVideoDocCopyAdd(idx) {
      const origin_text = this.videoDocList[idx]?.content || ''
      if (!origin_text) return
      this.handleDocGenerate({
        function_type: 2,
        origin_text,
        loadingIdx: idx,
        loadingTitle: '仿写生成中...',
        onSuccess: (content) => {
          // 只插入AI返回的新仿写内容
          this.videoDocList.push({ content })
          this.videoDocList = [...this.videoDocList]
        }
      })
    },
    onVideoDocListChange(list) {
      this.videoDocList = list
    },
    toggleExampleMode() {
      this.isExampleMode = !this.isExampleMode
    },
    onExampleUpload(gIdx) {
      // 跳转到原生相册选择页面
      uni.navigateTo({
        url: '/pages/native-gallery/index',
        success: () => {
          console.log('Successfully navigated to native gallery selector page')
        },
        fail: err => {
          console.error('跳转失败:', err)
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    },
    onBack() {
      if (typeof uni !== 'undefined' && uni.navigateBack) {
        uni.navigateBack()
      } else if (window && window.history) {
        window.history.back()
      }
    },
    async onGenerateVideo() {
      // 组装参数
      // material_ids: 选中的素材ID数组
      const material_ids = this.selectedVideos.map(v => v.id)
      // script_content: 视频主文案内容
      const script_content = this.docContent
      // script_list: 多条分段文案
      const script_list = this.videoDocList.map(item => item.content)
      // cover_required: 是否需要生成封面
      const cover_required = this.coverAddToStart
      // cover_texts: 封面文案数组
      const cover_texts = [this.coverLine1, this.coverLine2].filter(Boolean)
      const top_subtitle = this.top_subtitle // 使用用户输入的顶部字幕文案
      const top_subtitle_enabled = this.showTopFont
      const voice_type = this.voiceOptions[0][this.voiceOptionIndex]?.code || ''
      const voice_speed = 1
      const voice_pitch = 1
      const resolution_width = this.videoStyle === 'landscape' ? 1920 : 1080
      const resolution_height = this.videoStyle === 'landscape' ? 1080 : 1920
      const subtitle_enabled = true
      const subtitle_font_size = Number(this.fontSizeList[this.fontSizeIndex])
      const subtitle_font_color = 'white'
      let subtitle_font = this.fontFamilyList[this.fontFamilyIndex]?.name || ''
      if (subtitle_font === '默认') subtitle_font = 'default'
      const subtitle_position = 0.9
      const music_enabled = this.musicEnabled
      // music_name: 背景音乐名称（“默认”则传'default'）
      let music_name = this.musicList[this.selectedMusicIndex]?.name || ''
      if (music_name === '默认') music_name = 'default'
      const music_volume = 0.3

      const params = {
        material_ids,         // 选中的素材ID数组
        script_content,       // 视频主文案内容
        script_list,          // 多条分段文案
        cover_required,       // 是否需要生成封面
        cover_texts,          // 封面文案数组
        top_subtitle,         // 顶部字幕内容
        top_subtitle_enabled, // 是否启用顶部字幕
        voice_type,           // 配音类型/角色的 code
        voice_speed,          // 配音语速
        voice_pitch,          // 配音音调
        resolution_width,     // 视频分辨率宽度
        resolution_height,    // 视频分辨率高度
        subtitle_enabled,     // 是否启用字幕
        subtitle_font_size,   // 字幕字号
        subtitle_font_color,  // 字幕颜色
        subtitle_font,        // 字体名称
        subtitle_position,    // 字幕垂直位置
        music_enabled,        // 是否启用背景音乐
        music_name,           // 背景音乐名称
        music_volume,          // 背景音乐音量
        compute_account_type: 1 //  算力账户类型（1:个人2:团队）
      }
      try {
        uni.showLoading({ title: '生成中...' })
        const res = await materialService.createVideoEditingTask(params)
        uni.hideLoading()
        if (res.status_code !== 1) {
          uni.showToast({ title: res.message || '任务提交失败', icon: 'none' })
          return
        }
        // 优先使用 data.task_id 作为任务ID
        const taskId = res?.data?.task_id
        const successMsg = res?.data?.message || res?.message || '任务已提交'
        uni.showToast({ title: successMsg, icon: 'success' })
        // 跳转到分析页面并传递任务id
        if (taskId) {
          uni.navigateTo({ url: `/pages/trending/analyse?id=${taskId}` })
        }
      } catch (err) {
        uni.hideLoading()
        uni.showToast({ title: err.message || '生成失败', icon: 'none' })
      }
    },
    openMaterialLibrary() {
      uni.navigateTo({
        url: '/pages/native-gallery/index?tab=library',
        success: () => {
          console.log('Successfully navigated to material library')
        },
        fail: err => {
          console.error('跳转失败:', err)
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    },
    removeSelectedVideo(idx) {
      this.selectedVideos.splice(idx, 1)
    },
    formatDuration(seconds) {
      if (!seconds) return '00:00'
      const min = Math.floor(seconds / 60)
      const sec = Math.floor(seconds % 60)
      return `${min.toString().padStart(2, '0')}:${sec.toString().padStart(2, '0')}`
    },
    // 新增：切换音乐
    onSelectMusic(idx) {
      this.selectedMusicIndex = idx
    },
  }
}
</script>
<style scoped>
.create-page {
  background: #111113;
  width: 750rpx;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}
.create-safe-area {
  height: var(--status-bar-height);
  width: 100%;
  background-color: #111113;
}
.create-header {
  position: fixed;
  top: var(--status-bar-height);
  left: 0;
  width: 750rpx;
  z-index: 10;
  background: #111113;
  display: flex;
  align-items: center;
  height: 100rpx;
  padding: 0 32rpx;
  border-bottom: 2rpx solid #232325;
  justify-content: space-between;
}
.create-header__icon {
  width: 16rpx;
  height: 30rpx;
  display: block;
}
.create-header__right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.create-header__title {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}
.create-header__icon-right {
  width: 26rpx;
  height: 24rpx;
  display: block;
}
.create-scroll {
  position: absolute;
  top: calc(var(--status-bar-height) + 100rpx);
  left: 0;
  right: 0;
  bottom: 0;
  width: 750rpx;
  height: calc(100vh - var(--status-bar-height) - 100rpx);
}
.create-content {
  padding: 0 24rpx 40rpx 24rpx;
}
.step-row {
  display: flex;
  align-items: center;
  margin: 44rpx 0 0 24rpx;
}
.step-num {
  background: #ff0043;
  color: #fff;
  border-radius: 4rpx;
  padding: 4rpx 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-right: 16rpx;
}
.step-title {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.example-mode {
  background: #383839;
  border-radius: 12rpx;
  margin-left: auto;
  padding: 8rpx 18rpx;
  display: flex;
  align-items: center;
}
.example-mode__icon {
  width: 20rpx;
  height: 22rpx;
  margin-right: 8rpx;
  display: block;
}
.example-mode__text {
  font-size: 24rpx;
  color: #fff;
}
.material-box {
  background: #212124;
  border-radius: 24rpx;
  margin: 30rpx auto 0 auto;
  width: 702rpx;
  padding: 18rpx 24rpx 26rpx 24rpx;
}
.material-box__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
  gap: 16rpx;
}
.material-box__header.example-header {
  margin-bottom: 0;
}
.material-box__desc-input {
  flex: 1;
  font-size: 24rpx;
  color: #bfbfbf;
  background: transparent;
  border: none;
  resize: none;
  outline: none;
  padding: 0;
  margin-right: 0;
  line-height: 36rpx;
  min-width: 0;
  overflow: hidden;
}
.material-box__input-group {
  display: flex;
  align-items: flex-start;
  flex: 1;
  min-width: 0;
}
.material-box__input-group .material-box__required {
  color: #ff0043;
  margin-right: 4rpx;
  margin-top: 6rpx;
  flex-shrink: 0;
}
.material-box__input-group .material-box__desc-input {
  flex: 1;
  margin-right: 0;
}
.material-select {
  background: #383839;
  border-radius: 12rpx;
  padding: 8rpx 18rpx;
  display: flex;
  align-items: center;
  height: 48rpx;
  flex-shrink: 0;
  min-width: 120rpx;
}
.material-select__icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  display: block;
}
.material-select__text {
  font-size: 24rpx;
  color: #bfbfbf;
}
.material-select__arrow {
  width: 12rpx;
  height: 24rpx;
  margin-left: 8rpx;
  display: block;
}
.upload-btn {
  padding: 40rpx 32rpx 34rpx 32rpx;
}
.upload-btn__icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 12rpx;
  display: block;
  object-fit: contain;
}
.upload-btn__text {
  font-size: 24rpx;
  color: #fff;
  white-space: nowrap;
  text-align: center;
}
.upload-btn.upload-btn--left {
  margin: 32rpx 0 0 0;
  background: #353537;
  border-radius: 16rpx;
  width: 160rpx;
  height: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
}

.doc-box__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.doc-box__header-left.flex-col {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6rpx;
}
.doc-box__title {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}
.doc-box__desc {
  font-size: 24rpx;
  color: #8c8c8c;
  margin-top: 8rpx;
}
.doc-box__arrow {
  width: 12rpx;
  height: 24rpx;
  display: block;
}
.doc-box__divider {
  width: 654rpx;
  height: 2rpx;
  margin: 26rpx 0 0 0;
  display: block;
}
.doc-box__content {
  font-size: 24rpx;
  color: #fff;
  margin-top: 20rpx;
  line-height: 34rpx;
}
.doc-box__content-input {
  width: 100%;
  font-size: 24rpx;
  color: #fff;
  background: transparent;
  border: none;
  resize: none;
  outline: none;
  padding: 0;
  line-height: 36rpx;
  min-height: 72rpx;
}
.doc-box--design {
  background: #232325;
  border-radius: 20rpx;
  margin: 32rpx auto 0 auto;
  width: 702rpx;
  padding: 24rpx 20rpx 28rpx 20rpx;
  box-sizing: border-box;
}
.doc-box--no-content {
  padding: 24rpx 20rpx 24rpx 20rpx;
}
.doc-box--no-content .doc-box__header {
  margin-bottom: 0;
}
.doc-box--no-content .subtitle-setting-row {
  margin-bottom: 0;
}
.doc-box--no-content .cover-input-row {
  margin-top: 0;
}
.doc-box__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}
.doc-box__title {
  font-size: 26rpx;
  color: #fff;
  font-weight: 500;
}
.doc-box__required {
  color: #ff0043;
  margin-right: 4rpx;
}
.doc-box__desc {
  font-size: 22rpx;
  color: #bfbfbf;
  margin-top: 6rpx;
}
.doc-box__desc--empty {
  color: #ff0043;
}
.doc-box__arrow {
  width: 12rpx;
  height: 24rpx;
  display: block;
}
.doc-box__divider {
  width: 100%;
  height: 2rpx;
  background: #353537;
  margin: 18rpx 0 18rpx 0;
  border-radius: 1rpx;
}
.doc-box__content {
  font-size: 24rpx;
  color: #fff;
  line-height: 36rpx;
  word-break: break-all;
}
.doc-box__content-input {
  width: 100%;
  font-size: 24rpx;
  color: #fff;
  background: transparent;
  border: none;
  resize: none;
  outline: none;
  padding: 0;
  line-height: 36rpx;
  min-height: 72rpx;
}
.doc-box__content-input--empty {
  color: #8c8c8c;
}
.voice-setting-bg {
  background: #232325;
  border-radius: 24rpx;
  margin: 32rpx auto 0 auto;
  width: 702rpx;
  padding: 24rpx 0 24rpx 0;
}
.voice-setting__options-scroll {
  width: 100%;
  overflow: visible;
  white-space: nowrap;
}
.voice-setting__options {
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  white-space: nowrap;
  position: relative;
}
.voice-setting__options::after {
  content: '';
  display: inline-block;
  width: 4rpx;
  height: 1px;
  flex-shrink: 0;
}
.voice-setting__option {
  background: #383839;
  border-radius: 12rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  color: #bfbfbf;
  white-space: nowrap;
  text-align: center;
  cursor: pointer;
  transition: border 0.2s, color 0.2s;
  border: 2rpx solid transparent;
  margin-right: 16rpx;
}

.voice-setting__option--active {
  border: 2rpx solid #ff0043;
  color: #ff0043;
  font-weight: 500;
}
/* 强制覆盖uni-text默认white-space，防止换行 */
.voice-setting__option text {
  white-space: nowrap !important;
  display: inline-block;
}
.voice-setting__options-expanded {
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  white-space: nowrap;
  position: relative;
}
.voice-setting__options-expanded::after {
  content: '';
  display: inline-block;
  width: 4rpx;
  height: 1px;
  flex-shrink: 0;
}
.voice-setting__options-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
  margin-top: 20rpx;
  padding: 0 24rpx;
  width: 100%;
}
.voice-setting__options-grid .voice-setting__option {
  background: #383839;
  border-radius: 12rpx;
  padding: 16rpx 8rpx;
  font-size: 24rpx;
  color: #bfbfbf;
  white-space: nowrap;
  text-align: center;
  cursor: pointer;
  transition: border 0.2s, color 0.2s;
  border: 2rpx solid transparent;
  margin-right: 0; /* Remove margin-right for grid layout */
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.voice-setting__options-grid .voice-setting__option--active {
  border: 2rpx solid #ff0043;
  color: #ff0043;
  font-weight: 500;
}
.voice-setting__toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
  padding: 8rpx 0;
  cursor: pointer;
  transition: opacity 0.2s;
}
.voice-setting__toggle:active {
  opacity: 0.7;
}
.voice-setting__toggle-text {
  font-size: 24rpx;
  color: #8c8c8c;
  margin-right: 8rpx;
}
.voice-setting__toggle-arrow {
  width: 20rpx;
  height: 10rpx;
  display: block;
}
.music-box {
  background: #212124;
  border-radius: 24rpx;
  margin: 32rpx auto 0 auto;
  width: 702rpx;
  padding: 24rpx 24rpx 24rpx 24rpx;
}
.music-box--no-options {
  padding: 24rpx 24rpx 24rpx 24rpx;
}
.music-box--no-options .music-box__row {
  margin-bottom: 0;
}
.music-box__row {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 24rpx;
  flex-wrap: nowrap;
}
.music-box__row.justify-between {
  justify-content: space-between;
}
.music-box__btn {
  background: #383839;
  border-radius: 16rpx;
  min-width: 140rpx;
  padding: 22rpx 36rpx;
  font-size: 28rpx;
  color: #bfbfbf;
  white-space: nowrap;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  cursor: pointer;
  transition: border 0.2s, color 0.2s;
  border: 2rpx solid transparent;
}
.music-box__btn--active {
  border: 2rpx solid #ff0043;
  color: #ff0043;
  font-weight: 500;
}
.music-box__label {
  font-size: 28rpx;
  color: #fff;
  white-space: nowrap;
  margin-right: 24rpx;
  flex-shrink: 0;
}

/* 音乐选项滚动样式 */
.music-options-scroll {
  width: 100%;
  overflow: visible;
  white-space: nowrap;
}

.music-options {
  display: flex;
  align-items: center;
  padding: 0 0 24rpx 0;
  white-space: nowrap;
  position: relative;
}

.music-options::after {
  content: '';
  display: inline-block;
  width: 4rpx;
  height: 1px;
  flex-shrink: 0;
}

.music-option {
  background: #383839;
  border-radius: 12rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  color: #bfbfbf;
  white-space: nowrap;
  text-align: center;
  cursor: pointer;
  transition: border 0.2s, color 0.2s;
  border: 2rpx solid transparent;
  margin-right: 16rpx;
}

.music-option--active {
  border: 2rpx solid #ff0043;
  color: #ff0043;
  font-weight: 500;
}

/* 强制覆盖uni-text默认white-space，防止换行 */
.music-option text {
  white-space: nowrap !important;
  display: inline-block;
}

/* 背景音乐设置展开/收起样式 */
.music-options-expanded {
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  white-space: nowrap;
  position: relative;
}
.music-options-expanded::after {
  content: '';
  display: inline-block;
  width: 4rpx;
  height: 1px;
  flex-shrink: 0;
}
.music-options-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 20rpx;
  width: 100%;
}
.music-options-grid .music-option {
  background: #383839;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  font-size: 24rpx;
  color: #bfbfbf;
  white-space: nowrap;
  text-align: center;
  cursor: pointer;
  transition: border 0.2s, color 0.2s;
  border: 2rpx solid transparent;
  margin-right: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.music-options-grid .music-option--active {
  border: 2rpx solid #ff0043;
  color: #ff0043;
  font-weight: 500;
}
.music-setting__toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
  padding: 8rpx 0;
  cursor: pointer;
  transition: opacity 0.2s;
}
.music-setting__toggle:active {
  opacity: 0.7;
}
.music-setting__toggle-text {
  font-size: 24rpx;
  color: #8c8c8c;
  margin-right: 8rpx;
}
.music-setting__toggle-arrow {
  width: 20rpx;
  height: 10rpx;
  display: block;
}
.music-box__auto {
  display: flex;
  align-items: center;
  margin-left: auto;
  white-space: nowrap;
  flex-shrink: 0;
}
.music-box__auto-text {
  font-size: 24rpx;
  color: #bfbfbf;
  white-space: nowrap;
  text-align: right;
}
.music-box__auto-arrow {
  width: 12rpx;
  height: 24rpx;
  margin-left: 8rpx;
  display: block;
}
.more-setting {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 58rpx 0 0 0;
}
.more-setting__text {
  font-size: 28rpx;
  color: #8c8c8c;
}
.more-setting__arrow {
  width: 24rpx;
  height: 12rpx;
  margin-left: 12rpx;
  display: block;
}
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}
.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  max-height: 0;
}
.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
  max-height: 2000rpx;
}
.example-header {
  margin-bottom: 0;
}
.example-desc {
  flex: 1;
  font-size: 24rpx;
  color: #ff0043;
  background: transparent;
  border: none;
  resize: none;
  outline: none;
  padding: 0;
  margin-right: 0;
  line-height: 36rpx;
}
.example-group-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin-top: 24rpx;
}
.example-group {
  margin-bottom: 0;
}
.example-group__title {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}
.example-group__desc {
  font-size: 24rpx;
  color: #bfbfbf;
  margin-bottom: 18rpx;
  display: block;
}
.example-video-row {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}
.example-video-card {
  position: relative;
  background: #232325;
  border-radius: 16rpx;
  width: 160rpx;
  height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.example-video-card__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16rpx;
}
.example-video-card__play {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.example-video-card__play-icon {
  width: 48rpx;
  height: 48rpx;
  /* 这里建议换成播放icon */
}
.example-video-card__label {
  position: absolute;
  top: 0;
  right: 0;
  background: #ff0043;
  color: #fff;
  font-size: 20rpx;
  border-radius: 0 0 0 12rpx;
  padding: 4rpx 12rpx;
  z-index: 2;
}
.example-upload-btn {
  background: #353537;
  border-radius: 16rpx;
  width: 160rpx;
  height: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
  margin-left: 0;
  opacity: 1;
}
.example-upload-btn--disabled {
  opacity: 0.5;
  pointer-events: none;
}
.example-mode-btn {
  background: #383839;
  border-radius: 12rpx;
  padding: 8rpx 18rpx;
  color: #fff;
  margin-left: auto;
  cursor: pointer;
  font-size: 24rpx;
  display: flex;
  align-items: center;
}
.example-mode-btn.active {
  background: #ff0043;
  color: #fff;
}
.selected-video-list {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
  margin: 16rpx 0 0 0;
}
.selected-video-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #232325;
  display: flex;
  align-items: center;
  justify-content: center;
}
.selected-video-thumb {
  width: 120px;
  height: 90px;
  object-fit: contain;
  border-radius: 12rpx;
  display: block;
  margin: 0 auto;
}
.selected-video-duration {
  position: absolute;
  left: 8rpx;
  bottom: 8rpx;
  background: rgba(0,0,0,0.6);
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}
.selected-video-remove {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  color: #fff;
  background: #ff0043;
  border-radius: 50%;
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  cursor: pointer;
}
.subtitle-setting-row {
  margin-bottom: 32rpx;
}
.subtitle-select-group {
  display: flex;
  align-items: center;
  gap: 24rpx;
  width: 100%;
}
.subtitle-label {
  font-size: 26rpx;
  color: #fff;
  min-width: 60rpx;
}
.subtitle-select-font {
  background: #353537;
  border-radius: 12rpx;
  padding: 0 32rpx;
  height: 56rpx;
  min-width: 240rpx;
  width: 36%;
  display: flex;
  align-items: center;
  position: relative;
  margin-right: 12rpx;
}
.subtitle-select-size {
  background: #353537;
  border-radius: 12rpx;
  padding: 0 32rpx;
  height: 56rpx;
  min-width: 160rpx;
  width: 24%;
  display: flex;
  align-items: center;
  position: relative;
  margin-right: 12rpx;
}
.subtitle-select-text {
  color: #bfbfbf;
  font-size: 26rpx;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.subtitle-select-arrow {
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-top: 12rpx solid #8c8c8c;
  margin-left: 12rpx;
}
.cover-input-row {
  display: flex;
  align-items: center;
  margin-top: 24rpx;
}
.cover-input-label {
  font-size: 26rpx;
  color: #fff;
  min-width: 80rpx;
}
.cover-input {
  background: #353537;
  border-radius: 12rpx;
  padding: 18rpx 32rpx;
  color: #bfbfbf;
  font-size: 26rpx;
  flex: 1;
  margin-left: 24rpx;
  border: none;
  outline: none;
  height: 72rpx;
  line-height: 72rpx;
}
.create-bottom-space {
  height: 100rpx; /* 为固定按钮留出空间 */
}
.generate-btn-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background: #141414;
  padding: 16rpx 24rpx;
  box-sizing: border-box;
}
.generate-btn-fixed .generate-btn {
  background: #4d4d4f;
  border-radius: 30rpx;
  padding: 20rpx 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.generate-btn-fixed .generate-btn--active {
  background: #ff0043;
}
.generate-btn-fixed .generate-btn text {
  font-size: 28rpx;
  color: #fff;
  white-space: nowrap;
  text-align: center;
}
</style>
