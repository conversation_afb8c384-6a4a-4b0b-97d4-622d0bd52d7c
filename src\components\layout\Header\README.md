# Header 布局头部组件

## 组件描述

Header 是一个通用的布局头部组件，提供标题显示、返回按钮、设置按钮和插槽功能。支持深色模式、自定义样式和灵活的内容布局。

## 功能特性

- 🎯 **固定定位** - 固定在页面顶部，始终可见
- 🎨 **主题支持** - 支持浅色和深色主题切换
- 📱 **安全区域** - 自动适配刘海屏等安全区域
- 🔄 **插槽系统** - 支持左侧、中间、右侧插槽自定义
- 🎭 **灵活配置** - 可配置标题、返回按钮、设置按钮

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '' | 头部标题 |
| showBack | Boolean | false | 是否显示返回按钮 |
| showSettings | Boolean | false | 是否显示设置按钮 |
| backgroundColor | String | '' | 自定义背景色 |
| textColor | String | '' | 自定义文字颜色 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| back | - | 返回按钮点击时触发 |
| settings | - | 设置按钮点击时触发 |

## Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| left | 左侧区域自定义内容 |
| center | 中间区域自定义内容 |
| right | 右侧区域自定义内容 |

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <Header 
      title="我的页面"
      :show-back="true"
      @back="handleBack"
    />
    <!-- 页面内容 -->
  </view>
</template>

<script setup>
import Header from '@/components/layout/Header/index.vue'

const handleBack = () => {
  console.log('返回上一页')
}
</script>
```

### 带设置按钮

```vue
<template>
  <view>
    <Header 
      title="设置"
      :show-back="true"
      :show-settings="true"
      @back="handleBack"
      @settings="handleSettings"
    />
    <!-- 设置页面内容 -->
  </view>
</template>

<script setup>
import Header from '@/components/layout/Header/index.vue'

const handleBack = () => {
  console.log('返回上一页')
}

const handleSettings = () => {
  console.log('打开设置')
}
</script>
```

### 使用插槽自定义内容

```vue
<template>
  <view>
    <Header title="聊天">
      <!-- 左侧插槽 -->
      <template #left>
        <view class="custom-left">
          <text class="custom-icon">👤</text>
        </view>
      </template>
      
      <!-- 中间插槽 -->
      <template #center>
        <view class="custom-center">
          <text class="custom-title">张三</text>
          <text class="custom-subtitle">在线</text>
        </view>
      </template>
      
      <!-- 右侧插槽 -->
      <template #right>
        <view class="custom-right">
          <text class="custom-icon">📞</text>
          <text class="custom-icon">📹</text>
        </view>
      </template>
    </Header>
    <!-- 页面内容 -->
  </view>
</template>

<script setup>
import Header from '@/components/layout/Header/index.vue'
</script>

<style lang="scss" scoped>
.custom-left,
.custom-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.custom-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.custom-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.custom-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

.custom-icon {
  font-size: 36rpx;
  padding: 8rpx;
}
</style>
```

### 自定义样式

```vue
<template>
  <view>
    <Header 
      title="个性化头部"
      :show-back="true"
      background-color="#ff6b6b"
      text-color="#ffffff"
      @back="handleBack"
    />
    <!-- 页面内容 -->
  </view>
</template>

<script setup>
import Header from '@/components/layout/Header/index.vue'

const handleBack = () => {
  console.log('返回上一页')
}
</script>
```

### 深色模式支持

```vue
<template>
  <view>
    <Header 
      title="深色主题"
      :show-back="true"
      :show-settings="true"
      @back="handleBack"
      @settings="handleSettings"
    />
    <!-- 页面内容 -->
  </view>
</template>

<script setup>
import Header from '@/components/layout/Header/index.vue'
import { useAppStore } from '@/store/modules/app'

const appStore = useAppStore()

const handleBack = () => {
  console.log('返回上一页')
}

const handleSettings = () => {
  // 切换深色模式
  appStore.toggleDarkMode()
}
</script>
```

## 样式定制

组件使用 SCSS 编写，主要样式特点：

- 高度：`$header-height` + `$safe-area-inset-top`
- 背景：支持主题切换和自定义背景色
- 标题：居中显示，支持文字省略
- 按钮：圆形背景，支持交互效果
- 安全区域：自动适配不同设备

## 布局结构

```
Header
├── 安全区域 (Safe Area)
└── 内容区域
    ├── 左侧区域 (80rpx)
    │   ├── 返回按钮 (可选)
    │   └── 左侧插槽
    ├── 中间区域 (绝对定位居中)
    │   ├── 标题文字
    │   └── 中间插槽
    └── 右侧区域 (80rpx)
        ├── 设置按钮 (可选)
        └── 右侧插槽
```

## 主题支持

组件支持深色模式，通过 `appStore.isDark` 自动切换：

- **浅色模式**：白色背景，深色文字
- **深色模式**：深色背景，浅色文字

## 注意事项

1. 组件固定在顶部，注意页面内容需要预留顶部空间
2. 返回按钮会自动调用 `appStore.navigateBack()`
3. 设置按钮会自动跳转到设置页面
4. 标题文字会自动居中并支持省略
5. 组件使用 `z-index: 999`，确保在其他元素之上

## 最佳实践

1. **标题简洁**：保持标题简洁明了，建议不超过 8 个字符
2. **插槽使用**：合理使用插槽实现自定义布局
3. **主题一致**：在整个应用中保持头部样式的一致性
4. **响应式**：在不同设备上测试显示效果
5. **交互反馈**：为按钮添加适当的交互反馈

## 与其他组件配合

Header 通常与以下组件配合使用：

- **TabBar**: 底部标签栏
- **PageHeader**: 页面头部组件
- **CustomNavBar**: 自定义导航栏

形成完整的页面布局体系。 