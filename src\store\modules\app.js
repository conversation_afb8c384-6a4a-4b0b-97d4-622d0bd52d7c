import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 应用状态
  const loading = ref(false)
  const theme = ref('light')
  const language = ref('zh-CN')
  const systemInfo = ref({})
  const networkType = ref('wifi')
  const isOnline = ref(true)
  
  // 页面状态
  const currentPage = ref('home')
  const pageStack = ref([])
  const tabBarVisible = ref(true)
  
  // 全局配置
  const config = ref({
    apiBaseUrl: process.env.VUE_APP_API_BASE_URL || 'https://api.wangcut.com',
    uploadMaxSize: 100 * 1024 * 1024, // 100MB
    supportedVideoFormats: ['mp4', 'mov', 'avi', 'mkv'],
    supportedImageFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    supportedAudioFormats: ['mp3', 'wav', 'aac', 'm4a'],
    maxVideoDuration: 300, // 5分钟
    maxImageCount: 50,
    enableAnalytics: true,
    enableCrashReporting: true,
    enablePerformanceMonitoring: true
  })
  
  // Toast消息
  const toastQueue = ref([])
  const currentToast = ref(null)
  
  // 全局错误
  const errors = ref([])
  
  // 计算属性
  const isDark = computed(() => theme.value === 'dark')
  const isIOS = computed(() => systemInfo.value.platform === 'ios')
  const isAndroid = computed(() => systemInfo.value.platform === 'android')
  const isH5 = computed(() => systemInfo.value.platform === 'h5')
  const safeAreaInsets = computed(() => systemInfo.value.safeAreaInsets || {})
  
  // 初始化应用
  const initApp = async () => {
    try {
      loading.value = true
      
      // 获取系统信息
      const sysInfo = uni.getSystemInfoSync()
      systemInfo.value = sysInfo
      
      // 获取网络状态
      const networkInfo = uni.getNetworkType()
      networkType.value = networkInfo.networkType
      
      // 监听网络状态变化
      uni.onNetworkStatusChange((res) => {
        isOnline.value = res.isConnected
        networkType.value = res.networkType
      })
      
      // 从存储恢复设置
      const savedTheme = uni.getStorageSync('theme')
      if (savedTheme) {
        theme.value = savedTheme
      }
      
      const savedLanguage = uni.getStorageSync('language')
      if (savedLanguage) {
        language.value = savedLanguage
      }
      
      // 设置状态栏
      updateStatusBar()
      
    } catch (error) {
      console.error('Init app error:', error)
      showToast('应用初始化失败', 'error')
    } finally {
      loading.value = false
    }
  }
  
  // 设置主题
  const setTheme = (newTheme) => {
    theme.value = newTheme
    uni.setStorageSync('theme', newTheme)
    updateStatusBar()
  }
  
  // 设置语言
  const setLanguage = (newLanguage) => {
    language.value = newLanguage
    uni.setStorageSync('language', newLanguage)
  }
  
  // 更新状态栏
  const updateStatusBar = () => {
    const statusBarStyle = isDark.value ? 'light' : 'dark'
    
    // #ifdef APP-PLUS
    plus.navigator.setStatusBarStyle(statusBarStyle)
    // #endif
    
    // #ifdef MP-WEIXIN
    wx.setNavigationBarColor({
      frontColor: statusBarStyle === 'light' ? '#ffffff' : '#000000',
      backgroundColor: isDark.value ? '#000000' : '#ffffff'
    })
    // #endif
  }
  
  // 显示Toast
  const showToast = (message, type = 'info', duration = 3000) => {
    const toast = {
      id: Date.now(),
      message,
      type,
      duration,
      timestamp: new Date()
    }
    
    toastQueue.value.push(toast)
    
    if (!currentToast.value) {
      showNextToast()
    }
  }
  
  // 显示下一个Toast
  const showNextToast = () => {
    if (toastQueue.value.length === 0) {
      currentToast.value = null
      return
    }
    
    currentToast.value = toastQueue.value.shift()
    
    setTimeout(() => {
      currentToast.value = null
      showNextToast()
    }, currentToast.value.duration)
  }
  
  // 隐藏Toast
  const hideToast = () => {
    currentToast.value = null
    toastQueue.value = []
  }
  
  // 显示Loading
  const showLoading = (message = '加载中...') => {
    loading.value = true
    uni.showLoading({
      title: message,
      mask: true
    })
  }
  
  // 隐藏Loading
  const hideLoading = () => {
    loading.value = false
    uni.hideLoading()
  }
  
  // 记录错误
  const logError = (error, context = {}) => {
    const errorLog = {
      id: Date.now(),
      message: error.message || error,
      stack: error.stack,
      timestamp: new Date(),
      context,
      userAgent: systemInfo.value.userAgent,
      platform: systemInfo.value.platform,
      version: systemInfo.value.version
    }
    
    errors.value.push(errorLog)
    
    // 只保留最近的100条错误
    if (errors.value.length > 100) {
      errors.value = errors.value.slice(-100)
    }
    
    // 上报错误（如果启用）
    if (config.value.enableCrashReporting) {
      reportError(errorLog)
    }
    
    console.error('Application Error:', errorLog)
  }
  
  // 上报错误
  const reportError = async (errorLog) => {
    try {
      // 这里应该调用错误上报API
      await fetch(`${config.value.apiBaseUrl}/api/errors`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorLog)
      })
    } catch (error) {
      console.error('Report error failed:', error)
    }
  }
  
  // 清除错误
  const clearErrors = () => {
    errors.value = []
  }
  
  // 页面导航
  const navigateTo = (url, options = {}) => {
    pageStack.value.push(currentPage.value)
    currentPage.value = url
    
    return uni.navigateTo({
      url,
      ...options
    })
  }
  
  const navigateBack = (delta = 1) => {
    for (let i = 0; i < delta && pageStack.value.length > 0; i++) {
      pageStack.value.pop()
    }
    
    if (pageStack.value.length > 0) {
      currentPage.value = pageStack.value[pageStack.value.length - 1]
    }
    
    return uni.navigateBack({ delta })
  }
  
  const redirectTo = (url, options = {}) => {
    currentPage.value = url
    
    return uni.redirectTo({
      url,
      ...options
    })
  }
  
  const switchTab = (url) => {
    currentPage.value = url
    pageStack.value = []
    
    return uni.switchTab({ url })
  }
  
  // 设置TabBar可见性
  const setTabBarVisible = (visible) => {
    tabBarVisible.value = visible
    
    if (visible) {
      uni.showTabBar()
    } else {
      uni.hideTabBar()
    }
  }
  
  // 检查更新
  const checkUpdate = async () => {
    try {
      // #ifdef APP-PLUS
      plus.runtime.getProperty(plus.runtime.appid, (info) => {
        // 检查版本更新逻辑
        console.log('App info:', info)
      })
      // #endif
      
      // #ifdef MP-WEIXIN
      const updateManager = wx.getUpdateManager()
      updateManager.onCheckForUpdate((res) => {
        console.log('Update check result:', res.hasUpdate)
      })
      // #endif
      
    } catch (error) {
      console.error('Check update error:', error)
    }
  }
  
  // 分享内容
  const shareContent = (content) => {
    return uni.share({
      provider: 'weixin',
      scene: 'WXSceneSession',
      type: 0,
      href: content.url,
      title: content.title,
      summary: content.summary,
      imageUrl: content.image
    })
  }
  
  return {
    // 状态
    loading,
    theme,
    language,
    systemInfo,
    networkType,
    isOnline,
    currentPage,
    pageStack,
    tabBarVisible,
    config,
    toastQueue,
    currentToast,
    errors,
    
    // 计算属性
    isDark,
    isIOS,
    isAndroid,
    isH5,
    safeAreaInsets,
    
    // 方法
    initApp,
    setTheme,
    setLanguage,
    updateStatusBar,
    showToast,
    showNextToast,
    hideToast,
    showLoading,
    hideLoading,
    logError,
    reportError,
    clearErrors,
    navigateTo,
    navigateBack,
    redirectTo,
    switchTab,
    setTabBarVisible,
    checkUpdate,
    shareContent
  }
}) 