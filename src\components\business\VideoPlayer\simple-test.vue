<template>
  <view class="simple-video-test">
    <view class="simple-video-test__header">
      <text class="simple-video-test__title">简单视频播放器测试</text>
    </view>
    
    <view class="simple-video-test__video-container">
      <video
        id="simpleVideo"
        class="simple-video-test__video"
        src="https://www.w3schools.com/html/mov_bbb.mp4"
        :controls="true"
        :show-center-play-btn="true"
        :show-play-btn="true"
        :show-fullscreen-btn="true"
        :show-progress="true"
        :enable-progress-gesture="true"
        object-fit="contain"
        @play="onPlay"
        @pause="onPause"
        @ended="onEnded"
        @timeupdate="onTimeUpdate"
        @loadedmetadata="onLoadedMetadata"
      ></video>
    </view>
    
    <view class="simple-video-test__controls">
      <view class="simple-video-test__btn" @click="playVideo">
        <text class="simple-video-test__btn-text">播放</text>
      </view>
      <view class="simple-video-test__btn" @click="pauseVideo">
        <text class="simple-video-test__btn-text">暂停</text>
      </view>
      <view class="simple-video-test__btn" @click="seekVideo">
        <text class="simple-video-test__btn-text">跳转到10秒</text>
      </view>
    </view>
    
    <view class="simple-video-test__info">
      <text class="simple-video-test__info-text">当前时间: {{ currentTime }}</text>
      <text class="simple-video-test__info-text">总时长: {{ totalTime }}</text>
      <text class="simple-video-test__info-text">播放状态: {{ isPlaying ? '播放中' : '已暂停' }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const currentTime = ref('00:00')
const totalTime = ref('00:00')
const isPlaying = ref(false)

const playVideo = () => {
  const videoContext = uni.createVideoContext('simpleVideo')
  videoContext.play()
}

const pauseVideo = () => {
  const videoContext = uni.createVideoContext('simpleVideo')
  videoContext.pause()
}

const seekVideo = () => {
  const videoContext = uni.createVideoContext('simpleVideo')
  videoContext.seek(10)
}

const onPlay = () => {
  console.log('视频开始播放')
  isPlaying.value = true
}

const onPause = () => {
  console.log('视频暂停')
  isPlaying.value = false
}

const onEnded = () => {
  console.log('视频播放结束')
  isPlaying.value = false
}

const onTimeUpdate = (e) => {
  console.log('时间更新:', e.detail)
  const current = e.detail.currentTime || 0
  const total = e.detail.duration || 0
  
  currentTime.value = formatTime(current)
  totalTime.value = formatTime(total)
}

const onLoadedMetadata = (e) => {
  console.log('视频元数据加载完成:', e.detail)
  const total = e.detail.duration || 0
  totalTime.value = formatTime(total)
}

const formatTime = (seconds) => {
  if (typeof seconds === 'string') {
    return seconds
  }
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}
</script>

<style lang="scss" scoped>
.simple-video-test {
  padding: 24rpx;
  background: #f5f5f5;
  min-height: 100vh;

  &__header {
    margin-bottom: 32rpx;
  }

  &__title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }

  &__video-container {
    margin-bottom: 32rpx;
    background: #000;
    border-radius: 12rpx;
    overflow: hidden;
  }

  &__video {
    width: 100%;
    height: 400rpx;
  }

  &__controls {
    display: flex;
    gap: 16rpx;
    margin-bottom: 32rpx;
  }

  &__btn {
    flex: 1;
    background: #007aff;
    border-radius: 8rpx;
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__btn-text {
    color: #fff;
    font-size: 28rpx;
  }

  &__info {
    background: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
  }

  &__info-text {
    display: block;
    font-size: 26rpx;
    color: #666;
    margin-bottom: 12rpx;
  }
}
</style>