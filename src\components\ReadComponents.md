# 组件库总览

## 📚 组件库说明

本组件库为旺剪App uni-app项目提供了一套完整的UI组件体系，包含通用组件、业务组件和布局组件三大类。所有组件都遵循统一的设计规范和开发标准，支持TypeScript、国际化、主题切换等特性。

## 📊 组件统计

- **总组件数量**: 19个
- **通用组件**: 3个
- **业务组件**: 13个  
- **布局组件**: 2个
- **空目录组件**: 5个（待开发）

## 🗂️ 组件分类

### 🎯 通用组件 (Common Components)

通用组件是基础UI组件，可在整个应用中重复使用，提供基础的交互功能。

| 组件名 | 状态 | 说明 | 文档链接 |
|--------|------|------|----------|
| **CustomNavBar** | ✅ 已完成 | 自定义导航栏组件 | [查看文档](./common/CustomNavBar/README.md) |
| **PrimaryButton** | ✅ 已完成 | 主要按钮组件 | [查看文档](./common/PrimaryButton/README.md) |
| **CollapsePanel** | ✅ 已完成 | 折叠面板组件 | [查看文档](./common/CollapsePanel/README.md) |

### 💼 业务组件 (Business Components)

业务组件是针对特定业务场景开发的组件，包含具体的业务逻辑和交互。

| 组件名 | 状态 | 说明 | 文档链接 |
|--------|------|------|----------|
| **Dialog** | ✅ 已完成 | 对话框组件 | [查看文档](./business/Dialog/README.md) |
| **BottomTabBar** | ✅ 已完成 | 底部标签栏组件 | [查看文档](./business/BottomTabBar/README.md) |
| **FunctionGrid** | ✅ 已完成 | 功能网格组件 | [查看文档](./business/FunctionGrid/README.md) |
| **FunctionCard** | ✅ 已完成 | 功能卡片组件 | [查看文档](./business/FunctionCard/README.md) |
| **Banner** | ✅ 已完成 | 轮播横幅组件 | [查看文档](./business/Banner/README.md) |
| **PageHeader** | ✅ 已完成 | 页面头部组件 | [查看文档](./business/PageHeader/README.md) |
| **HomeBanner** | ✅ 已完成 | 首页轮播组件 | [查看文档](./business/HomeBanner/README.md) |
| **HomeMoreFunctions** | ✅ 已完成 | 首页更多功能组件 | [查看文档](./business/HomeMoreFunctions/README.md) |
| **VideoPlayer** | ✅ 已完成 | 视频播放器组件 | [查看文档](./business/VideoPlayer/README.md) |
| **DownloadProgress** | ✅ 已完成 | 下载进度组件 | [查看文档](./business/DownloadProgress/README.md) |
| **VoiceSelector** | ⏳ 待开发 | 语音选择器组件 | - |
| **VideoUploader** | ⏳ 待开发 | 视频上传组件 | - |
| **CopywritingGenerator** | ⏳ 待开发 | 文案生成器组件 | - |
| **CopywritingEditor** | ⏳ 待开发 | 文案编辑器组件 | - |
| **BgmSelector** | ⏳ 待开发 | 背景音乐选择器组件 | - |
| **PopupDialog** | ⏳ 待开发 | 弹窗对话框组件 | - |

### 🏗️ 布局组件 (Layout Components)

布局组件用于构建页面的整体结构，提供页面框架和导航功能。

| 组件名 | 状态 | 说明 | 文档链接 |
|--------|------|------|----------|
| **Header** | ✅ 已完成 | 布局头部组件 | [查看文档](./layout/Header/README.md) |
| **TabBar** | ✅ 已完成 | 布局标签栏组件 | [查看文档](./layout/TabBar/README.md) |

## 🚀 快速开始

### 安装使用

```vue
<template>
  <view>
    <!-- 使用通用组件 -->
    <PrimaryButton text="点击我" @click="handleClick" />
    
    <!-- 使用业务组件 -->
    <FunctionGrid :function-list="functionList" />
    
    <!-- 使用布局组件 -->
    <Header title="页面标题" :show-back="true" />
  </view>
</template>

<script setup>
import PrimaryButton from '@/components/common/PrimaryButton/index.vue'
import FunctionGrid from '@/components/business/FunctionGrid/index.vue'
import Header from '@/components/layout/Header/index.vue'

const functionList = [
  { title: '功能1', icon: '🎯', link: '/pages/feature1/index' },
  { title: '功能2', icon: '🚀', link: '/pages/feature2/index' }
]

const handleClick = () => {
  console.log('按钮被点击')
}
</script>
```

## 📋 组件开发规范

### 文件结构

每个组件应包含以下文件：

```
ComponentName/
├── index.vue          # 组件主文件
├── README.md          # 组件文档
├── index.js           # 导出文件（可选）
└── demo/              # 演示文件（可选）
```

### 命名规范

- **组件名**: 使用大驼峰命名法 (PascalCase)
- **文件名**: 使用 kebab-case
- **CSS类名**: 使用 BEM 命名规范

### 代码规范

- 使用 Vue 3 Composition API
- 支持 TypeScript
- 使用 SCSS 编写样式
- 遵循 ESLint 和 Prettier 规范

## 🎨 设计系统

### 颜色规范

- **主色调**: `#4A90E2`
- **成功色**: `#52C41A`
- **警告色**: `#FAAD14`
- **错误色**: `#FF4D4F`
- **信息色**: `#1890FF`

### 尺寸规范

- **间距**: 8rpx, 16rpx, 24rpx, 32rpx, 40rpx
- **圆角**: 4rpx, 8rpx, 12rpx, 16rpx, 24rpx
- **字体**: 12rpx, 14rpx, 16rpx, 18rpx, 20rpx, 24rpx, 32rpx

### 动画规范

- **快速**: 0.15s
- **正常**: 0.3s
- **缓慢**: 0.5s

## 🔧 开发工具

### 演示页面

开发环境下的演示页面位于 `src/components/demo/` 目录，包含所有组件的使用示例。

### 测试

- **单元测试**: 使用 Jest 进行单元测试
- **E2E测试**: 使用 Playwright 进行端到端测试
- **覆盖率**: 要求测试覆盖率不低于 80%

### 构建

```bash
# 开发环境
npm run dev

# 生产构建
npm run build

# 运行测试
npm run test

# 生成文档
npm run docs
```

## 📖 文档说明

### 文档结构

每个组件的 README.md 文档包含：

1. **组件描述** - 组件的基本介绍
2. **功能特性** - 主要功能点
3. **Props 参数** - 属性说明
4. **Events 事件** - 事件说明
5. **Slots 插槽** - 插槽说明
6. **使用示例** - 代码示例
7. **样式定制** - 样式说明
8. **注意事项** - 使用注意点
9. **最佳实践** - 使用建议

### 示例代码

所有示例代码都经过测试，可以直接运行。示例包含：

- 基础用法
- 高级用法
- 自定义样式
- 状态管理
- 事件处理

## 🤝 贡献指南

### 开发流程

1. **创建分支**: 从 `main` 分支创建功能分支
2. **开发组件**: 按照规范开发组件
3. **编写文档**: 完善组件文档和示例
4. **编写测试**: 添加单元测试和集成测试
5. **提交代码**: 提交代码并创建 Pull Request
6. **代码审查**: 通过代码审查后合并

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
feat: 添加新组件
fix: 修复组件bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建工具调整
```

## 📞 技术支持

### 问题反馈

- **GitHub Issues**: [提交 Issue](https://github.com/your-repo/issues)
- **文档问题**: 直接在文档中提交 PR
- **功能建议**: 通过 Issue 或 Discussion 提出

### 联系方式

- **邮箱**: <EMAIL>
- **微信群**: 扫描二维码加入
- **QQ群**: 123456789

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

---

**最后更新**: 2024年12月
**版本**: v1.0.0
**维护者**: 旺剪App开发团队 