// 旺剪App自动化测试环境设置
// 全局变量设置
global.program = program
global.expect = require('expect')

// 测试超时配置
jest.setTimeout(30000)
// 测试前钩子
beforeAll(async () => {
  console.log('🚀 开始旺剪App自动化测试')
  
  // 等待应用启动
  await program.launch()
  
  // 等待首页加载
  await program.navigateTo('/pages/index/index')
  await program.waitFor(2000)
  
  console.log('✅ 测试环境初始化完成')
})

// 测试后钩子
afterAll(async () => {
  console.log('🏁 测试执行完成，清理测试环境')
  
  // 清理测试数据
  await program.evaluate(() => {
    // 清理本地存储
    if (typeof uni !== 'undefined' && uni.clearStorageSync) {
      uni.clearStorageSync()
    }
  })
  
  console.log('✅ 测试环境清理完成')
})

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason)
})

// 测试工具函数
global.testUtils = {
  // 等待元素出现
  waitForElement: async (selector, timeout = 5000) => {
    const page = await program.currentPage()
    return await page.waitForSelector(selector, { timeout })
  },
  
  // 等待元素消失
  waitForElementToDisappear: async (selector, timeout = 5000) => {
    const page = await program.currentPage()
    return await page.waitForSelector(selector, { hidden: true, timeout })
  },
  
  // 截图
  takeScreenshot: async (name) => {
    const page = await program.currentPage()
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `screenshot-${name}-${timestamp}.png`
    return await page.screenshot({ path: `./test-results/screenshots/${filename}` })
  },
  
  // 等待Toast消失
  waitForToast: async (timeout = 3000) => {
    await program.waitFor(timeout)
  },
  
  // 模拟网络延迟
  mockNetworkDelay: (delay = 1000) => {
    return new Promise(resolve => setTimeout(resolve, delay))
  }
} 