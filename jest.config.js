module.exports = {
	globalTeardown: '@dcloudio/uni-automator/dist/teardown.js',
	testEnvironment: '@dcloudio/uni-automator/dist/environment.js',
    testEnvironmentOptions: {
        compile: true,
		h5: {
			options: {
				headless: false
			}
		},
    },
    testTimeout: 10000,
    reporters: [
        'default',
        ['jest-html-reporters', {
            publicPath: './test-results/reports',
            filename: 'test-report.html',
            pageTitle: '旺剪App视频剪辑功能测试报告',
            expand: true,
            openReport: true,
            testPathIgnorePatterns: ['<rootDir>/node_modules/'], // 忽略的测试文件路径模式
        }]
    ],
    watchPathIgnorePatterns: ['/node_modules/', '/dist/', '/.git/'],
    moduleFileExtensions: ['js', 'json', 'vue'],
    rootDir: __dirname,
    testMatch: [
		// "<rootDir>/src/pages/tests/**/*.test.js",
		// "<rootDir>/src/tests/unit/**/*.test.js",
		//     "<rootDir>/src/tests/**/*.e2e.js", 
		"<rootDir>/src/tests/scripts/run-tests.js",
    ],
    testPathIgnorePatterns: ['/node_modules/'],
	moduleNameMapper: {
		'^@/(.*)$': '<rootDir>/src/$1'
	}
}