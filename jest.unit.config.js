module.exports = {
  testMatch: [
    "<rootDir>/src/tests/unit/**/*.test.js",
    "<rootDir>/src/tests/unit/**/*.spec.js"
  ],
  // testEnvironment: "jest-environment-jsdom",
  testEnvironment: 'node',
  moduleFileExtensions: ['js', 'json', 'vue'],
  rootDir: __dirname,
  testPathIgnorePatterns: ['/node_modules/'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './test-results/reports',
      filename: 'test-report-unit.html',
      pageTitle: '旺剪App视频剪辑功能测试报告',
      expand: true,
      openReport: true,
      testPathIgnorePatterns: ['<rootDir>/node_modules/'], // 忽略的测试文件路径模式
    }]
  ],
}; 