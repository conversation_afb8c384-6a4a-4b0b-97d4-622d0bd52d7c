/**
 * 接口服务使用示例
 */

import { userService, videoService, materialService, systemService } from './index'
import { showLoading, hideLoading, showToast } from '@/utils/tools'

// 用户相关示例
export const userExamples = {
  // 用户登录示例
  async login(phone, password) {
    try {
      showLoading('登录中...')
      const result = await userService.login({ phone, password })
      
      // 登录成功，保存token和用户信息
      uni.setStorageSync('token', result.token)
      uni.setStorageSync('userInfo', result.userInfo)
      
      showToast('登录成功', 'success')
      return result
    } catch (error) {
      showToast(error.message || '登录失败')
      throw error
    } finally {
      hideLoading()
    }
  },
  
  // 获取用户信息示例
  async getUserInfo() {
    try {
      const userInfo = await userService.getUserInfo()
      // 更新本地存储的用户信息
      uni.setStorageSync('userInfo', userInfo)
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  },
  
  // 上传头像示例
  async uploadAvatar() {
    try {
      // 选择图片
      const [tempFilePath] = await uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      })
      
      showLoading('上传中...')
      const result = await userService.uploadAvatar(tempFilePath)
      
      showToast('头像上传成功', 'success')
      return result
    } catch (error) {
      showToast(error.message || '头像上传失败')
      throw error
    } finally {
      hideLoading()
    }
  }
}

// 视频相关示例
export const videoExamples = {
  // 获取视频列表示例
  async getVideoList(page = 1, pageSize = 10) {
    try {
      const params = {
        page,
        pageSize,
        sortBy: 'createTime',
        sortOrder: 'desc'
      }
      
      const result = await videoService.getVideoList(params)
      return result
    } catch (error) {
      console.error('获取视频列表失败:', error)
      throw error
    }
  },
  
  // 创建视频项目示例
  async createVideoProject(projectData) {
    try {
      showLoading('创建项目中...')
      
      const result = await videoService.createVideoProject({
        name: projectData.name,
        description: projectData.description,
        templateId: projectData.templateId,
        materials: projectData.materials || []
      })
      
      showToast('项目创建成功', 'success')
      return result
    } catch (error) {
      showToast(error.message || '项目创建失败')
      throw error
    } finally {
      hideLoading()
    }
  },
  
  // AI视频生成示例
  async generateAIVideo(prompt, options = {}) {
    try {
      showLoading('AI生成中...')
      
      const taskResult = await videoService.generateAIVideo({
        prompt,
        duration: options.duration || 30,
        style: options.style || 'default',
        quality: options.quality || 'high'
      })
      
      // 轮询任务状态
      const finalResult = await this.pollTaskStatus(
        taskResult.taskId,
        (taskId) => videoService.getAITaskStatus(taskId)
      )
      
      showToast('AI视频生成成功', 'success')
      return finalResult
    } catch (error) {
      showToast(error.message || 'AI视频生成失败')
      throw error
    } finally {
      hideLoading()
    }
  },
  
  // 轮询任务状态
  async pollTaskStatus(taskId, getStatusFn, maxAttempts = 60, interval = 2000) {
    let attempts = 0
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++
          const status = await getStatusFn(taskId)
          
          if (status.status === 'completed') {
            resolve(status.result)
          } else if (status.status === 'failed') {
            reject(new Error(status.error || '任务执行失败'))
          } else if (attempts >= maxAttempts) {
            reject(new Error('任务超时'))
          } else {
            // 继续轮询
            setTimeout(poll, interval)
          }
        } catch (error) {
          reject(error)
        }
      }
      
      poll()
    })
  }
}

// 素材相关示例
export const materialExamples = {
  // 获取素材列表示例
  async getMaterialList(type = 'all', page = 1, pageSize = 20) {
    try {
      const params = {
        type,
        page,
        pageSize,
        sortBy: 'createTime',
        sortOrder: 'desc'
      }
      
      const result = await materialService.getMaterialList(params)
      return result
    } catch (error) {
      console.error('获取素材列表失败:', error)
      throw error
    }
  },
  
  // 上传素材示例
  async uploadMaterial(filePath, materialInfo) {
    try {
      showLoading('上传素材中...')
      
      const formData = {
        name: materialInfo.name,
        description: materialInfo.description,
        category: materialInfo.category,
        tags: JSON.stringify(materialInfo.tags || [])
      }
      
      const result = await materialService.uploadMaterial(filePath, formData)
      
      showToast('素材上传成功', 'success')
      return result
    } catch (error) {
      showToast(error.message || '素材上传失败')
      throw error
    } finally {
      hideLoading()
    }
  },
  
  // 批量下载素材示例
  async batchDownloadMaterials(materialIds) {
    try {
      showLoading('准备下载...')
      
      const result = await materialService.batchDownloadMaterials(materialIds)
      
      // 开始下载
      const downloadTask = uni.downloadFile({
        url: result.downloadUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            showToast('下载完成', 'success')
            // 可以在这里处理下载完成后的逻辑
          }
        },
        fail: (error) => {
          showToast('下载失败')
          console.error('下载失败:', error)
        }
      })
      
      return downloadTask
    } catch (error) {
      showToast(error.message || '下载准备失败')
      throw error
    } finally {
      hideLoading()
    }
  }
}

// 系统相关示例
export const systemExamples = {
  // 检查应用更新示例
  async checkAppUpdate() {
    try {
      const updateInfo = await systemService.checkAppUpdate()
      
      if (updateInfo.hasUpdate) {
        // 显示更新提示
        const confirmUpdate = await uni.showModal({
          title: '发现新版本',
          content: `版本 ${updateInfo.version} 已发布\n${updateInfo.description}`,
          confirmText: '立即更新',
          cancelText: '稍后提醒'
        })
        
        if (confirmUpdate.confirm) {
          // 跳转到更新页面或下载链接
          uni.navigateTo({
            url: `/pages/update/index?downloadUrl=${encodeURIComponent(updateInfo.downloadUrl)}`
          })
        }
      } else {
        showToast('已是最新版本')
      }
      
      return updateInfo
    } catch (error) {
      console.error('检查更新失败:', error)
      throw error
    }
  },
  
  // 提交反馈示例
  async submitFeedback(feedbackData) {
    try {
      showLoading('提交中...')
      
      const result = await systemService.submitFeedback({
        type: feedbackData.type,
        title: feedbackData.title,
        content: feedbackData.content,
        contact: feedbackData.contact,
        deviceInfo: uni.getSystemInfoSync()
      })
      
      showToast('反馈提交成功', 'success')
      return result
    } catch (error) {
      showToast(error.message || '反馈提交失败')
      throw error
    } finally {
      hideLoading()
    }
  },
  
  // 获取系统配置示例
  async initSystemConfig() {
    try {
      const config = await systemService.getSystemConfig()
      
      // 保存配置到本地存储
      uni.setStorageSync('systemConfig', config)
      
      // 应用配置
      if (config.theme) {
        // 设置主题
        this.applyTheme(config.theme)
      }
      
      if (config.language) {
        // 设置语言
        this.setLanguage(config.language)
      }
      
      return config
    } catch (error) {
      console.error('获取系统配置失败:', error)
      // 使用默认配置
      const defaultConfig = {
        theme: 'light',
        language: 'zh-CN'
      }
      uni.setStorageSync('systemConfig', defaultConfig)
      return defaultConfig
    }
  },
  
  // 应用主题
  applyTheme(theme) {
    // 这里可以实现主题切换逻辑
    console.log('应用主题:', theme)
  },
  
  // 设置语言
  setLanguage(language) {
    // 这里可以实现语言切换逻辑
    console.log('设置语言:', language)
  }
}

// 综合使用示例
export const combinedExamples = {
  // 完整的视频创建流程示例
  async createVideoWithMaterials(projectName, materialIds) {
    try {
      showLoading('准备创建视频...')
      
      // 1. 获取素材详情
      const materials = await Promise.all(
        materialIds.map(id => materialService.getMaterialDetail(id))
      )
      
      // 2. 创建视频项目
      const project = await videoService.createVideoProject({
        name: projectName,
        materials: materials.map(m => ({
          id: m.id,
          type: m.type,
          url: m.url,
          duration: m.duration
        }))
      })
      
      // 3. 开始视频编辑
      const editResult = await videoService.editVideo({
        projectId: project.id,
        timeline: this.generateTimeline(materials),
        effects: [],
        transitions: []
      })
      
      // 4. 导出视频
      const exportTask = await videoService.exportVideo({
        projectId: project.id,
        quality: 'high',
        format: 'mp4'
      })
      
      // 5. 等待导出完成
      const finalVideo = await videoExamples.pollTaskStatus(
        exportTask.taskId,
        (taskId) => videoService.getExportStatus(taskId)
      )
      
      showToast('视频创建成功', 'success')
      return finalVideo
    } catch (error) {
      showToast(error.message || '视频创建失败')
      throw error
    } finally {
      hideLoading()
    }
  },
  
  // 生成时间轴
  generateTimeline(materials) {
    let currentTime = 0
    return materials.map(material => {
      const timelineItem = {
        materialId: material.id,
        startTime: currentTime,
        endTime: currentTime + material.duration,
        track: material.type === 'video' ? 0 : 1
      }
      currentTime += material.duration
      return timelineItem
    })
  }
}

// 错误处理示例
export const errorHandlingExamples = {
  // 带重试的请求示例
  async requestWithRetry(requestFn, maxRetries = 3, delay = 1000) {
    let lastError
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error
        
        if (i < maxRetries - 1) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
        }
      }
    }
    
    throw lastError
  },
  
  // 网络状态检查示例
  async checkNetworkAndRequest(requestFn) {
    return new Promise((resolve, reject) => {
      uni.getNetworkType({
        success: async (res) => {
          if (res.networkType === 'none') {
            showToast('网络连接不可用')
            reject(new Error('网络连接不可用'))
            return
          }
          
          try {
            const result = await requestFn()
            resolve(result)
          } catch (error) {
            reject(error)
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }
}

export default {
  userExamples,
  videoExamples,
  materialExamples,
  systemExamples,
  combinedExamples,
  errorHandlingExamples
} 