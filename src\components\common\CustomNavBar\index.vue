<template>
  <view class="custom-navbar">
    <view class="status-bar"></view>
    <view class="nav-bar-content">
      <slot />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'

defineProps({
  title: {
    type: String,
    default: ''
  },
  showBack: {
    type: Boolean,
    default: false
  },
  background: {
    type: String,
    default: '' // 不设置默认背景色
  },
  color: {
    type: String,
    default: '#222'
  },
  rightText: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
.custom-navbar {
  width: 100%;
  /* 不设置背景色，让页面背景可穿透 */
}
.status-bar {
  height: var(--status-bar-height);
  width: 100%;
  /* 不设置背景色 */
}
.nav-bar-content {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16rpx;
  width: 100%;
  /* 不设置背景色，内容悬浮 */
}
</style>
