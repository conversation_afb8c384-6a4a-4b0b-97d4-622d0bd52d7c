<script>
import { useUserStore } from '@/store/modules/user'
import { getCid, getUserId } from '@/utils/http'

export default {
  onLaunch: async function () {
    console.log('App Launch')
    const userStore = useUserStore()
    // 先清理所有用户信息（账号密码除外）
    await userStore.logout()
    // 读取本地账号密码
    const account = uni.getStorageSync('login_account')
    const password = uni.getStorageSync('login_password')
    if (account && password) {
      // 自动登录
      const result = await userStore.login({ account, password, isEncrypted: true })
      if (result.success) {
        // 登录成功，进入首页
        uni.reLaunch({ url: '/pages/index/index' })
        return
      }
    }
    // 其他情况全部进入登录页
    uni.reLaunch({ url: '/pages/login/index' })
  },
  
  onShow: function () {
    console.log('App Show')
  },
  
  onHide: function () {
    console.log('App Hide')
  },
  
  methods: {
    // 检查登录状态
    async checkLoginStatus() {
      try {
        const userStore = useUserStore()
        
        // 首先从本地存储恢复用户状态
        userStore.restoreUserState()
        
        // 获取本地存储的 CID 和 UID
        const cid = getCid()
        const userId = getUserId()
        
        console.log('检查登录状态:', {
          isLogin: userStore.isLogin,
          hasCid: !!cid,
          hasUserId: !!userId,
          currentPage: this.getCurrentPage()
        })
        
        // 如果已登录且有 CID 和 UID，则初始化用户数据
        if (userStore.isLogin && cid && userId) {
          console.log('用户已登录，开始初始化数据...')
          await userStore.initAfterLogin()
          return
        }
        
        // 如果未登录或缺少 CID/UID，且不在登录页面，则跳转到登录页面
        if ((!userStore.isLogin || !cid || !userId) && !this.isLoginPage()) {
          console.log('用户未登录或缺少必要信息，跳转到登录页面')
          this.redirectToLogin()
        }
      } catch (error) {
        console.error('检查登录状态失败:', error)
        // 出错时也跳转到登录页面
        if (!this.isLoginPage()) {
          this.redirectToLogin()
        }
      }
    },
    
    // 获取当前页面路径
    getCurrentPage() {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        return currentPage.route
      }
      return ''
    },
    
    // 判断是否在登录页面
    isLoginPage() {
      const currentPage = this.getCurrentPage()
      return currentPage === 'pages/login/index'
    },
    
    // 跳转到登录页面
    redirectToLogin() {
      try {
        // 使用 reLaunch 确保清除页面栈，避免用户通过返回键回到其他页面
        uni.reLaunch({
          url: '/pages/login/index',
          success: () => {
            console.log('成功跳转到登录页面')
          },
          fail: (error) => {
            console.error('跳转到登录页面失败:', error)
            // 如果 reLaunch 失败，尝试使用 navigateTo
            uni.navigateTo({
              url: '/pages/login/index'
            })
          }
        })
      } catch (error) {
        console.error('跳转登录页面异常:', error)
      }
    }
  }
}
</script>

<style>
/*每个页面公共css */
</style>
