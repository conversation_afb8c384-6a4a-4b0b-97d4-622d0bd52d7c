<template>
	<view class="native-gallery__nav-bar" :style="{ paddingTop: statusBarHeight + 'px' }">
		<view class="native-gallery__nav-bar-content">
			<view class="native-gallery__nav-bar-left" @click="onBack">
				<text class="native-gallery__nav-bar-back-icon">×</text>
			</view>
			<!-- 标签栏 -->
			<view class="tab-container">
				<view 
					class="tab-item" 
					:class="{ 'active': activeTab === 'local' }" 
					@click="switchTab('local')"
				>
					<text class="tab-text" :class="{ 'active-text': activeTab === 'local' }">本地素材</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ 'active': activeTab === 'cloud' }" 
					@click="switchTab('cloud')"
				>
					<text class="tab-text" :class="{ 'active-text': activeTab === 'cloud' }">云素材</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ 'active': activeTab === 'library' }" 
					@click="switchTab('library')"
				>
					<text class="tab-text" :class="{ 'active-text': activeTab === 'library' }">素材库</text>
				</view>
			</view>
		</view>
	</view>
	<view class="gallery-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
		<!-- 本地素材选择器组件 -->
		<view v-if="activeTab === 'local'" class="content-container">
			<!-- 相册选择器组件 - 性能点：动态计算高度，避免固定高度导致的布局问题 -->
			<!-- 兼容点：nvue中flex布局默认direction为column，无需显式设置 -->
			<qinsilk-gallery 
				ref="gallery" 
				tab="all" 
				class="gallery-selector"
				:style="{ height: galleryHeight + 'px' }"
				@on-select="onGallerySelect"
			></qinsilk-gallery>

			<!-- 新增：选择图片并上传按钮 -->
			<!--
			<view class="upload-btn-area">
				<button class="upload-btn" @click="chooseAndUploadImage">选择图片并上传</button>
			</view>
			-->
			
			<!-- 底部选择确认区域 - 性能点：使用 scroll-view 实现横向滚动，避免 list 嵌套 -->
			<view class="bottom-selection-area" :style="{ height: bottomAreaHeight + 'px' }">
				<!-- 选中的媒体项列表 -->
				<scroll-view 
					class="selected-items-scroll" 
					:style="{ height: selectedItemsScrollHeight + 'px' }"
					scroll-x="true" 
					show-scrollbar="false"
				>
					<view class="selected-items-container">
						<view 
							v-for="(item, index) in selectedItems" 
							:key="item.id" 
							class="selected-item"
						>
							<!-- 图片圆角通过包装容器实现 -->
							<view class="selected-item-image-wrapper">
								<image :src="item.path" class="selected-item-image" mode="aspectFill" />
							</view>
							<!-- 时长显示 -->
							<view class="selected-item-duration" v-if="item.duration && item.duration > 0">
								<text class="duration-text">{{ item.durationStr || formatDuration(item.duration) }}</text>
							</view>
							<!-- 删除按钮 -->
							<view class="selected-item-remove" @click="removeSelectedItem(index)">
								<text class="remove-icon">×</text>
							</view>
						</view>
					</view>
				</scroll-view>
				
				<!-- 底部操作区域 -->
				<view class="bottom-actions">
					<view class="selection-tip" v-if="selectedItems.length === 0">
						<text class="selection-tip-text">请选择至少一个图片或者视频</text>
					</view>
					<view class="selection-tip" v-else>
						<text class="selection-tip-text">已选择 {{ selectedItems.length }} 个文件</text>
					</view>
					<view class="confirm-btn" @click="confirmSelection">
						<text class="confirm-btn-text">选好了</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 素材库内容 -->
		<MaterialLibrary 
			v-if="activeTab === 'library'" 
			@confirm="onMaterialLibraryConfirm"
		/>
		
		<!-- 云素材内容 -->
		<view v-if="activeTab === 'cloud'" class="content-container">
			<view class="empty-state">
				<text class="empty-text">云素材功能即将上线</text>
			</view>
		</view>
	</view>
</template>

<script>
import MaterialLibrary from '@/components/MaterialLibrary.nvue'
import { getVodUploadCredentials } from '@/service/vod'
import { uploadToAliyunVOD } from '@/utils/vodUpload'
import { 
	getSelectedNativeMaterials, 
	clearSelectedNativeMaterials, 
	formatDuration, 
	getFileNameFromPath, 
	convertNativeData 
} from '@/utils/nativeGallery'

export default {
	name: 'NativeGallerySelector',
	components: {
		MaterialLibrary
	},
	data() {
		return {
			activeTab: 'local', // 当前激活的标签: 'local', 'cloud', 'library'
			statusBarHeight: 0, // 状态栏高度
			screenHeight: 0, // 屏幕高度
			selectedImagePath: '',
			selectedImageInfo: {},
			selectedItems: [],
			// 布局相关常量
			NAV_BAR_HEIGHT: 44, // 导航栏高度
			BOTTOM_ACTIONS_HEIGHT: 60, // 底部操作区域高度
			SELECTED_ITEMS_HEIGHT: 120, // 选中项预览区域高度
			BOTTOM_PADDING: 20, // 底部内边距
			BOTTOM_AREA_PADDING: 16, // 底部区域内边距
		}
	},
	
	computed: {
		// 计算底部选择区域的总高度
		bottomAreaHeight() {
			if (this.selectedItems.length === 0) {
				// 无选中项时，只显示底部操作区域
				return this.BOTTOM_ACTIONS_HEIGHT + this.BOTTOM_AREA_PADDING * 2;
			} else {
				// 有选中项时，显示选中项预览区域 + 底部操作区域
				return this.SELECTED_ITEMS_HEIGHT + this.BOTTOM_ACTIONS_HEIGHT + this.BOTTOM_AREA_PADDING * 2;
			}
		},
		
		// 计算选中项滚动区域的高度
		selectedItemsScrollHeight() {
			return this.selectedItems.length > 0 ? this.SELECTED_ITEMS_HEIGHT : 0;
		},
		
		// 计算相册选择器组件的高度 - 性能点：动态计算避免布局闪烁
		galleryHeight() {
			// 可用高度 = 屏幕高度 - 状态栏 - 导航栏 - 底部选择区域高度
			const availableHeight = this.screenHeight - this.statusBarHeight - this.NAV_BAR_HEIGHT - this.bottomAreaHeight;
			// 控制台输出各个数值，便于调试布局
			console.log(
				`屏幕高度: ${this.screenHeight}, 状态栏高度: ${this.statusBarHeight}, 导航栏高度: ${this.NAV_BAR_HEIGHT}, 底部区域高度: ${this.bottomAreaHeight}, 可用高度: ${availableHeight}`
			);
			// 确保最小高度为300px，最大高度为屏幕高度的70%
			const minHeight = 300;
			const maxHeight = this.screenHeight;
			
			// 添加安全边距，避免内容被遮挡
			const safeHeight = Math.max(minHeight, Math.min(availableHeight - 10, maxHeight));
			
			return safeHeight;
		}
	},
	
	onLoad(options) {
		console.log('Native Gallery Selector page loaded', options);
		
		// 获取系统信息，适配刘海屏
		this.getSystemInfo();
		
		// 如果有传入tab参数，则切换到指定标签
		if (options && options.tab) {
			this.activeTab = options.tab;
		}
	},
	
	onReady() {
		console.log('Native Gallery Selector page ready');
	},
	
	onShow() {
		console.log('Native Gallery Selector page show');
		// 页面显示时，可以在这里处理一些数据恢复逻辑
		// 比如从本地存储恢复选中的素材等
	},
	
	// 监听选中项变化，优化布局更新
	watch: {
		selectedItems: {
			handler(newVal, oldVal) {
				// 当选中项数量变化时，触发布局重新计算
				console.log('选中项变化:', newVal.length, '->', oldVal.length);
				// 使用nextTick确保DOM更新后再执行
				this.$nextTick(() => {
					// 可以在这里添加额外的布局优化逻辑
				});
			},
			deep: true
		}
	},
	
	methods: {
		// 获取系统信息，适配刘海屏
		getSystemInfo() {
			uni.getSystemInfo({
				success: (res) => {
					console.log('系统信息:', res);
					this.statusBarHeight = res.statusBarHeight || 0;
					this.screenHeight = res.screenHeight || 667; // 默认iPhone 6/7/8高度
				},
				fail: (err) => {
					console.error('获取系统信息失败:', err);
					// 设置默认值
					this.statusBarHeight = 0;
					this.screenHeight = 667;
				}
			});
		},
		
		// 切换标签页
		switchTab(tab) {
			this.activeTab = tab;
		},
		
		// 本地素材相关方法
		onGallerySelect(e) {
			console.log("相册选择事件：", e);
			
			// 处理选择事件，e.detail.selected 是数组格式
			if (e.detail && e.detail.selected) {
				const selected = e.detail.selected;
				console.log("选中的媒体项：", selected);
				
				// 转换数据格式并更新选中项
				this.updateSelectedItems(selected);
			} else {
				// 如果没有选中项，清空选择
				this.selectedItems = [];
			}
		},
		
		// 更新选中项列表
		updateSelectedItems(nativeSelectedItems) {
			// 使用工具函数转换数据格式
			const convertedItems = convertNativeData(nativeSelectedItems);
			
			// 更新选中项列表
			this.selectedItems = convertedItems;
			
			console.log("更新后的选中项：", this.selectedItems);
			
			// 显示选择反馈
			if (this.selectedItems.length > 0) {
				uni.showToast({
					title: `已选择 ${this.selectedItems.length} 个文件`,
					icon: 'none',
					duration: 1500
				});
			}
		},
		
		clearSelection() {
			this.selectedImagePath = '';
			this.selectedImageInfo = null;
			this.selectedItems = [];
		},
		
		getSelectedImage() {
			return this.selectedImageInfo;
		},
		
		resetGallery() {
			this.clearSelection();
		},
		
		removeSelectedItem(index) {
			// 性能点：使用splice删除数组元素，触发响应式更新
			this.selectedItems.splice(index, 1);
			console.log('移除选中项:', index, '剩余数量:', this.selectedItems.length);
			
			// 如果删除后没有选中项，可以添加一些视觉反馈
			if (this.selectedItems.length === 0) {
				uni.showToast({
					title: '已清空选择',
					icon: 'none',
					duration: 1500
				});
			}
		},
		
		onBack() {
			uni.navigateBack();
		},
		
		confirmSelection() {
			if (this.selectedItems.length === 0) {
				uni.showToast({
					title: '请至少选择一个图片或视频',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			
			console.log('确认选择:', this.selectedItems);
			
			// 保存选中的素材到本地存储，以便返回页面可以获取
			uni.setStorageSync('selectedNativeMaterials', this.selectedItems);
			
			// 触发事件，将选中的素材传递给父组件
			this.$emit('confirm', this.selectedItems);
			
			uni.showToast({
				title: '选择完成',
				icon: 'success',
				duration: 1500
			});
			
			// 延迟返回，让用户看到成功提示
			setTimeout(() => {
				uni.navigateBack({
					delta: 1
				});
			}, 1500);
		},
		
		// 新增：选择图片并上传
		async chooseAndUploadImage() {
			try {
				const res = await uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera']
				})
				if (res.tempFiles && res.tempFiles.length > 0) {
					const fileObj = res.tempFiles[0]
					const fileName = fileObj.name
					const title = fileName.replace(/\.[^/.]+$/, '')
					// 获取上传凭证
					const cred = await getVodUploadCredentials({
						title,
						file_name: fileName
					})
					
					// 根据status_code进行兼容处理
					if (cred && cred.status_code === 1) {
						const data = cred.data || cred // 兼容后端返回结构
						// 兼容PC端H5和移动端H5
						let file = fileObj.file || fileObj;
						if (!(file instanceof File)) {
							uni.showToast({ title: '请选择本地图片(H5环境)', icon: 'none' });
							return;
						}
						uni.showLoading({ title: '上传中...' })
						await uploadToAliyunVOD({
							file,
							uploadAuth: data.upload_auth,
							uploadAddress: data.upload_address,
							onProgress: (percent) => {
								uni.showLoading({ title: `上传中 ${percent}%` })
							}
						})
						uni.hideLoading()
						uni.showToast({ title: '上传成功', icon: 'success' })
					} else {
						// 失败情况：弹出消息并结束流程
						uni.showToast({ title: cred?.message || '获取上传凭证失败', icon: 'none' })
						return
					}
				} else {
					uni.showToast({ title: '未选择图片', icon: 'none' })
				}
			} catch (err) {
				console.log(err.message)
				uni.hideLoading()
				uni.showToast({ title: err.message || '上传失败', icon: 'none' })
			}
		},
		
		// 素材库相关方法
		onMaterialLibraryConfirm(selectedItems) {
			console.log('素材库选择完成:', selectedItems);
			// 处理选择完成事件
			setTimeout(() => {
				// 返回上一页
				uni.navigateBack({
					delta: 1
				});
			});
		}
	}
}
</script>

<style scoped>
/* 顶部标签栏样式优化 */
.native-gallery__nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background-color: #1a1a1a;
	z-index: 100;
}

.native-gallery__nav-bar-content {
	height: 44px;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding: 0 16px;
}

.native-gallery__nav-bar-left {
	width: 32px;
	height: 32px;
	justify-content: center;
	align-items: center;
}

.native-gallery__nav-bar-back-icon {
	color: #ffffff;
	font-size: 24px;
	font-weight: bold;
	text-align: center;
	line-height: 32px;
}

.tab-container {
	flex: 1;
	flex-direction: row;
	justify-content: space-around;
	margin-left: 20px;
}

.tab-item {
	padding: 0 12px;
	height: 44px;
	justify-content: center;
	align-items: center;
	position: relative;
}

.tab-text {
	color: #8e8e8e;
	font-size: 15px;
}

.active-text {
	color: #ffffff;
	font-weight: bold;
}

.active {
	border-bottom-width: 2px;
	border-bottom-color: #ff0043;
}

.gallery-container {
	flex: 1;
	background-color: #fff;
}

.content-container {
	flex: 1;
	/* 移除固定padding-bottom，改为动态计算 */
}

.gallery-selector {
	width: 750rpx;
	/* 高度改为动态计算，在computed中处理 */
}

.empty-state {
	flex: 1;
	justify-content: center;
	align-items: center;
	padding: 40px;
}

.empty-text {
	font-size: 16px;
	color: #999999;
	text-align: center;
}

/* 底部选择确认区域样式 - 性能优化：使用 flex 布局，避免绝对定位重绘 */
.bottom-selection-area {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #1a1a1a;
	border-top-left-radius: 16px;
	border-top-right-radius: 16px;
	padding: 16px 20px 20px 20px;
	/* 高度改为动态计算 */
	/* nvue性能优化：使用transform替代position动画 */
	transform: translateZ(0);
}

.selected-items-scroll {
	margin-bottom: 8px;
	/* 高度改为动态计算 */
	flex-direction: row;
	align-items: flex-start;
	background-color: transparent;
}

.selected-items-container {
	flex-direction: row;
	align-items: flex-start;
	height: 100px;
	padding: 0;
}

.selected-item {
	position: relative;
	width: 80px;
	height: 100px;
	border-radius: 6px;
	margin-right: 12px;
}

/* 选中项图片圆角通过包装容器实现 */
.selected-item-image-wrapper {
	width: 100%;
	height: 100%;
	border-radius: 6px;
	/* overflow: visible; 保证角标不被裁剪 */
}

.selected-item-image {
	width: 100%;
	height: 100%;
	border-radius: 6px;
	z-index: 1;
}

.selected-item-duration {
	position: absolute;
	bottom: 2px;
	left: 2px;
	background-color: rgba(0, 0, 0, 0.6);
	color: #ffffff;
	font-size: 11px;
	padding: 1px 4px;
	border-radius: 3px;
	z-index: 10;
	justify-content: center;
	align-items: center;
}

.duration-text {
	color: #ffffff;
	font-size: 11px;
}

.selected-item-remove {
	position: absolute;
	top: 2px;
	right: 2px;
	width: 20px;
	height: 20px;
	background-color: #ffffff;
	border-radius: 10px;
	justify-content: center;
	align-items: center;
	z-index: 10;
}

.remove-icon {
	color: #000000;
	font-size: 16px;
	font-weight: bold;
}

.bottom-actions {
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	padding: 0 5px;
}

.selection-tip {
	flex: 1;
}

.selection-tip-text {
	color: #ffffff;
	font-size: 13px;
	opacity: 0.9;
}

.confirm-btn {
	background-color: #ff0043;
	border-radius: 8px;
	padding: 10px 20px;
	margin-left: 16px;
	justify-content: center;
	align-items: center;
}

.confirm-btn-text {
	color: #ffffff;
	font-size: 16px;
	font-weight: 600;
}

.upload-btn-area {
	margin: 32px 0 16px 0;
	flex-direction: row;
	justify-content: center;
	align-items: center;
}
.upload-btn {
	background: linear-gradient(135deg, #ff2d55, #ff6b9d);
	color: #fff;
	font-size: 16px;
	padding: 12px 32px;
	border-radius: 8px;
	/* nvue不支持box-shadow，使用elevation替代 */
	elevation: 4;
	border: none;
}
</style> 