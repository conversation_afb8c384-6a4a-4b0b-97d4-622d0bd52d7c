<template>
  <view class="login-page">
    <image class="login-page__bg" src="@/asset/img/login/login_bg.png" mode="aspectFill" />
    <view class="login-page__content">
      <!-- App图标区域 -->
      <view class="login-page__app-icon-area">
        <image class="login-page__app-icon" src="@/asset/img/login/logo.png" mode="aspectFit" />
        <text class="login-page__app-title">旺剪</text>
      </view>
      
      <!-- 账号密码区域 -->
      <view class="login-page__form-area">
        <input 
          class="login-page__input" 
          v-model="formData.account" 
          placeholder="请输入账号"
          @focus="handleInputFocus('account')"
          @blur="handleInputBlur('account')"
          :class="{ 'login-page__input--focused': focusedField === 'account' }"
        />
        <input 
          class="login-page__input" 
          v-model="formData.password" 
          type="password" 
          placeholder="请输入密码"
          @focus="handleInputFocus('password')"
          @blur="handleInputBlur('password')"
          :class="{ 'login-page__input--focused': focusedField === 'password' }"
        />
      </view>
      
      <!-- 隐私协议勾选 -->
      <view class="login-page__privacy-row" @click="togglePrivacyAgreement">
        <view class="login-page__checkbox">
          <view v-if="formData.agreed" class="login-page__checkbox--checked">
            <text class="login-page__checkbox-tick">✔</text>
          </view>
          <view v-else class="login-page__checkbox--unchecked"></view>
        </view>
        <text class="login-page__privacy-text">
          登录即表示已阅读并同意
          <text class="login-page__privacy-link" @click="showUserAgreement">《用户协议》</text>和
          <text class="login-page__privacy-link" @click="showPrivacyPolicy">《隐私政策》</text>
        </text>
      </view>
      
      <!-- 错误提示 -->
      <view v-if="errorMessage" class="login-page__error-message">
        {{ errorMessage }}
      </view>
      
      <!-- 测试提示 -->
      <view class="login-page__test-tip" @click="fillTestAccount">
        <text class="login-page__test-tip-text">测试账号：admin，密码：admin</text>
        <text class="login-page__test-tip-hint">点击自动填充</text>
      </view>
      
      <!-- 开发环境测试按钮 -->
      <view class="login-page__dev-buttons">
        <button class="login-page__dev-btn" @click="clearLocalStorage">清除本地存储</button>
        <button class="login-page__dev-btn" @click="checkLoginStatus">检查登录状态</button>
        <button class="login-page__dev-btn login-page__dev-btn--primary" @click="goToHomePage">进入首页</button>
      </view>
      
      <!-- 登录按钮 -->
      <PrimaryButton 
        class="login-page__login-btn"
        :text="loginButtonText"
        :disabled="!canLogin || isLoading"
        :loading="isLoading"
        @click="handleLogin"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { useUserStore } from '@/store/modules/user'
import PrimaryButton from '@/components/common/PrimaryButton/index.vue'
import { onLoad } from '@dcloudio/uni-app'

// 用户状态管理
const userStore = useUserStore()

// 检查是否为开发环境
const isDevelopment = process.env.NODE_ENV === 'development'

// 响应式数据
const formData = reactive({
  account: '',
  password: '',
  agreed: false
})

const focusedField = ref('')
const isLoading = ref(false)
const errorMessage = ref('')

// 计算属性
const canLogin = computed(() => {
  return formData.account.trim() && formData.password.trim()
})

const loginButtonText = computed(() => {
  return isLoading.value ? '登录中...' : '登录'
})

// 方法
const handleInputFocus = (field) => {
  focusedField.value = field
  errorMessage.value = '' // 清除错误信息
}

const handleInputBlur = (field) => {
  focusedField.value = ''
  validateField(field)
}

const validateField = (field) => {
  if (field === 'account') {
    // 账号验证：只要有填写就通过
    if (!formData.account.trim()) {
      errorMessage.value = '请输入账号'
      return false
    }
  } else if (field === 'password') {
    // 密码验证：只要有填写就通过
    if (!formData.password.trim()) {
      errorMessage.value = '请输入密码'
      return false
    }
  }
  return true
}

const togglePrivacyAgreement = () => {
  formData.agreed = !formData.agreed
  errorMessage.value = '' // 清除错误信息
}

const validateForm = () => {
  if (!formData.account.trim()) {
    errorMessage.value = '请输入账号'
    return false
  }
  if (!formData.password.trim()) {
    errorMessage.value = '请输入密码'
    return false
  }
  if (!formData.agreed) {
    // 弹窗询问是否同意协议
    showAgreementDialog()
    return false
  }
  return true
}

// 显示协议确认弹窗
const showAgreementDialog = () => {
  uni.showModal({
    title: '用户协议确认',
    content: '为了保障您的权益，请阅读并同意《用户协议》和《隐私政策》。\n\n点击"同意"即表示您已阅读并同意相关条款。',
    confirmText: '同意并登录',
    cancelText: '暂不同意',
    confirmColor: '#ff2d55',
    success: (res) => {
      if (res.confirm) {
        // 用户同意协议，勾选并执行登录
        formData.agreed = true
        errorMessage.value = ''
        // 直接执行登录逻辑
        executeLogin()
      } else {
        // 用户不同意协议，显示错误提示
        errorMessage.value = '请先同意用户协议和隐私政策'
      }
    }
  })
}

// 执行登录逻辑
const executeLogin = async () => {
  isLoading.value = true
  errorMessage.value = ''
  
  try {
    // 调用用户store的登录方法
    const result = await userStore.login({
      account: formData.account.trim(),
      password: formData.password
    })
    
    if (result.success) {
      // 登录成功，显示成功提示
      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      })
      
      // 登录后批量初始化接口
      try {
        await userStore.initAfterLogin()
      } catch (initError) {
        console.error('初始化接口失败:', initError)
        // 初始化失败不影响登录流程
      }
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        // 使用reLaunch跳转到首页，清除导航栈
        uni.reLaunch({ 
          url: '/pages/index/index',
          success: () => {
            console.log('登录成功，已跳转到首页')
          },
          fail: (error) => {
            console.error('跳转失败:', error)
            // 如果跳转失败，尝试使用switchTab
            uni.switchTab({
              url: '/pages/index/index'
            })
          }
        })
      }, 1500)
    } else {
      // 登录失败，显示错误信息
      errorMessage.value = result.message || '账号或密码错误'
    }
  } catch (error) {
    console.error('登录错误:', error)
    errorMessage.value = '网络错误，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

const handleLogin = async () => {
  console.log('登录按钮被点击')
  console.log('当前表单数据:', formData)
  console.log('canLogin状态:', canLogin.value)
  
  // 添加一个简单的测试，确保方法被调用
  uni.showToast({
    title: '按钮被点击了',
    icon: 'none',
    duration: 1000
  })
  
  if (!validateForm()) {
    console.log('表单验证失败')
    return
  }
  
  console.log('表单验证通过，开始执行登录')
  // 如果验证通过，直接执行登录
  executeLogin()
}

// 显示用户协议
const showUserAgreement = () => {
  uni.showModal({
    title: '用户协议',
    content: '用户协议内容...\n\n这里是用户协议的详细内容，包括服务条款、使用规范等。',
    showCancel: false,
    confirmText: '我知道了',
    confirmColor: '#ff2d55'
  })
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  uni.showModal({
    title: '隐私政策',
    content: '隐私政策内容...\n\n这里是隐私政策的详细内容，包括数据收集、使用和保护等。',
    showCancel: false,
    confirmText: '我知道了',
    confirmColor: '#ff2d55'
  })
}

// 开发环境测试方法
const clearLocalStorage = () => {
  try {
    // 清除所有相关的本地存储
    uni.removeStorageSync('token')
    uni.removeStorageSync('refresh_token')
    uni.removeStorageSync('userInfo')
    uni.removeStorageSync('cid')
    uni.removeStorageSync('user_id')
    
    // 重置用户状态
    userStore.logout()
    
    uni.showToast({
      title: '本地存储已清除',
      icon: 'success',
      duration: 2000
    })
    
    console.log('本地存储已清除')
  } catch (error) {
    console.error('清除本地存储失败:', error)
    uni.showToast({
      title: '清除失败',
      icon: 'error',
      duration: 2000
    })
  }
}

const checkLoginStatus = () => {
  try {
    const token = uni.getStorageSync('token')
    const cid = uni.getStorageSync('cid')
    const userId = uni.getStorageSync('user_id')
    const userInfo = uni.getStorageSync('userInfo')
    
    const status = {
      hasToken: !!token,
      hasCid: !!cid,
      hasUserId: !!userId,
      hasUserInfo: !!userInfo,
      isLogin: userStore.isLogin
    }
    
    console.log('当前登录状态:', status)
    
    uni.showModal({
      title: '登录状态检查',
      content: `Token: ${status.hasToken ? '有' : '无'}\nCID: ${status.hasCid ? '有' : '无'}\nUID: ${status.hasUserId ? '有' : '无'}\n用户信息: ${status.hasUserInfo ? '有' : '无'}\nStore登录状态: ${status.isLogin ? '已登录' : '未登录'}`,
      showCancel: false,
      confirmText: '确定',
      confirmColor: '#ff2d55'
    })
  } catch (error) {
    console.error('检查登录状态失败:', error)
    uni.showToast({
      title: '检查失败',
      icon: 'error',
      duration: 2000
    })
  }
}

// 填充测试账号
const fillTestAccount = () => {
  // 填充测试账号和密码
          formData.account = 'admin'
      formData.password = 'admin'
  
  // 清除错误信息
  errorMessage.value = ''
  
  // 显示填充成功提示
  uni.showToast({
    title: '已填充测试账号',
    icon: 'success',
    duration: 1500
  })
  
  // 自动勾选隐私协议（方便测试）
  if (!formData.agreed) {
    formData.agreed = true
  }
}

// 直接进入首页（跳过登录流程）
const goToHomePage = () => {
  console.log('开发环境：跳过登录流程，直接进入首页')
  
  // 显示提示
  uni.showToast({
    title: '正在进入首页...',
    icon: 'loading',
    duration: 1000
  })
  
  // 延迟跳转，让用户看到提示
  setTimeout(() => {
    // 使用reLaunch跳转到首页，清除导航栈
    uni.reLaunch({ 
      url: '/pages/index/index',
      success: () => {
        console.log('开发环境：已跳过登录，直接进入首页')
        uni.showToast({
          title: '已进入首页',
          icon: 'success',
          duration: 1500
        })
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        // 如果跳转失败，尝试使用switchTab
        uni.switchTab({
          url: '/pages/index/index',
          success: () => {
            uni.showToast({
              title: '已进入首页',
              icon: 'success',
              duration: 1500
            })
          }
        })
      }
    })
  }, 1000)
}

onLoad(() => {
  // 进入登录页时清理用户信息（账号密码除外）
  userStore.logout()
  // 自动填充账号
  const savedAccount = uni.getStorageSync('login_account')
  formData.account = savedAccount || ''
  formData.password = ''
})
</script>

<style lang="scss" scoped>
.login-page {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #111113;
  overflow: hidden;
  
  &__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 0;
  }
  
  &__content {
    position: relative;
    z-index: 1;
    width: 85vw;
    max-width: 750rpx;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.04);
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx 0 rgba(0, 0, 0, 0.10);
    padding: 64rpx 32rpx 48rpx 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  &__app-icon-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 68rpx;
  }
  
  &__app-icon {
    width: 120rpx;
    height: 120rpx;
    border-radius: 24rpx;
    margin-bottom: 16rpx;
    position: relative;
    z-index: 1;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 120rpx;
      height: 120rpx;
      border-radius: 24rpx;
      background: #fff;
      z-index: -1;
      display: block;
      border-radius: 50%;
    }
  }
  
  &__app-title {
    font-size: 36rpx;
    color: #999797;
    font-weight: 600;
  }
  
  &__form-area {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 32rpx;
    margin-bottom: 32rpx;
  }
  
  &__input {
    width: 100%;
    height: 88rpx;
    border-radius: 16rpx;
    background: #232325;
    color: #fff;
    font-size: 28rpx;
    padding: 0 24rpx;
    border: 2rpx solid transparent;
    outline: none;
    transition: all 0.3s ease;
    
    &--focused {
      border-color: #ff2d55;
      background: #2a2a2c;
    }
    
    &::placeholder {
      color: #666;
    }
  }
  
  &__privacy-row {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    width: 100%;
    cursor: pointer;
  }
  
  &__privacy-text {
    color: #bfbfbf;
    font-size: 24rpx;
    margin-left: 12rpx;
  }
  
  &__privacy-link {
    color: #ff2d55;
    text-decoration: underline;
    cursor: pointer;
    
    &:active {
      opacity: 0.8;
    }
  }
  
  &__checkbox {
    width: 32rpx;
    height: 32rpx;
    border-radius: 6rpx;
    margin-right: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &--unchecked {
      width: 32rpx;
      height: 32rpx;
      border: 2rpx solid #bfbfbf;
      border-radius: 6rpx;
      background: #232325;
    }
    
    &--checked {
      width: 32rpx;
      height: 32rpx;
      border-radius: 6rpx;
      background: #ff2d55;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  &__checkbox-tick {
    color: #fff;
    font-size: 24rpx;
    font-weight: bold;
    line-height: 1;
  }
  
  &__error-message {
    color: #ff4d4f;
    font-size: 24rpx;
    margin-bottom: 16rpx;
    text-align: center;
    min-height: 32rpx;
  }
  
  &__test-tip {
    width: 100%;
    padding: 16rpx 24rpx;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12rpx;
    margin-bottom: 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1rpx solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:active {
      background: rgba(255, 255, 255, 0.1);
      transform: scale(0.98);
    }
  }
  
  &__test-tip-text {
    color: #999;
    font-size: 22rpx;
    text-align: center;
    margin-bottom: 4rpx;
  }
  
  &__test-tip-hint {
    color: #ff2d55;
    font-size: 20rpx;
    text-align: center;
    opacity: 0.8;
  }
  
  &__dev-buttons {
    width: 100%;
    display: flex;
    gap: 16rpx;
    margin-bottom: 24rpx;
  }
  
  &__dev-btn {
    flex: 1;
    min-width: 120rpx;
    height: 64rpx;
    background: rgba(255, 45, 85, 0.1);
    border: 1rpx solid rgba(255, 45, 85, 0.3);
    border-radius: 12rpx;
    color: #ff2d55;
    font-size: 22rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    padding: 0 12rpx;
    
    &:active {
      background: rgba(255, 45, 85, 0.2);
      transform: scale(0.98);
    }
    
    &--primary {
      background: rgba(76, 175, 80, 0.1);
      border-color: rgba(76, 175, 80, 0.3);
      color: #4caf50;
      
      &:active {
        background: rgba(76, 175, 80, 0.2);
      }
    }
  }
  
  &__login-btn {
    width: 100%;
    margin-top: 12rpx;
  }
}
</style>
