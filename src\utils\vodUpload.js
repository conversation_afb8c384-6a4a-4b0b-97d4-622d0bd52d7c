// 阿里云VOD上传工具（本地SDK版）
// 注意：需在index.html或入口js中提前加载src/lib/aliyun-upload-sdk-1.5.6/aliyun-upload-sdk-1.5.7.min.js

/**
 * 上传视频到阿里云VOD
 * @param {File} file - 要上传的视频文件
 * @param {string} uploadAuth - 上传凭证
 * @param {string} uploadAddress - 上传地址
 * @param {Function} onProgress - 上传进度回调(percent)
 * @returns {Promise<string>} resolve(videoId)
 */
export function uploadToAliyunVOD({ file, uploadAuth, uploadAddress, onProgress }) {
  return new Promise((resolve, reject) => {
    if (!window.AliyunUpload || !window.AliyunUpload.Vod) {
      return reject('AliyunUpload SDK未加载，请检查引入路径');
    }
    const uploader = new window.AliyunUpload.Vod({
      onUploadstarted: (uploadInfo) => {
        uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress);
      },
      onUploadprogress: (uploadInfo, totalSize, loadedPercent) => {
        if (onProgress) onProgress(Math.floor(loadedPercent * 100));
      },
      onUploadSucceed: (uploadInfo) => {
        resolve(uploadInfo.videoId);
      },
      onUploadFailed: (uploadInfo, code, message) => {
        reject(new Error(message || code));
      }
    });
    uploader.addFile(file, null, null, null, undefined);
    uploader.startUpload();
  });
}
