# Dialog 公共弹窗组件

## 组件说明

Dialog 组件用于展示业务弹窗，支持标题、功能区、主内容、扩展功能、底部操作按钮等模块，支持像素级自定义，适用于多种业务场景。

## 组件目录
- 组件文件：`src/components/business/Dialog/index.vue`
- 静态资源：`src/asset/dialog/`

## Props
| 属性名         | 类型    | 说明                       | 必填 | 默认值 |
| -------------- | ------- | -------------------------- | ---- | ------ |
| visible        | boolean | 是否显示弹窗               | 是   | -      |
| title          | string  | 弹窗标题                   | 是   | -      |
| content        | string  | 主文案内容                 | 是   | -      |
| features       | array   | 顶部功能按钮区配置         | 否   | []     |
| extraFeatures  | array   | 扩展功能按钮区配置         | 否   | []     |
| actions        | array   | 底部操作按钮配置           | 是   | -      |
| closeIcon      | string  | 关闭按钮图片（本地路径）   | 否   | /src/asset/dialog/dialog-close.png |

### features/extraFeatures 配置项
- key: string（唯一标识，可选）
- icon: string（本地图片路径）
- text: string（按钮文案）

### actions 配置项
- key: string（唯一标识，可选）
- text: string（按钮文案）
- type: 'primary' | 'default'（按钮类型）

## Emits
| 事件名        | 说明                   |
| ------------- | ---------------------- |
| close         | 点击关闭/遮罩时触发    |
| confirm       | 点击主操作按钮时触发   |
| cancel        | 点击次操作按钮时触发   |
| featureClick  | 点击功能按钮时触发     |

## 插槽
- content：自定义主内容区

## 用法示例
```vue
<template>
  <Dialog
    :visible="showDialog"
    title="增加文案"
    content="在深圳龙华，你只要月薪超过1万..."
    :features="features"
    :extraFeatures="extraFeatures"
    :actions="actions"
    @close="showDialog = false"
    @confirm="onConfirm"
    @cancel="onCancel"
    @featureClick="onFeatureClick"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Dialog from '@/components/business/Dialog/index.vue';

const showDialog = ref(true);
const features = [
  { icon: '/src/asset/dialog/dialog-ai.png', text: 'AI生成' }
];
const extraFeatures = [
  { icon: '/src/asset/dialog/dialog-douyin-link.png', text: '抖音链接提取' },
  { icon: '/src/asset/dialog/dialog-ai-rewrite.png', text: 'AI改写' }
];
const actions = [
  { text: '取消', type: 'default' },
  { text: '确定', type: 'primary' }
];
const onConfirm = () => {};
const onCancel = () => {};
const onFeatureClick = (feature: any) => {};
</script>
```

## 注意事项
- 所有图片资源需本地化，路径以 `/src/asset/dialog/` 开头。
- 仅开发环境下可在 demo 目录查看演示效果。
