# Banner 轮播横幅组件

## 组件描述

Banner 是一个功能丰富的轮播横幅组件，支持自动轮播、自定义指示器、背景图片和交互按钮。适用于首页推广、活动宣传等场景。

## 功能特性

- 🎯 **自动轮播** - 支持自动播放和手动切换
- 🎨 **自定义样式** - 支持背景图片、渐变背景和文字内容
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🔄 **交互事件** - 支持点击事件和轮播切换事件
- 🎭 **指示器** - 支持默认指示器和自定义指示器

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| bannerList | Array | [] | 轮播数据列表 |
| indicatorDots | Boolean | false | 是否显示默认指示器 |
| autoplay | Boolean | true | 是否自动轮播 |
| interval | Number | 4000 | 轮播间隔时间（毫秒） |
| duration | Number | 500 | 滑动动画时长（毫秒） |
| circular | Boolean | true | 是否循环轮播 |
| showCustomIndicator | Boolean | true | 是否显示自定义指示器 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | item: Object | 横幅点击时触发 |
| change | index: Number | 轮播切换时触发 |

## 数据结构

### bannerList 数据格式

```javascript
[
  {
    title: '横幅标题',                    // 可选：横幅标题
    subtitle: '横幅副标题',               // 可选：横幅副标题
    buttonText: '立即体验',               // 可选：按钮文字
    image: '/static/images/avatar.png',   // 可选：右侧图片
    backgroundImage: 'linear-gradient(...)', // 可选：背景图片或渐变
    link: '/pages/detail/index'           // 可选：跳转链接
  }
]
```

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <Banner 
      :banner-list="bannerList"
      @click="handleBannerClick"
      @change="handleBannerChange"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import Banner from '@/components/business/Banner/index.vue'

const bannerList = ref([
  {
    title: 'AI 智能创作',
    subtitle: '一键生成优质内容',
    buttonText: '立即体验',
    image: '/static/images/ai-avatar.png',
    backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    link: '/pages/ai-create/index'
  },
  {
    title: '视频制作',
    subtitle: '专业视频编辑工具',
    buttonText: '开始制作',
    image: '/static/images/video-avatar.png',
    backgroundImage: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    link: '/pages/video-create/index'
  }
])

const handleBannerClick = (item) => {
  console.log('点击横幅:', item.title)
}

const handleBannerChange = (index) => {
  console.log('轮播切换到:', index)
}
</script>
```

### 自定义配置

```vue
<template>
  <view>
    <Banner 
      :banner-list="bannerList"
      :indicator-dots="true"
      :autoplay="false"
      :interval="5000"
      :duration="800"
      :circular="true"
      :show-custom-indicator="false"
      @click="handleBannerClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import Banner from '@/components/business/Banner/index.vue'

const bannerList = ref([
  {
    title: '限时优惠',
    subtitle: '新用户专享福利',
    buttonText: '立即抢购',
    backgroundImage: '/static/images/promotion-bg.jpg',
    link: '/pages/promotion/index'
  }
])

const handleBannerClick = (item) => {
  uni.showToast({
    title: '正在跳转...',
    icon: 'loading'
  })
}
</script>
```

### 国际化支持

```vue
<template>
  <view>
    <Banner 
      :banner-list="bannerList"
      @click="handleBannerClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import Banner from '@/components/business/Banner/index.vue'

const bannerList = ref([
  {
    // 使用国际化 key，组件会自动获取翻译
    title: '', // 对应 $t('pages.home.bannerTitle')
    subtitle: '', // 对应 $t('pages.home.bannerSubtitle')
    buttonText: '', // 对应 $t('pages.home.bannerButton')
    image: '/static/images/banner-avatar.png',
    backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    link: '/pages/create/index'
  }
])

const handleBannerClick = (item) => {
  console.log('横幅点击:', item)
}
</script>
```

## 样式定制

组件使用 SCSS 编写，主要样式特点：

- 高度：`400rpx`
- 圆角：`$radius-lg`
- 渐变背景：默认蓝紫渐变
- 文字颜色：白色系
- 按钮样式：圆角按钮，渐变背景

## 注意事项

1. 组件支持国际化，可以通过 `$t` 函数获取翻译文本
2. 背景图片支持 URL 和 CSS 渐变两种形式
3. 自定义指示器需要手动实现跳转逻辑
4. 组件会自动处理页面跳转
5. 建议图片尺寸保持一致，避免显示异常

## 最佳实践

1. **图片优化**：使用适当尺寸的图片，避免过大影响加载速度
2. **文案简洁**：标题和副标题保持简洁明了
3. **按钮文案**：使用行动性词汇，如"立即体验"、"开始使用"
4. **轮播配置**：根据内容重要性调整轮播间隔
5. **响应式**：在不同设备上测试显示效果 