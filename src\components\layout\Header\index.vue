<template>
  <view class="header" :class="{ 'header--dark': isDark }">
    <view class="header__safe-area"></view>
    <view class="header__content">
      <!-- 左侧区域 -->
      <view class="header__left">
        <view 
          v-if="showBack" 
          class="header__back interactive" 
          @tap="handleBack"
        >
          <text class="icon-back">‹</text>
        </view>
        <slot name="left"></slot>
      </view>
      
      <!-- 中间标题区域 -->
      <view class="header__center">
        <text v-if="title" class="header__title">{{ title }}</text>
        <slot name="center"></slot>
      </view>
      
      <!-- 右侧区域 -->
      <view class="header__right">
        <slot name="right">
          <view 
            v-if="showSettings" 
            class="header__settings interactive" 
            @tap="handleSettings"
          >
            <text class="icon-settings">⚙</text>
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useAppStore } from '@/store/modules/app'

// Props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  showBack: {
    type: Boolean,
    default: false
  },
  showSettings: {
    type: Boolean,
    default: false
  },
  backgroundColor: {
    type: String,
    default: ''
  },
  textColor: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['back', 'settings'])

// Store
const appStore = useAppStore()

// Computed
const isDark = computed(() => appStore.isDark)

// Methods
const handleBack = () => {
  emit('back')
  if (!emit('back')) {
    appStore.navigateBack()
  }
}

const handleSettings = () => {
  emit('settings')
  if (!emit('settings')) {
    appStore.navigateTo('/pages/settings/index')
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: $bg-primary;
  border-bottom: 1rpx solid $border-light;
  
  &--dark {
    background-color: $bg-dark;
    border-bottom-color: $border-dark;
  }
  
  &__safe-area {
    height: $safe-area-inset-top;
    background-color: inherit;
  }
  
  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: $header-height;
    padding: 0 $spacing-lg;
    position: relative;
  }
  
  &__left,
  &__right {
    display: flex;
    align-items: center;
    min-width: 80rpx;
  }
  
  &__left {
    justify-content: flex-start;
  }
  
  &__right {
    justify-content: flex-end;
  }
  
  &__center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 60%;
  }
  
  &__title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    text-align: center;
    @include text-ellipsis;
    
    .header--dark & {
      color: $text-inverse;
    }
  }
  
  &__back,
  &__settings {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64rpx;
    height: 64rpx;
    border-radius: $radius-full;
    background-color: transparent;
    
    &:active {
      background-color: rgba($neutral-500, 0.1);
    }
  }
  
  .icon-back,
  .icon-settings {
    font-size: $font-size-2xl;
    color: $text-primary;
    
    .header--dark & {
      color: $text-inverse;
    }
  }
  
  .icon-back {
    font-size: $font-size-3xl;
    font-weight: $font-weight-bold;
    margin-left: -4rpx;
  }
}

// 为页面内容留出顶部空间
.header + .page-content {
  margin-top: calc(#{$header-height} + #{$safe-area-inset-top});
}
</style> 