# 旺剪App uni-app 项目开发规则

## 项目概述
- **项目名称**: 旺剪App - 智能短视频剪辑与运营工具
- **技术栈**: uni-app + Vue3 + Pinia + JavaScript
- **支持平台**: H5、微信小程序、支付宝小程序、App等多端
- **核心功能**: 智能短视频剪辑、AI混剪、模板视频、爆款分析

## 开发环境规则

### 1. 代码结构规范
- 严格按照 uni-app 官方推荐的目录结构组织代码
- 组件放置在 `src/components/` 目录下，按功能分类
- 页面放置在 `src/pages/` 目录下，每个页面一个文件夹
- 工具类放置在 `src/utils/` 目录下
- API 接口放置在 `src/api/` 目录下
- 状态管理放置在 `src/store/` 目录下

### 2. 命名规范
- **文件命名**: 使用 kebab-case (短横线命名)
- **组件命名**: 使用 PascalCase (大驼峰命名)
- **变量命名**: 使用 camelCase (小驼峰命名)
- **常量命名**: 使用 UPPER_SNAKE_CASE (大写下划线命名)
- **页面路径**: 使用 kebab-case，与文件夹名保持一致

### 3. Vue3 组合式API规范
- 优先使用 Vue3 的 Composition API
- 使用 `<script setup>` 语法糖
- 响应式数据使用 `ref` 和 `reactive`
- 计算属性使用 `computed`
- 生命周期使用 `onMounted`、`onUnmounted` 等

### 4. uni-app 特定规范
- 使用 `uni.` 前缀的 API 进行平台功能调用
- 样式单位优先使用 `rpx` 实现响应式布局
- 条件编译使用 `#ifdef` 和 `#endif` 处理平台差异
- 页面跳转使用 `uni.navigateTo`、`uni.redirectTo` 等

### 5. nvue 文件开发规范

#### 5.1 nvue 基本概念
- nvue 是基于 weex 引擎的原生渲染页面
- 主要用于 App 端需要高性能的页面（如视频播放、长列表等）
- nvue 页面在 iOS 和 Android 上都是原生渲染，性能更好
- nvue 不支持所有的 CSS 特性，需要严格按照规范编写

#### 5.2 nvue 文件结构
```vue
<template>
  <!-- nvue 只支持 weex 内置组件 -->
  <div class="container">
    <text class="title">{{ title }}</text>
    <image class="image" :src="imageUrl" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      title: 'nvue 页面',
      imageUrl: '/static/logo.png'
    }
  },
  methods: {
    handleClick() {
      // 事件处理
    }
  }
}
</script>

<style>
/* nvue 样式必须严格按照 weex 规范 */
.container {
  flex: 1;
  background-color: #ffffff;
}

.title {
  font-size: 32px; /* 注意：nvue 中使用 px，不是 rpx */
  color: #333333;
  text-align: center;
}

.image {
  width: 200px;
  height: 200px;
}
</style>
```

#### 5.3 nvue 样式严格规范

##### 5.3.1 尺寸单位规范
```css
/* ❌ 错误写法 - nvue 不支持百分比 */
.wrong-container {
  width: 100%;
  height: 50%;
}

/* ❌ 错误写法 - nvue 不支持 rpx */
.wrong-size {
  width: 750rpx;
  height: 200rpx;
}

/* ✅ 正确写法 - 使用固定 px 值 */
.correct-container {
  width: 750px;  /* 750px 等同于 100% 屏幕宽度 */
  height: 400px;
}

/* ✅ 正确写法 - 使用 flex 布局 */
.flex-container {
  flex: 1;  /* 占满剩余空间 */
  flex-direction: column;
}
```

##### 5.3.2 布局规范
```css
/* nvue 默认使用 flex 布局 */
.container {
  /* 主轴方向：column(默认) | row */
  flex-direction: column;
  
  /* 主轴对齐：flex-start(默认) | flex-end | center | space-between | space-around */
  justify-content: flex-start;
  
  /* 交叉轴对齐：stretch(默认) | flex-start | flex-end | center */
  align-items: stretch;
  
  /* 换行：nowrap(默认) | wrap */
  flex-wrap: nowrap;
}

/* 子元素 flex 属性 */
.flex-item {
  flex: 1;  /* 占比 */
  flex-grow: 1;  /* 放大比例 */
  flex-shrink: 1;  /* 缩小比例 */
  flex-basis: auto;  /* 基础大小 */
}
```

##### 5.3.3 定位规范
```css
/* nvue 支持的定位方式 */
.positioned-element {
  position: relative; /* relative | absolute | fixed | sticky */
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
}

/* ❌ 错误写法 - 不支持 z-index */
.wrong-layer {
  z-index: 999;
}

/* ✅ 正确写法 - 通过 DOM 层级控制层叠 */
<!-- 后面的元素会覆盖前面的元素 -->
```

##### 5.3.4 文本样式规范
```css
.text-style {
  /* 字体大小 - 必须使用 px */
  font-size: 32px;
  
  /* 字体粗细 */
  font-weight: normal; /* normal | bold */
  
  /* 字体样式 */
  font-style: normal; /* normal | italic */
  
  /* 文本颜色 */
  color: #333333;
  
  /* 文本对齐 */
  text-align: left; /* left | center | right */
  
  /* 行高 */
  line-height: 48px;
  
  /* 文本装饰 */
  text-decoration: none; /* none | underline | line-through */
}

/* ❌ 不支持的文本属性 */
.unsupported-text {
  text-shadow: 1px 1px 1px #000; /* 不支持 */
  letter-spacing: 2px; /* 不支持 */
  word-spacing: 4px; /* 不支持 */
}
```

##### 5.3.5 背景和边框规范
```css
.background-border {
  /* 背景颜色 */
  background-color: #ffffff;
  
  /* ❌ 不支持背景图片 */
  /* background-image: url('image.png'); */
  
  /* 边框 */
  border-width: 1px;
  border-style: solid; /* solid | dashed | dotted */
  border-color: #cccccc;
  
  /* 圆角 */
  border-radius: 8px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
```

##### 5.3.6 变换和动画规范
```css
.transform-animation {
  /* 变换 */
  transform: translateX(100px) translateY(50px) rotate(45deg) scale(1.2);
  
  /* 变换原点 */
  transform-origin: center center; /* left | center | right top | center | bottom */
  
  /* 过渡动画 */
  transition-property: transform, opacity;
  transition-duration: 0.3s;
  transition-timing-function: ease-in-out; /* linear | ease | ease-in | ease-out | ease-in-out */
  transition-delay: 0s;
}

/* ❌ 不支持的变换 */
.unsupported-transform {
  transform: skew(30deg); /* 不支持倾斜 */
  transform: perspective(1000px); /* 不支持透视 */
}
```

#### 5.4 nvue 组件使用规范

##### 5.4.1 支持的内置组件
```vue
<template>
  <div class="container">
    <!-- 容器组件 -->
    <div class="box"></div>
    
    <!-- 文本组件 -->
    <text class="text">文本内容</text>
    
    <!-- 图片组件 -->
    <image class="image" :src="imageUrl" resize="cover" />
    
    <!-- 输入框组件 -->
    <input class="input" type="text" placeholder="请输入" />
    
    <!-- 滚动容器 -->
    <scroller class="scroller">
      <div v-for="item in list" :key="item.id">
        <text>{{ item.name }}</text>
      </div>
    </scroller>
    
    <!-- 列表组件（高性能） -->
    <list class="list">
      <cell v-for="item in list" :key="item.id">
        <text>{{ item.name }}</text>
      </cell>
    </list>
    
    <!-- 轮播图 -->
    <slider class="slider" auto-play="true">
      <div v-for="item in banners" :key="item.id" class="slide">
        <image :src="item.image" class="slide-image" />
      </div>
    </slider>
  </div>
</template>
```

##### 5.4.2 不支持的组件和特性
```vue
<!-- ❌ 以下组件在 nvue 中不支持 -->
<template>
  <!-- 不支持 view 组件，使用 div 替代 -->
  <!-- <view></view> -->
  
  <!-- 不支持 button 组件，需要自定义 -->
  <!-- <button></button> -->
  
  <!-- 不支持 navigator 组件 -->
  <!-- <navigator></navigator> -->
  
  <!-- 不支持小程序特有组件 -->
  <!-- <swiper></swiper> -->
  <!-- <picker></picker> -->
</template>
```

#### 5.5 nvue 响应式适配方案

##### 5.5.1 屏幕适配计算
```javascript
// nvue 屏幕适配工具
const screenWidth = 750; // 设计稿宽度
const deviceWidth = uni.getSystemInfoSync().screenWidth;

// px 转换函数
export function px2dp(px) {
  return (px * deviceWidth) / screenWidth;
}

// 使用示例
export default {
  data() {
    return {
      containerWidth: px2dp(750), // 全屏宽度
      itemWidth: px2dp(200),      // 元素宽度
      fontSize: px2dp(32)         // 字体大小
    }
  }
}
```

##### 5.5.2 动态样式绑定
```vue
<template>
  <div class="container" :style="containerStyle">
    <text :style="textStyle">动态样式文本</text>
  </div>
</template>

<script>
export default {
  computed: {
    containerStyle() {
      return {
        width: this.px2dp(750) + 'px',
        height: this.px2dp(400) + 'px',
        backgroundColor: '#ffffff'
      }
    },
    textStyle() {
      return {
        fontSize: this.px2dp(32) + 'px',
        color: '#333333'
      }
    }
  },
  methods: {
    px2dp(px) {
      const deviceWidth = uni.getSystemInfoSync().screenWidth;
      return (px * deviceWidth) / 750;
    }
  }
}
</script>
```

#### 5.6 nvue 性能优化规范

##### 5.6.1 长列表优化
```vue
<template>
  <!-- 使用 list 组件替代 scroll-view -->
  <list class="list" @loadmore="loadMore" loadmoreoffset="50">
    <refresh class="refresh" @refresh="onRefresh" :display="refreshing">
      <text class="refresh-text">下拉刷新</text>
    </refresh>
    
    <cell v-for="item in dataList" :key="item.id" class="cell">
      <div class="item">
        <text class="title">{{ item.title }}</text>
        <text class="desc">{{ item.description }}</text>
      </div>
    </cell>
    
    <loading class="loading" @loading="onLoading" :display="loading">
      <text class="loading-text">加载中...</text>
    </loading>
  </list>
</template>

<script>
export default {
  data() {
    return {
      dataList: [],
      refreshing: false,
      loading: false,
      page: 1
    }
  },
  methods: {
    async loadMore() {
      if (this.loading) return;
      this.loading = true;
      
      try {
        const newData = await this.fetchData(this.page + 1);
        this.dataList.push(...newData);
        this.page++;
      } catch (error) {
        console.error('加载失败:', error);
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false;
      }
    },
    
    async onRefresh() {
      this.refreshing = true;
      
      try {
        const newData = await this.fetchData(1);
        this.dataList = newData;
        this.page = 1;
      } catch (error) {
        console.error('刷新失败:', error);
        uni.showToast({
          title: '刷新失败，请重试',
          icon: 'none'
        })
      } finally {
        this.refreshing = false;
      }
    }
  }
}
</script>
```

##### 5.6.2 图片优化
```vue
<template>
  <div class="image-container">
    <!-- 图片懒加载 -->
    <image 
      class="lazy-image"
      :src="imageSrc"
      resize="cover"
      @load="onImageLoad"
      @error="onImageError"
      placeholder="/static/placeholder.png"
    />
  </div>
</template>

<style>
.image-container {
  width: 200px;
  height: 200px;
  background-color: #f5f5f5;
}

.lazy-image {
  width: 200px;
  height: 200px;
}
</style>
```

#### 5.7 nvue 调试和测试

##### 5.7.1 调试技巧
```javascript
// nvue 调试方法
export default {
  mounted() {
    // 使用 console.log 调试
    console.log('nvue 页面已挂载');
    
    // 获取元素信息
    const dom = weex.requireModule('dom');
    dom.getComponentRect(this.$refs.container, (result) => {
      console.log('容器尺寸:', result);
    });
  }
}
```

##### 5.7.2 常见问题解决
```css
/* 问题1: 元素不显示 */
.invisible-fix {
  /* 确保设置了宽高 */
  width: 100px;
  height: 100px;
  /* 确保有背景色或内容 */
  background-color: #ffffff;
}

/* 问题2: 文本不换行 */
.text-wrap {
  /* text 组件默认不换行，需要设置 */
  lines: 0; /* 0 表示不限制行数 */
  word-wrap: break-word;
}

/* 问题3: flex 布局异常 */
.flex-fix {
  /* 明确设置 flex 方向 */
  flex-direction: column;
  /* 设置对齐方式 */
  justify-content: flex-start;
  align-items: stretch;
}
```

#### 5.8 nvue 与 vue 页面混合使用

##### 5.8.1 页面选择原则
```javascript
// 使用 nvue 的场景：
// 1. 需要高性能的长列表页面
// 2. 视频播放页面
// 3. 复杂动画页面
// 4. 需要原生渲染性能的页面

// 使用 vue 的场景：
// 1. 普通的业务页面
// 2. 表单页面
// 3. 需要丰富 CSS 特性的页面
// 4. 需要使用 uni-app 组件库的页面
```

##### 5.8.2 页面间通信
```javascript
// nvue 页面跳转到 vue 页面
uni.navigateTo({
  url: '/pages/vue-page/index?param=value'
});

// vue 页面跳转到 nvue 页面
uni.navigateTo({
  url: '/pages/nvue-page/index.nvue?param=value'
});

// 使用 eventBus 进行页面通信
// 在 nvue 页面中
uni.$emit('dataUpdate', { data: 'new data' });

// 在 vue 页面中
uni.$on('dataUpdate', (data) => {
  console.log('接收到数据:', data);
});
```

## 技术实现规则

### 1. 状态管理 (Pinia)
```javascript
// store 模块示例
import { defineStore } from 'pinia'

export const useVideoStore = defineStore('video', {
  state: () => ({
    currentVideo: null,
    videoList: [],
    isPlaying: false
  }),
  
  getters: {
    hasVideo: (state) => state.videoList.length > 0
  },
  
  actions: {
    async loadVideo(videoId) {
      // 异步操作
    }
  }
})
```

### 2. API 请求封装
```javascript
// 统一请求封装
const request = (options) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: baseURL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          // 统一错误处理，提示信息使用中文
          const errorMsg = getErrorMessage(res.statusCode)
          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          })
          reject(res)
        }
      },
      fail: (error) => {
        // 网络异常提示中文
        uni.showToast({
          title: '网络连接异常，请检查网络设置',
          icon: 'none',
          duration: 2000
        })
        reject(error)
      }
    })
  })
}

// 错误状态码对应的中文提示
const getErrorMessage = (statusCode) => {
  const errorMessages = {
    400: '请求参数错误',
    401: '登录已过期，请重新登录',
    403: '没有访问权限',
    404: '请求的资源不存在',
    405: '请求方法不被允许',
    408: '请求超时，请重试',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务暂时不可用',
    504: '网关超时'
  }
  return errorMessages[statusCode] || '请求失败，请稍后重试'
}

// 带加载提示的请求封装
const requestWithLoading = (options, loadingText = '加载中...') => {
  uni.showLoading({
    title: loadingText,
    mask: true
  })
  
  return request(options).finally(() => {
    uni.hideLoading()
  })
}
```

### 3. 组件开发规范
```vue
<template>
  <view class="component-name">
    <!-- 组件内容 -->
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// Props 定义
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})

// Emits 定义
const emit = defineEmits(['click', 'change'])

// 响应式数据
const isVisible = ref(false)

// 计算属性
const displayTitle = computed(() => {
  return props.title || '默认标题'
})

// 生命周期
onMounted(() => {
  console.log('组件已挂载')
})

// 方法
const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
.component-name {
  // 样式使用 rpx 单位
  padding: 20rpx;
  
  // 支持 SCSS 嵌套
  .title {
    font-size: 32rpx;
    color: #333;
  }
}
</style>
```

### 4. 页面开发规范
```vue
<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <custom-nav-bar :title="pageTitle" />
    
    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 内容区域 -->
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useVideoStore } from '@/store/modules/video'

// 页面标题
const pageTitle = ref('页面标题')

// 使用 store
const videoStore = useVideoStore()

// 页面生命周期
onMounted(() => {
  // 页面初始化逻辑
})

// 页面方法
const handlePageAction = () => {
  // 页面操作逻辑
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-content {
  padding: 20rpx;
}
</style>
```

## 平台适配规则

### 1. 条件编译
```javascript
// 平台判断
// #ifdef H5
console.log('H5平台')
// #endif

// #ifdef MP-WEIXIN
console.log('微信小程序')
// #endif

// #ifdef APP-PLUS
console.log('App平台')
// #endif
```

### 2. 样式适配
```scss
// 安全区域适配
.safe-area-bottom {
  // #ifdef APP-PLUS
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  // #endif
}

// 状态栏高度适配
.status-bar {
  // #ifdef APP-PLUS
  height: var(--status-bar-height);
  // #endif
}
```

### 3. 功能适配
```javascript
// 获取系统信息
const getSystemInfo = () => {
  return new Promise((resolve) => {
    uni.getSystemInfo({
      success: (res) => {
        resolve(res)
      }
    })
  })
}

// 选择图片
const chooseImage = () => {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count: 9,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: resolve,
      fail: reject
    })
  })
}
```

## 性能优化规则

### 1. 图片优化
- 使用合适的图片格式 (webp > jpg > png)
- 图片懒加载使用 `lazy-load` 属性
- 大图片使用 `mode="aspectFit"` 或 `mode="aspectFill"`

### 2. 列表优化
- 长列表使用虚拟滚动或分页加载
- 使用 `scroll-view` 组件优化滚动性能
- 避免在 `v-for` 中使用复杂计算

### 3. 内存管理
- 及时清理定时器和事件监听器
- 大数据对象使用 `Object.freeze()` 冻结
- 避免内存泄漏，正确使用生命周期

## 测试规则

### 1. 单元测试
- 使用 Jest 进行单元测试
- 组件测试覆盖率不低于 80%
- 工具函数必须有对应的测试用例

### 2. 集成测试
- 使用 uni-automator 进行自动化测试
- 关键业务流程必须有集成测试
- API 接口测试覆盖主要功能

### 3. 兼容性测试
- 在主要平台进行功能测试
- 不同设备尺寸的适配测试
- 网络异常情况的处理测试

## 发布规则

### 1. 代码检查
- 使用 ESLint 进行代码规范检查
- 使用 Prettier 进行代码格式化
- 提交前必须通过所有检查

### 2. 构建优化
- 生产环境移除 console.log
- 启用代码压缩和混淆
- 优化图片和静态资源

### 3. 版本管理
- 使用语义化版本号 (Semantic Versioning)
- 重要更新必须有 CHANGELOG
- 发布前进行充分测试

## 安全规则

### 1. 数据安全
- 敏感信息不得硬编码在代码中
- 使用 HTTPS 进行网络请求
- 用户数据进行适当的加密存储

### 2. 权限管理
- 按需申请系统权限
- 用户隐私信息获取需要明确授权
- 遵循各平台的隐私政策要求

### 3. 输入验证
- 所有用户输入必须进行验证
- 防止 XSS 和注入攻击
- 文件上传需要类型和大小限制

## 文档规则

### 1. 代码注释
- 复杂逻辑必须有详细注释
- 公共方法必须有 JSDoc 注释
- 组件 props 和 events 必须有说明

### 2. README 文档
- 项目说明和快速开始指南
- 开发环境搭建步骤
- 部署和发布流程说明

### 3. API 文档
- 接口文档使用统一格式
- 包含请求参数和响应示例
- 错误码和异常处理说明

## 团队协作规则

### 1. Git 工作流
- 使用 Git Flow 分支管理策略
- 功能开发在 feature 分支进行
- 代码合并前必须进行 Code Review

### 2. 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

### 3. 问题跟踪
- 使用 Issue 跟踪 bug 和需求
- 重要问题必须有详细的复现步骤
- 及时更新问题状态和进展

## 特殊注意事项

### 1. 视频处理
- 视频文件大小限制和格式支持
- 视频编码和解码性能优化
- 不同平台的视频播放兼容性

### 2. AI 功能集成
- AI 接口调用的错误处理
- 用户数据隐私保护
- AI 功能的降级方案

### 3. 用户体验
- 加载状态和错误提示
- 操作反馈和引导提示
- 无网络状态的处理

这些规则确保项目的代码质量、性能表现和用户体验，同时保持团队开发的一致性和效率。