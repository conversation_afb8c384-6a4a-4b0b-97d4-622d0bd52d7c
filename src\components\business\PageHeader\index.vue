<template>
  <view class="page-header" @click="handleHeaderClick">
    <view class="page-header__left">
      <view 
        v-if="showBack"
        class="page-header__back"
        @click="handleBack"
      >
        <text class="page-header__back-icon">←</text>
      </view>
    </view>
    
    <view class="page-header__center">
      <text class="page-header__title">{{ title }}</text>
    </view>
    
    <view class="page-header__right">
      <view 
        v-if="rightText"
        class="page-header__action"
        @click="handleRightClick"
      >
        <text class="page-header__action-text">{{ rightText }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

// Props
const props = defineProps({
  // 页面标题
  title: {
    type: String,
    default: ''
  },
  // 右侧按钮文字
  rightText: {
    type: String,
    default: ''
  },
  // 是否显示返回按钮
  showBack: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  }
})

// Emits
const emit = defineEmits(['back', 'rightClick', 'click'])

// Methods
const handleBack = () => {
  emit('back')
  uni.navigateBack()
}

const handleRightClick = () => {
  emit('rightClick')
}

const handleHeaderClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
.page-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx; // 44px
  padding: 0 32rpx;
  background: linear-gradient(180deg, #000000 0%, #1a1a1a 100%);
  z-index: 100;
  
  &__left,
  &__right {
    width: 120rpx;
    display: flex;
    align-items: center;
  }
  
  &__left {
    justify-content: flex-start;
  }
  
  &__right {
    justify-content: flex-end;
  }
  
  &__center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__title {
    font-size: 36rpx;
    font-weight: 600;
    color: #ffffff;
    text-align: center;
  }
  
  &__back {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64rpx;
    height: 64rpx;
    border-radius: 32rpx;
    background: rgba(255, 255, 255, 0.1);
    
    &-icon {
      font-size: 32rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
  
  &__action {
    padding: 12rpx 24rpx;
    border-radius: 20rpx;
    background: rgba(255, 255, 255, 0.1);
    
    &-text {
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 500;
    }
  }
}
</style> 