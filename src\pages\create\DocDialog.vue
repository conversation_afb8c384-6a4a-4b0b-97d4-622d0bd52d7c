<template>
  <transition name="fade-dialog">
    <view class="doc-dialog-mask">
      <view class="doc-dialog-container">
        <!-- 标题栏 -->
        <view class="doc-dialog-header">
          <text class="doc-dialog-title">增加文案</text>
          <!-- <text class="doc-dialog-close" @tap.stop="onClose">×</text> -->
        </view>
        <!-- tab切换 -->
        <view class="doc-dialog-tabs">
          <view :class="['doc-dialog-tab', { 'active': tabIndex === 0 }]" @click="tabIndex = 0">
            <text class="doc-dialog-tab-icon ai">&#10024;</text>
            <text class="doc-dialog-tab-text">AI生成</text>
          </view>
          <view :class="['doc-dialog-tab', { 'active': tabIndex === 1 }]" @click="tabIndex = 1">
            <text class="doc-dialog-tab-text">输入/提取</text>
          </view>
        </view>
        <!-- 文案类型 -->
        <view class="doc-dialog-section">
          <text class="doc-dialog-section-title">文案类型</text>
          <view class="doc-dialog-type-list-grid">
            <view v-for="(type, idx) in docTypes" :key="type" :class="['doc-dialog-type-btn', { 'active': docTypeIndex === idx }]" @click="docTypeIndex = idx">
              <text>{{ type }}</text>
            </view>
          </view>
        </view>
        <!-- 自定义要求 -->
        <view class="doc-dialog-section custom-section">
          <text class="custom-label">自定义要求</text>
          <input class="custom-input" v-model="customRequest" placeholder="请输入自定义要求（可选）" />
        </view>
        <!-- 操作按钮 -->
        <view class="doc-dialog-actions">
          <button class="doc-dialog-btn cancel" @click="onClose">取消</button>
          <button class="doc-dialog-btn primary" @click="onConfirm">生成文案</button>
        </view>
      </view>
    </view>
  </transition>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, defineExpose } from 'vue';
defineExpose({});
const props = defineProps<{ visible: boolean }>()
const emit = defineEmits(['close', 'confirm'])
const tabIndex = ref(0)
const docTypes = [
  '人设', '店设', '活动', '拿货攻略', '市场攻略', '定价攻略', '顾客种草'
]
const docTypeIndex = ref(0)
const customRequest = ref('')
function onClose() { emit('close') }
function onConfirm() {
  emit('confirm', {
    docType: docTypes[docTypeIndex.value],
    customRequest: customRequest.value
  })
}
</script>
<style scoped>
.fade-dialog-enter-active, .fade-dialog-leave-active {
  transition: all 0.25s cubic-bezier(0.4,0,0.2,1);
}
.fade-dialog-enter-from, .fade-dialog-leave-to {
  opacity: 0;
  transform: scale(0.95);
}
.fade-dialog-enter-to, .fade-dialog-leave-from {
  opacity: 1;
  transform: scale(1);
}
.doc-dialog-mask {
  position: fixed;
  left: 0; top: 0; width: 100vw; height: 100vh;
  background: rgba(0,0,0,0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.doc-dialog-container {
  background: #232325;
  border-radius: 24rpx;
  width: 600rpx;
  max-width: 92vw;
  box-sizing: border-box;
  padding: 44rpx 36rpx 36rpx 36rpx;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.18);
}
.doc-dialog-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 32rpx;
}
.doc-dialog-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 34rpx;
  font-weight: 500;
}
.doc-dialog-close {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 44rpx;
  color: #bfbfbf;
  line-height: 1;
  cursor: pointer;
  z-index: 10;
  padding: 24rpx 32rpx;
}
.doc-dialog-tabs {
  display: flex;
  align-items: center;
  margin-bottom: 36rpx;
  gap: 0;
  background: #19191b;
  border-radius: 16rpx;
  overflow: hidden;
  height: 88rpx; /* 44px = 88rpx */
}
.doc-dialog-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  height: 88rpx;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.doc-dialog-tab.active {
  background: #353537;
  color: #fff;
}
.doc-dialog-tab-icon.ai {
  color: #ff0043;
  font-size: 28rpx;
  margin-right: 10rpx;
}
.doc-dialog-tab-text {
  font-size: 28rpx;
}
.doc-dialog-section {
  margin-bottom: 28rpx;
}
.doc-dialog-section-title {
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 18rpx;
}
.doc-dialog-type-list-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx 20rpx; /* 10px = 20rpx */
  margin-top: 12rpx;
}
.doc-dialog-type-btn {
  background: #353537;
  color: #bfbfbf;
  font-size: 26rpx;
  border-radius: 12rpx;
  height: 72rpx; /* 36px = 72rpx */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, border 0.2s;
  border: 2rpx solid transparent;
  box-sizing: border-box;
}
.doc-dialog-type-btn.active {
  background: #232325;
  color: #ff0043;
  border: 2rpx solid #ff0043;
}
.custom-section {
  margin-bottom: 28rpx;
}
.custom-label-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  gap: 8rpx;
}
.custom-label {
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
}
.doc-box__arrow {
  width: 24rpx;
  height: 12rpx;
  margin-left: 4rpx;
  vertical-align: middle;
}
.doc-dialog-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 36rpx;
  gap: 24rpx;
}
.doc-dialog-btn {
  flex: 1;
  font-size: 30rpx;
  border-radius: 16rpx;
  height: 88rpx;
  padding: 0;
  border: none;
  outline: none;
  margin: 0 8rpx;
  background: #353537;
  color: #fff;
  font-weight: 500;
  transition: background 0.2s, color 0.2s;
  line-height: 88rpx;
}
.doc-dialog-btn.primary {
  background: #ff0043;
  color: #fff;
}
.doc-dialog-btn.cancel {
  background: #353537;
  color: #fff;
}
.custom-input {
  width: 100%;
  margin-top: 12rpx;
  background: #353537;
  border-radius: 8rpx;
  padding: 18rpx 24rpx;
  color: #fff;
  font-size: 24rpx;
  border: none;
  outline: none;
  min-height: 80rpx;
  /* 支持多行输入 */
  line-height: 1.6;
}
</style>
