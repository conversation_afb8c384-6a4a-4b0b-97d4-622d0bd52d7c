<template>
  <view class="preview-page">
    <!-- 顶部搜索栏 -->
    <view class="header-bar">
      <image class="header-icon" src="/src/asset/img/preview/cover.png" />
      <text class="header-title">服装展示视频</text>
      <text class="header-search">搜索</text>
    </view>
    <!-- 视频区域背景 -->
    <view class="section-bg">
      <image class="section-arrow" src="/src/asset/img/icons/section_arrow.png" @click="handleBack" />
    </view>
    <!-- 视频信息 -->
    <view class="video-info">
      <view class="author-row">
        <text class="author">@当你老了</text>
        <text class="desc">
          在深圳龙华，你只要月薪超过 1 万，你一定要来这家服装店。 但来这里，你根本不用担心价格，件件都是极致…
        </text>
      </view>
    </view>
    <!-- 点赞和剪同款 -->
    <view class="action-row">
      <view class="like-box" @click="toggleLike">
        <image class="like-icon" :class="{ liked }" src="/src/asset/img/icons/like.png" />
        <text class="like-count">{{ likeCount }}</text>
      </view>
      <view class="cut-btn">
        <image class="cut-btn-bg" src="/src/asset/img/preview/cut_btn_bg.png" mode="aspectFill" />
        <text class="cut-btn-text">剪同款</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 点赞状态和数量
const liked = ref(false)
const likeCount = ref(9999)

function toggleLike() {
  liked.value = !liked.value
  likeCount.value += liked.value ? 1 : -1
}

function handleBack() {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.preview-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #111113;
  width: 750rpx;
  overflow: hidden;
  padding: 8rpx 0 0 0;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 110rpx; // 预留底部操作栏空间
}
.header-bar {
  display: flex;
  align-items: center;
  background: rgba(255,255,255,0.2);
  border-radius: 18px;
  margin: 0 24rpx 0 58rpx;
  padding: 16rpx 32rpx 16rpx 24rpx;
  .header-icon {
    width: 28rpx;
    height: 28rpx;
    margin: 6rpx 0;
  }
  .header-title {
    color: rgba(255,255,255,0.5);
    font-size: 28rpx;
    text-align: center;
    white-space: nowrap;
    line-height: 40rpx;
    margin-left: 28rpx;
  }
  .header-search {
    color: rgba(255,255,255,0.7);
    font-size: 28rpx;
    text-align: center;
    white-space: nowrap;
    line-height: 40rpx;
    margin-left: 332rpx;
  }
}
.section-bg {
  background-image: url('../../asset/img/preview/section_bg.png');
  background-repeat: no-repeat;
  margin-top: 62rpx;
  position: relative;
  min-height: 120rpx;
  flex: 1 1 auto;
  .section-arrow {
    width: 16rpx;
    height: 30rpx;
    position: absolute;
    left: 18rpx;
    top: -112rpx;
    z-index: 2;
    cursor: pointer;
  }
}
.video-info {
  padding: 44rpx 20rpx 28rpx 24rpx;
  .author-row {
    .author {
      color: #fff;
      font-size: 34rpx;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 48rpx;
      margin-right: 540rpx;
    }
    .desc {
      width: 706rpx;
      color: #fff;
      font-size: 28rpx;
      text-align: left;
      line-height: 40rpx;
      margin-top: 14rpx;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: normal;
    }
  }
}
.action-row {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #141414;
  padding: 10rpx 24rpx 18rpx 32rpx;
  box-sizing: border-box;
  width: 100%;
  .like-box {
    display: flex;
    align-items: center;
    padding: 14rpx 28rpx;
    cursor: pointer;
    .like-icon {
      width: 28rpx;
      height: 26rpx;
      margin: 0 16rpx 0 14rpx;
      transition: filter 0.2s;
    }
    .like-icon.liked {
      filter: grayscale(0) brightness(1.2) drop-shadow(0 0 2px #ff2d55);
    }
    .like-count {
      color: #fff;
      font-size: 24rpx;
      line-height: 28rpx;
      margin-top: 4rpx;
    }
  }
  .cut-btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 240rpx;
    border-radius: 24rpx;
    overflow: hidden;
    flex: 1;
    margin-right: 0;
    .cut-btn-bg {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 0;
    }
    .cut-btn-text {
      position: relative;
      z-index: 1;
      color: #fff;
      font-size: 32rpx;
      font-weight: 500;
      text-align: center;
      white-space: nowrap;
      line-height: 44rpx;
      padding: 22rpx 0;
      width: 100%;
      display: block;
    }
  }
}
</style>
