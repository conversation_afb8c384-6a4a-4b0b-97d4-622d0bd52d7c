import { mount } from '@vue/test-utils'
import { describe, it, expect, beforeEach } from 'vitest'
import DownloadProgress from '@/components/business/DownloadProgress/index.vue'

describe('DownloadProgress', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(DownloadProgress, {
      props: {
        visible: true,
        progress: 50,
        fileName: 'test.mp4',
        status: 'downloading'
      }
    })
  })

  it('should render correctly when visible', () => {
    expect(wrapper.find('.download-progress').exists()).toBe(true)
    expect(wrapper.find('.download-progress__title').text()).toBe('下载中')
    expect(wrapper.find('.download-progress__filename-text').text()).toBe('test.mp4')
    expect(wrapper.find('.download-progress__progress-text').text()).toBe('50%')
  })

  it('should not render when not visible', async () => {
    await wrapper.setProps({ visible: false })
    expect(wrapper.find('.download-progress').exists()).toBe(false)
  })

  it('should display correct progress percentage', async () => {
    await wrapper.setProps({ progress: 75 })
    expect(wrapper.find('.download-progress__progress-text').text()).toBe('75%')
  })

  it('should display correct status text for downloading', () => {
    expect(wrapper.find('.download-progress__status-text').text()).toBe('正在下载...')
  })

  it('should display correct status text for completed', async () => {
    await wrapper.setProps({ status: 'completed' })
    expect(wrapper.find('.download-progress__status-text').text()).toBe('下载完成')
  })

  it('should display correct status text for failed', async () => {
    await wrapper.setProps({ status: 'failed' })
    expect(wrapper.find('.download-progress__status-text').text()).toBe('下载失败')
  })

  it('should display correct status text for cancelled', async () => {
    await wrapper.setProps({ status: 'cancelled' })
    expect(wrapper.find('.download-progress__status-text').text()).toBe('下载已取消')
  })

  it('should emit cancel event when cancel button is clicked', async () => {
    await wrapper.find('.download-progress__cancel-btn').trigger('click')
    expect(wrapper.emitted('cancel')).toBeTruthy()
  })

  it('should emit cancel event when close button is clicked', async () => {
    await wrapper.find('.download-progress__close').trigger('click')
    expect(wrapper.emitted('cancel')).toBeTruthy()
  })

  it('should have correct progress bar width', async () => {
    await wrapper.setProps({ progress: 30 })
    const progressFill = wrapper.find('.download-progress__progress-fill')
    expect(progressFill.attributes('style')).toContain('width: 30%')
  })
}) 