# 创作历史页面

## 功能概述

创作历史页面展示用户的所有创作记录，包括AI穿版、AI混剪等不同类型的创作内容。

## 页面结构

### 1. 页面头部 (PageHeader)
- **标题**: "创作历史"
- **右侧操作**: "管理" 按钮
- **点击标题**: 可切换空状态和有内容状态（演示功能）

### 2. 标签栏 (history-tabs)
- **全部**: 显示所有创作记录
- **AI穿版**: 只显示AI穿版相关内容
- **AI混剪**: 只显示AI混剪相关内容
- **激活状态**: 红色下划线指示器

### 3. 内容区域 (history-content)

#### 空状态 (empty-state)
- **图标**: 视频播放图标，带有渐变色背景
- **提示文字**: "你还没有任何创作哦"
- **操作按钮**: "去创作" 按钮，点击跳转到创作页面

#### 历史记录列表 (history-list)
- **日期分组**: 按创建日期分组显示
- **三列布局**: 每行显示3个创作项目
- **项目状态**:
  - 已完成: 显示"已生成"标签
  - 生成中: 显示进度和预计时间
  - 视频: 显示播放按钮和时长
  - 图片: 显示图片数量

### 4. 底部导航栏 (BottomTabBar)
- **当前页面**: 创作历史（索引3）
- **样式**: 黑色半透明背景，红色激活状态

## 组件特性

### 响应式设计
- 使用rpx单位确保跨设备兼容
- 三列网格布局自适应屏幕宽度
- 合理的间距和圆角设计

### 交互效果
- 标签切换动画
- 按钮点击反馈
- 图片加载占位

### 状态管理
- 空状态/有内容状态切换
- 标签激活状态管理
- 项目状态显示（生成中/已完成）

## 使用方式

```vue
<template>
  <view>
    <!-- 直接使用页面组件 -->
    <HistoryPage />
  </view>
</template>

<script setup>
import HistoryPage from '@/pages/history/index.vue'
</script>
```

## 样式特点

### 色彩方案
- **背景**: 黑色到深灰色渐变
- **主色调**: 红色 (#ff2d55)
- **文字**: 白色及其透明度变化
- **卡片**: 深灰色背景，圆角设计

### 字体规范
- **标题**: 36rpx, 600字重
- **标签**: 32rpx, 普通/600字重
- **内容**: 24-28rpx, 不同透明度

### 间距规范
- **页面边距**: 32rpx
- **元素间距**: 20-40rpx
- **内边距**: 12-24rpx

## 数据结构

### 历史记录项目
```javascript
{
  id: 1,
  title: 'AI穿版-视频',
  type: 'video', // 'video' | 'image'
  thumbnail: 'image-url',
  duration: '00:12', // 视频时长
  count: '4', // 图片数量
  status: 'completed', // 'completed' | 'generating'
  generatingText: '图片生成中...', // 生成中提示
  estimatedTime: '预计还需10分钟' // 预计时间
}
```

### 历史记录分组
```javascript
{
  date: '2025-05-25 16:00',
  items: [/* 项目数组 */]
}
```

## 扩展功能

### 可添加的功能
1. **下拉刷新**: 刷新历史记录
2. **上拉加载**: 分页加载更多数据
3. **搜索功能**: 搜索特定创作内容
4. **批量操作**: 批量删除、分享等
5. **筛选功能**: 按时间、类型筛选
6. **排序功能**: 按时间、名称排序

### 性能优化
1. **虚拟滚动**: 处理大量数据
2. **图片懒加载**: 优化加载性能
3. **缓存策略**: 缓存历史记录数据
4. **预加载**: 预加载下一页数据

## 注意事项

1. **图片资源**: 确保占位图片路径正确
2. **路由配置**: 确保页面路由已正确配置
3. **权限处理**: 处理用户登录状态
4. **错误处理**: 处理网络错误和数据异常
5. **兼容性**: 确保在不同平台正常显示 